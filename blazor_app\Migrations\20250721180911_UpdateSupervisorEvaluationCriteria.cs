﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EmployeeRatingSystem.Blazor.Migrations
{
    /// <inheritdoc />
    public partial class UpdateSupervisorEvaluationCriteria : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "WorkQualityScore",
                table: "SupervisorEvaluations",
                newName: "WorkDevelopmentScore");

            migrationBuilder.RenameColumn(
                name: "TeamworkScore",
                table: "SupervisorEvaluations",
                newName: "TeamworkParticipationScore");

            migrationBuilder.RenameColumn(
                name: "ProductivityScore",
                table: "SupervisorEvaluations",
                newName: "ResponsibilityAndPressureScore");

            migrationBuilder.RenameColumn(
                name: "InitiativeScore",
                table: "SupervisorEvaluations",
                newName: "RelationshipWithSuperiorsScore");

            migrationBuilder.RenameColumn(
                name: "CommunicationScore",
                table: "SupervisorEvaluations",
                newName: "PlanningAndInnovationScore");

            migrationBuilder.AddColumn<decimal>(
                name: "DisciplineAndCommitmentScore",
                table: "SupervisorEvaluations",
                type: "TEXT",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "EfficiencyAndQualityScore",
                table: "SupervisorEvaluations",
                type: "TEXT",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "EmergencyHandlingScore",
                table: "SupervisorEvaluations",
                type: "TEXT",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "GeneralBehaviorScore",
                table: "SupervisorEvaluations",
                type: "TEXT",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "LeadershipAbilityScore",
                table: "SupervisorEvaluations",
                type: "TEXT",
                nullable: false,
                defaultValue: 0m);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DisciplineAndCommitmentScore",
                table: "SupervisorEvaluations");

            migrationBuilder.DropColumn(
                name: "EfficiencyAndQualityScore",
                table: "SupervisorEvaluations");

            migrationBuilder.DropColumn(
                name: "EmergencyHandlingScore",
                table: "SupervisorEvaluations");

            migrationBuilder.DropColumn(
                name: "GeneralBehaviorScore",
                table: "SupervisorEvaluations");

            migrationBuilder.DropColumn(
                name: "LeadershipAbilityScore",
                table: "SupervisorEvaluations");

            migrationBuilder.RenameColumn(
                name: "WorkDevelopmentScore",
                table: "SupervisorEvaluations",
                newName: "WorkQualityScore");

            migrationBuilder.RenameColumn(
                name: "TeamworkParticipationScore",
                table: "SupervisorEvaluations",
                newName: "TeamworkScore");

            migrationBuilder.RenameColumn(
                name: "ResponsibilityAndPressureScore",
                table: "SupervisorEvaluations",
                newName: "ProductivityScore");

            migrationBuilder.RenameColumn(
                name: "RelationshipWithSuperiorsScore",
                table: "SupervisorEvaluations",
                newName: "InitiativeScore");

            migrationBuilder.RenameColumn(
                name: "PlanningAndInnovationScore",
                table: "SupervisorEvaluations",
                newName: "CommunicationScore");
        }
    }
}
