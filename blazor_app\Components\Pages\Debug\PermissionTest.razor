@page "/debug/permission-test"
@rendermode InteractiveServer
@using EmployeeRatingSystem.Blazor.Data
@using EmployeeRatingSystem.Blazor.Models
@using Microsoft.EntityFrameworkCore
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.Components.Authorization
@inject ApplicationDbContext DbContext
@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IJSRuntime JSRuntime

<PageTitle>Permission Test</PageTitle>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        Role-Based Permission Test Results
                    </h4>
                </div>
                <div class="card-body">
                    @if (isLoading)
                    {
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Testing permissions...</p>
                        </div>
                    }
                    else
                    {
                        <!-- Current User Info -->
                        <div class="alert alert-info mb-4">
                            <h5><i class="fas fa-user me-2"></i>Current User</h5>
                            @if (currentUser != null)
                            {
                                <p><strong>Employee ID:</strong> @currentUser.EmployeeId</p>
                                <p><strong>Name:</strong> @currentUser.EnglishName</p>
                                <p><strong>Role:</strong> <span class="badge @GetRoleBadgeClass(currentUser.Role)">@GetRoleDisplayName(currentUser.Role)</span></p>
                                <p><strong>Department:</strong> @(currentUser.PrimaryDepartment?.NameEn ?? "Not Assigned")</p>
                            }
                            else
                            {
                                <p class="text-danger">Not authenticated</p>
                            }
                        </div>

                        <!-- Permission Test Results -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h3>@canViewCount</h3>
                                        <p class="mb-0">Can View</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-warning text-dark">
                                    <div class="card-body text-center">
                                        <h3>@canEditCount</h3>
                                        <p class="mb-0">Can Edit</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-danger text-white">
                                    <div class="card-body text-center">
                                        <h3>@canDeleteCount</h3>
                                        <p class="mb-0">Can Delete</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Detailed Permission Matrix -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Evaluation ID</th>
                                        <th>Employee</th>
                                        <th>Department</th>
                                        <th>Type</th>
                                        <th>Status</th>
                                        <th>Evaluator</th>
                                        <th>Can View</th>
                                        <th>Can Edit</th>
                                        <th>Can Delete</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var test in permissionTests)
                                    {
                                        <tr>
                                            <td><strong>@test.Evaluation.Id</strong></td>
                                            <td>@test.Evaluation.Employee?.EnglishName</td>
                                            <td>@test.Evaluation.Employee?.PrimaryDepartment?.NameEn</td>
                                            <td>
                                                @if (test.Evaluation.Id >= 10000)
                                                {
                                                    <span class="badge bg-info">Comprehensive</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-primary">Traditional</span>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge @GetStatusBadgeClass(test.Evaluation.Status)">
                                                    @test.Evaluation.Status
                                                </span>
                                            </td>
                                            <td>@(test.Evaluation.Evaluator?.EnglishName ?? "Unknown")</td>
                                            <td>
                                                @if (test.CanView)
                                                {
                                                    <span class="badge bg-success">✓ Yes</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">✗ No</span>
                                                }
                                            </td>
                                            <td>
                                                @if (test.CanEdit)
                                                {
                                                    <span class="badge bg-success">✓ Yes</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">✗ No</span>
                                                }
                                            </td>
                                            <td>
                                                @if (test.CanDelete)
                                                {
                                                    <span class="badge bg-success">✓ Yes</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">✗ No</span>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Permission Rules Summary -->
                        <div class="alert alert-light mt-4">
                            <h5><i class="fas fa-info-circle me-2"></i>Permission Rules (Based on PRD)</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>View Permissions:</h6>
                                    <ul>
                                        <li><strong>Super Admin:</strong> All evaluations</li>
                                        <li><strong>Excellence Team:</strong> All evaluations (read-only)</li>
                                        <li><strong>Manager:</strong> Department tree evaluations</li>
                                        <li><strong>Supervisor:</strong> Direct reports + own evaluations</li>
                                        <li><strong>Employee:</strong> Own evaluations only</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>Edit/Delete Permissions:</h6>
                                    <ul>
                                        <li><strong>Super Admin:</strong> All evaluations</li>
                                        <li><strong>Excellence Team:</strong> Delete all (Edit: read-only)</li>
                                        <li><strong>Manager:</strong> Department evaluations</li>
                                        <li><strong>Supervisor:</strong> Own evaluations only</li>
                                        <li><strong>Employee:</strong> No edit/delete access</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Test Actions -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <button class="btn btn-primary me-2" @onclick="RefreshTest">
                                    <i class="fas fa-sync-alt me-1"></i>
                                    Refresh Test
                                </button>
                                <button class="btn btn-info" @onclick="ShowPermissionDetails">
                                    <i class="fas fa-eye me-1"></i>
                                    Show Permission Details
                                </button>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private bool isLoading = true;
    private ApplicationUser? currentUser = null;
    private bool isAuthenticated = false;
    private int canViewCount = 0;
    private int canEditCount = 0;
    private int canDeleteCount = 0;

    private List<PermissionTestResult> permissionTests = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadCurrentUser();
        await RunPermissionTests();
    }

    private async Task RefreshTest()
    {
        isLoading = true;
        StateHasChanged();
        await LoadCurrentUser();
        await RunPermissionTests();
    }

    private async Task LoadCurrentUser()
    {
        try
        {
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            isAuthenticated = authState.User.Identity?.IsAuthenticated ?? false;

            if (isAuthenticated)
            {
                var userIdClaim = authState.User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier);
                if (userIdClaim != null)
                {
                    currentUser = await UserManager.Users
                        .Include(u => u.PrimaryDepartment)
                        .FirstOrDefaultAsync(u => u.Id == userIdClaim.Value);
                }
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading current user: {ex.Message}");
        }
    }

    private async Task RunPermissionTests()
    {
        try
        {
            // Load all evaluations for testing
            var evaluations = await LoadEvaluationsForTesting();

            permissionTests.Clear();
            canViewCount = 0;
            canEditCount = 0;
            canDeleteCount = 0;

            foreach (var evaluation in evaluations)
            {
                var canView = CanViewEvaluation(evaluation);
                var canEdit = CanEditEvaluation(evaluation);
                var canDelete = CanDeleteEvaluation(evaluation);

                if (canView) canViewCount++;
                if (canEdit) canEditCount++;
                if (canDelete) canDeleteCount++;

                permissionTests.Add(new PermissionTestResult
                {
                    Evaluation = evaluation,
                    CanView = canView,
                    CanEdit = canEdit,
                    CanDelete = canDelete
                });
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error running permission tests: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task<List<Evaluation>> LoadEvaluationsForTesting()
    {
        var evaluations = new List<Evaluation>();

        // Load traditional evaluations
        var traditionalEvaluations = await DbContext.Evaluations
            .Include(e => e.Employee)
                .ThenInclude(e => e.PrimaryDepartment)
            .Include(e => e.Evaluator)
            .Where(e => !e.IsDeleted)
            .ToListAsync();

        evaluations.AddRange(traditionalEvaluations);

        // Load comprehensive evaluations and convert them
        var comprehensiveEvaluations = await DbContext.ComprehensiveEvaluations
            .Include(e => e.Employee)
                .ThenInclude(e => e.PrimaryDepartment)
            .ToListAsync();

        foreach (var compEval in comprehensiveEvaluations)
        {
            var convertedEval = new Evaluation
            {
                Id = compEval.Id + 10000, // Offset for identification
                EmployeeId = compEval.EmployeeId,
                Employee = compEval.Employee,
                EvaluatorId = "system",
                TotalScore = compEval.TotalScore,
                Status = compEval.Status,
                CreatedAt = compEval.CalculatedAt,
                EvaluationPeriodStart = DateTime.Parse(compEval.EvaluationPeriod + "-01"),
                EvaluationPeriodEnd = DateTime.Parse(compEval.EvaluationPeriod + "-01").AddMonths(1).AddDays(-1),
                OverallCommentsEn = "Comprehensive Evaluation"
            };
            evaluations.Add(convertedEval);
        }

        return evaluations;
    }

    // Copy the authorization methods from the main evaluations page
    private bool CanViewEvaluation(Evaluation evaluation)
    {
        if (!isAuthenticated || currentUser == null) return false;

        return currentUser.Role switch
        {
            UserRole.SUPER_ADMIN => true,
            UserRole.EXCELLENCE_TEAM => true,
            UserRole.MANAGER => currentUser.PrimaryDepartmentId.HasValue && evaluation.Employee?.PrimaryDepartmentId.HasValue == true &&
                               currentUser.PrimaryDepartmentId == evaluation.Employee.PrimaryDepartmentId,
            UserRole.SUPERVISOR => evaluation.EvaluatorId == currentUser.Id || evaluation.EmployeeId == currentUser.EmployeeId,
            UserRole.EMPLOYEE => evaluation.EmployeeId == currentUser.EmployeeId,
            _ => false
        };
    }

    private bool CanEditEvaluation(Evaluation evaluation)
    {
        if (!isAuthenticated || currentUser == null) return false;
        if (evaluation.Id >= 10000 || evaluation.OverallCommentsEn == "Comprehensive Evaluation") return false;
        if (evaluation.Status != EvaluationStatus.DRAFT) return false;

        return currentUser.Role switch
        {
            UserRole.SUPER_ADMIN => true,
            UserRole.EXCELLENCE_TEAM => true, // Full edit access equivalent to SUPER_ADMIN
            UserRole.MANAGER => currentUser.PrimaryDepartmentId.HasValue && evaluation.Employee?.PrimaryDepartmentId.HasValue == true &&
                               currentUser.PrimaryDepartmentId == evaluation.Employee.PrimaryDepartmentId,
            UserRole.SUPERVISOR => evaluation.EvaluatorId == currentUser.Id,
            UserRole.EMPLOYEE => false,
            _ => false
        };
    }

    private bool CanDeleteEvaluation(Evaluation evaluation)
    {
        if (!isAuthenticated || currentUser == null) return false;

        return currentUser.Role switch
        {
            UserRole.SUPER_ADMIN => true,
            UserRole.EXCELLENCE_TEAM => true, // Full delete access per user request
            UserRole.MANAGER => currentUser.PrimaryDepartmentId.HasValue && evaluation.Employee?.PrimaryDepartmentId.HasValue == true &&
                               currentUser.PrimaryDepartmentId == evaluation.Employee.PrimaryDepartmentId,
            UserRole.SUPERVISOR => evaluation.EvaluatorId == currentUser.Id,
            UserRole.EMPLOYEE => false,
            _ => false
        };
    }

    private async Task ShowPermissionDetails()
    {
        var details = $"Permission Test Results for {currentUser?.EnglishName} ({GetRoleDisplayName(currentUser?.Role ?? UserRole.EMPLOYEE)}):\n\n" +
                     $"Total Evaluations: {permissionTests.Count}\n" +
                     $"Can View: {canViewCount}\n" +
                     $"Can Edit: {canEditCount}\n" +
                     $"Can Delete: {canDeleteCount}\n\n" +
                     $"This test verifies that the role-based permissions are working according to the PRD specifications.";

        await JSRuntime.InvokeVoidAsync("alert", details);
    }

    private string GetRoleBadgeClass(UserRole role)
    {
        return role switch
        {
            UserRole.SUPER_ADMIN => "bg-danger",
            UserRole.MANAGER => "bg-primary",
            UserRole.SUPERVISOR => "bg-info",
            UserRole.EXCELLENCE_TEAM => "bg-warning",
            UserRole.EMPLOYEE => "bg-secondary",
            _ => "bg-light text-dark"
        };
    }

    private string GetRoleDisplayName(UserRole role)
    {
        return role switch
        {
            UserRole.SUPER_ADMIN => "Super Admin",
            UserRole.MANAGER => "Manager",
            UserRole.SUPERVISOR => "Supervisor",
            UserRole.EXCELLENCE_TEAM => "Excellence Team",
            UserRole.EMPLOYEE => "Employee",
            _ => role.ToString()
        };
    }

    private string GetStatusBadgeClass(EvaluationStatus status)
    {
        return status switch
        {
            EvaluationStatus.DRAFT => "bg-secondary",
            EvaluationStatus.SUBMITTED => "bg-primary",
            EvaluationStatus.APPROVED => "bg-success",
            EvaluationStatus.REJECTED => "bg-danger",
            _ => "bg-light text-dark"
        };
    }

    public class PermissionTestResult
    {
        public Evaluation Evaluation { get; set; } = new();
        public bool CanView { get; set; }
        public bool CanEdit { get; set; }
        public bool CanDelete { get; set; }
    }
}
