using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EmployeeRatingSystem.Blazor.Models
{
    /// <summary>
    /// Represents work volume data for an employee in a specific month
    /// </summary>
    public class WorkVolumeData : TimeStampedModel
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// Employee ID
        /// </summary>
        [Required]
        public string EmployeeId { get; set; } = string.Empty;

        /// <summary>
        /// Reference to the employee
        /// </summary>
        [ForeignKey(nameof(EmployeeId))]
        public virtual ApplicationUser Employee { get; set; } = null!;

        /// <summary>
        /// Department ID
        /// </summary>
        [Required]
        public int DepartmentId { get; set; }

        /// <summary>
        /// Reference to the department
        /// </summary>
        [ForeignKey(nameof(DepartmentId))]
        public virtual Department Department { get; set; } = null!;

        /// <summary>
        /// Evaluation period (Year-Month format: 2025-01)
        /// </summary>
        [Required]
        [StringLength(7)]
        public string EvaluationPeriod { get; set; } = string.Empty;

        /// <summary>
        /// Work volume in Quality Program
        /// حجم عمل الموظف في برنامج الجودة
        /// </summary>
        [Column(TypeName = "decimal(10,2)")]
        public decimal QualityProgramWork { get; set; }

        /// <summary>
        /// Work volume in Oracle system
        /// حجم عمل الموظف في الاوراكل
        /// </summary>
        [Column(TypeName = "decimal(10,2)")]
        public decimal OracleWork { get; set; }

        /// <summary>
        /// Work volume in documented work
        /// حجم عمل الموظف في اي عمل موثق
        /// </summary>
        [Column(TypeName = "decimal(10,2)")]
        public decimal DocumentedWork { get; set; }

        /// <summary>
        /// Total work volume for the employee
        /// </summary>
        [NotMapped]
        public decimal TotalEmployeeWork => QualityProgramWork + OracleWork + DocumentedWork;

        /// <summary>
        /// Comments or notes about the work volume
        /// </summary>
        [StringLength(500)]
        public string? Comments { get; set; }

        /// <summary>
        /// Who recorded this data
        /// </summary>
        [Required]
        public string RecordedBy { get; set; } = string.Empty;

        /// <summary>
        /// When this data was recorded
        /// </summary>
        public DateTime RecordedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Represents department work volume totals for a specific month
    /// </summary>
    public class DepartmentWorkVolumeTotal : TimeStampedModel
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// Department ID
        /// </summary>
        [Required]
        public int DepartmentId { get; set; }

        /// <summary>
        /// Reference to the department
        /// </summary>
        [ForeignKey(nameof(DepartmentId))]
        public virtual Department Department { get; set; } = null!;

        /// <summary>
        /// Evaluation period (Year-Month format: 2025-01)
        /// </summary>
        [Required]
        [StringLength(7)]
        public string EvaluationPeriod { get; set; } = string.Empty;

        /// <summary>
        /// Total department work in Quality Program
        /// حجم عمل القسم في برنامج الجودة
        /// </summary>
        [Column(TypeName = "decimal(10,2)")]
        public decimal TotalQualityProgramWork { get; set; }

        /// <summary>
        /// Total department work in Oracle system
        /// حجم عمل القسم في الاوراكل
        /// </summary>
        [Column(TypeName = "decimal(10,2)")]
        public decimal TotalOracleWork { get; set; }

        /// <summary>
        /// Total department work in documented work
        /// حجم عمل القسم موثق
        /// </summary>
        [Column(TypeName = "decimal(10,2)")]
        public decimal TotalDocumentedWork { get; set; }

        /// <summary>
        /// Total department work volume
        /// </summary>
        [NotMapped]
        public decimal TotalDepartmentWork => TotalQualityProgramWork + TotalOracleWork + TotalDocumentedWork;

        /// <summary>
        /// Who recorded this data
        /// </summary>
        [Required]
        public string RecordedBy { get; set; } = string.Empty;

        /// <summary>
        /// When this data was recorded
        /// </summary>
        public DateTime RecordedAt { get; set; } = DateTime.UtcNow;
    }
}
