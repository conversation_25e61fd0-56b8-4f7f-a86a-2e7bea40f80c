/* Clean layout for modern Bootstrap navbar design */
.page[b-zzt0ru0its] {
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

main[b-zzt0ru0its] {
    flex: 1;
}

#blazor-error-ui[b-zzt0ru0its] {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

    #blazor-error-ui .dismiss[b-zzt0ru0its] {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
    }
