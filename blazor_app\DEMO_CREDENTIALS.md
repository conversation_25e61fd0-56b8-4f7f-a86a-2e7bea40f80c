# Demo Credentials - Employee Rating System (Blazor)

## 🎯 Overview
This document provides demo credentials for testing all user roles and functionality in the Employee Rating System Blazor application.

## 🔐 Demo User Accounts

All demo accounts use the password: **`Password123!`**

### 1. Super Admin Account
- **Email**: `<EMAIL>`
- **Password**: `Password123!`
- **Role**: SUPER_ADMIN
- **Employee ID**: `EMP001`
- **English Name**: `System Administrator`
- **Arabic Name**: `مدير النظام`

**Access Level**: Complete system access
- ✅ Full user management (create, edit, delete, assign roles)
- ✅ Department hierarchy management (unlimited levels)
- ✅ Evaluation criteria configuration
- ✅ Cross-department access and reporting
- ✅ System configuration and settings
- ✅ All administrative functions

### 2. Manager Account
- **Email**: `<EMAIL>`
- **Password**: `Password123!`
- **Role**: MANAGER
- **Employee ID**: `EMP002`
- **English Name**: `Department Manager`
- **Arabic Name**: `مدير القسم`

**Access Level**: Department hierarchy management
- ✅ Access to assigned department tree (all sub-departments)
- ✅ Manage supervisors within department hierarchy
- ✅ Evaluate direct report supervisors
- ✅ Department-wide reporting and analytics
- ✅ Assign supervisors to sub-departments
- ❌ Cannot access other departments outside hierarchy

### 3. Supervisor Account
- **Email**: `<EMAIL>`
- **Password**: `Password123!`
- **Role**: SUPERVISOR
- **Employee ID**: `EMP003`
- **English Name**: `Team Supervisor`
- **Arabic Name**: `مشرف الفريق`

**Access Level**: Direct reports management
- ✅ View and evaluate direct report employees only
- ✅ Team-specific reports and analytics
- ✅ Submit evaluations for approval workflow
- ✅ Track team performance metrics
- ❌ Cannot see employees from other supervisors
- ❌ Cannot access other departments

### 4. Quality Team Account
- **Email**: `<EMAIL>`
- **Password**: `Password123!`
- **Role**: QUALITY_TEAM
- **Employee ID**: `EMP004`
- **English Name**: `Quality Assurance`
- **Arabic Name**: `ضمان الجودة`

**Access Level**: Cross-departmental quality monitoring (read-only)
- ✅ View evaluation quality metrics across all departments
- ✅ Access audit trails for evaluation processes
- ✅ Generate quality compliance reports
- ✅ Monitor evaluation completion rates
- ❌ Cannot modify evaluations or user data
- ❌ Read-only access throughout system

### 5. Employee Account
- **Email**: `<EMAIL>`
- **Password**: `Password123!`
- **Role**: EMPLOYEE
- **Employee ID**: `EMP005`
- **English Name**: `Regular Employee`
- **Arabic Name**: `موظف عادي`

**Access Level**: Personal performance data only
- ✅ View personal evaluation history
- ✅ Track individual performance trends
- ✅ Access development recommendations
- ✅ View recognition and achievements
- ❌ Cannot access other employees' data
- ❌ Cannot perform evaluations

## 🏢 Demo Department Structure

The system includes a pre-configured department hierarchy for testing:

```
Company (الشركة)
├── Sales Division (قسم المبيعات)
│   ├── Regional Sales (المبيعات الإقليمية)
│   │   ├── North Region Team (فريق المنطقة الشمالية)
│   │   └── South Region Team (فريق المنطقة الجنوبية)
│   └── Corporate Sales (مبيعات الشركات)
├── Operations Division (قسم العمليات)
│   ├── Quality Assurance (ضمان الجودة)
│   └── Production (الإنتاج)
│       ├── Manufacturing (التصنيع)
│       └── Assembly (التجميع)
└── Support Division (قسم الدعم)
    ├── IT Department (قسم تقنية المعلومات)
    └── HR Department (قسم الموارد البشرية)
```

## 📊 Demo Evaluation Categories

The system includes pre-configured evaluation categories:

### 1. Attendance & Punctuality (الحضور والانصراف) - 25%
- **Mandatory**: Yes (cannot be removed)
- **Questions**: 4 questions covering attendance metrics
- **Sub-criteria**:
  - Number of attendance days (40%)
  - Total hours worked (30%)
  - Punctuality rate (20%)
  - Unplanned absence frequency (10%)

### 2. Work Volume (حجم العمل) - 35%
- **Mandatory**: No
- **Questions**: 3 questions covering productivity
- **Sub-criteria**:
  - Work quality volume (50%)
  - Monthly work volume (30%)
  - Task completion rate (20%)

### 3. Creative Work (العمل الإبداعي) - 20%
- **Mandatory**: No
- **Questions**: 4 questions covering innovation
- **Sub-criteria**:
  - Innovation and new ideas (30%)
  - Process improvements (25%)
  - Problem-solving approach (25%)
  - Initiative taken (20%)

### 4. Direct Supervisor Evaluation (تقييم المسؤول المباشر) - 20%
- **Mandatory**: No
- **Questions**: 3 questions covering interpersonal skills
- **Sub-criteria**:
  - Communication style (50%)
  - Collaboration (30%)
  - Professional behavior (20%)

## 🧪 Testing Scenarios

### Scenario 1: Super Admin Testing
1. Login with `<EMAIL>`
2. Navigate to Users → Create new user with different roles
3. Navigate to Departments → Create nested department structure
4. Navigate to Evaluations → Configure evaluation categories and questions
5. Test bulk evaluation workflow with multiple employees
6. Verify cross-department access and reporting

### Scenario 2: Manager Testing
1. Login with `<EMAIL>`
2. Verify access to assigned department hierarchy only
3. Test supervisor management within department
4. Create evaluations for direct report supervisors
5. Generate department-wide reports
6. Verify restricted access to other departments

### Scenario 3: Supervisor Testing
1. Login with `<EMAIL>`
2. Verify access to direct reports only
3. Create individual evaluations for team members
4. Test bulk evaluation for team
5. Submit evaluations for approval
6. Verify restricted access to other teams

### Scenario 4: Quality Team Testing
1. Login with `<EMAIL>`
2. Verify read-only access across all departments
3. Generate quality compliance reports
4. Monitor evaluation completion rates
5. Access audit trails
6. Verify inability to modify data

### Scenario 5: Employee Testing
1. Login with `<EMAIL>`
2. View personal evaluation history
3. Track performance trends
4. Access development recommendations
5. Verify restricted access to other employee data

## 🌐 Language Testing

### Arabic (RTL) Testing
1. Click the "ع" button in navigation to switch to Arabic
2. Verify RTL layout throughout the application
3. Test form inputs and table layouts in RTL
4. Verify Arabic text rendering and font support
5. Test navigation and menu functionality in RTL

### English (LTR) Testing
1. Click the "E" button in navigation to switch to English
2. Verify LTR layout restoration
3. Test all functionality in English interface
4. Verify language preference persistence
5. Test bilingual data display

## 🔧 Quick Start Testing Guide

### 1. Initial Setup
```bash
cd blazor_app
dotnet run
```

### 2. Access Application
- Open browser to: `http://localhost:5222`
- Application should load with login page

### 3. Test Authentication
- Try logging in with each demo account
- Verify role-based access restrictions
- Test logout functionality

### 4. Test Core Features
- Department management (Super Admin)
- User management (Super Admin/Manager)
- Evaluation creation (Manager/Supervisor)
- Bulk evaluations (Manager/Supervisor)
- Dashboard and reporting (All roles)

### 5. Test Localization
- Switch between Arabic and English
- Verify RTL/LTR layout changes
- Test bilingual content display

## 📋 Expected Behavior

### Login Success Indicators
- Successful login redirects to Dashboard
- Navigation shows role-appropriate menu items
- User name displays in top-right corner
- Language toggle (E/ع) available in navigation

### Role-Based Access Verification
- **Super Admin**: All menu items visible, full access
- **Manager**: Department and user management visible, restricted scope
- **Supervisor**: Evaluation and team management only
- **Quality Team**: Read-only access with reporting focus
- **Employee**: Personal dashboard and performance history only

### Functionality Verification
- Forms validate properly with error messages
- Real-time calculations work in evaluations
- Search and filtering function correctly
- Data saves and loads properly
- Language switching works instantly

## 🚨 Troubleshooting

### Common Issues
1. **Login Fails**: Verify password is exactly `Password123!`
2. **Database Error**: Run `dotnet ef database update`
3. **Missing Data**: Check if data seeding completed successfully
4. **Language Issues**: Clear browser cache and cookies
5. **Access Denied**: Verify user role and department assignments

### Reset Demo Data
```bash
# Delete database and recreate
rm employee_rating.db
dotnet ef database update
```

## 📞 Support

For issues with demo credentials or testing:
1. Check application logs for error details
2. Verify database connection and migrations
3. Ensure all required services are running
4. Review browser console for JavaScript errors

---

**Note**: These demo credentials are for testing purposes only. In production, use strong, unique passwords and proper user management procedures.
