@page "/debug/quick-user-test"
@using Microsoft.AspNetCore.Identity
@using Microsoft.EntityFrameworkCore
@using System.ComponentModel.DataAnnotations
@using EmployeeRatingSystem.Blazor.Data
@using EmployeeRatingSystem.Blazor.Models
@using Microsoft.AspNetCore.Components.Authorization
@inject ApplicationDbContext DbContext
@inject UserManager<ApplicationUser> UserManager
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider

<PageTitle>Quick User Creation Test</PageTitle>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-rocket me-2"></i>
                        Quick User Creation Test
                    </h4>
                </div>
                <div class="card-body">
                    
                    <!-- Current User Info -->
                    <div class="alert alert-info">
                        <h5><i class="fas fa-user me-2"></i>Current User</h5>
                        @if (currentUser != null)
                        {
                            <p><strong>Name:</strong> @currentUser.EnglishName (@currentUser.ArabicName)</p>
                            <p><strong>Role:</strong> @GetRoleDisplayName(currentUser.Role)</p>
                            <p><strong>Can Create Users:</strong> 
                                @if (CanCreateUsers())
                                {
                                    <span class="badge bg-success">✓ Yes</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger">✗ No</span>
                                }
                            </p>
                        }
                        else
                        {
                            <p class="text-warning">No current user found</p>
                        }
                    </div>

                    <!-- Quick Test Form -->
                    @if (CanCreateUsers())
                    {
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-plus me-2"></i>Create Test User</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label class="form-label">Employee ID</label>
                                        <input @bind="testEmployeeId" class="form-control" placeholder="@GetUniqueEmployeeId()" />
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Email</label>
                                        <input @bind="testEmail" class="form-control" type="email" placeholder="@GetUniqueEmail()" />
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">English Name</label>
                                        <input @bind="testEnglishName" class="form-control" placeholder="Test User" />
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Arabic Name</label>
                                        <input @bind="testArabicName" class="form-control" placeholder="مستخدم تجريبي" />
                                    </div>
                                </div>
                                
                                <div class="mt-3">
                                    <button class="btn btn-primary me-2" @onclick="CreateTestUser" disabled="@isCreating">
                                        @if (isCreating)
                                        {
                                            <span class="spinner-border spinner-border-sm me-2"></span>
                                        }
                                        <i class="fas fa-plus me-2"></i>
                                        Create Test User
                                    </button>
                                    <button class="btn btn-secondary" @onclick="GenerateUniqueData">
                                        <i class="fas fa-random me-2"></i>
                                        Generate Unique Data
                                    </button>
                                </div>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-warning">
                            <h5><i class="fas fa-exclamation-triangle me-2"></i>Access Denied</h5>
                            <p>You do not have permission to create users. Only Super Admin and Excellence Team members can create users.</p>
                        </div>
                    }

                    <!-- Existing Users -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-list me-2"></i>Existing Users (Employee IDs)</h5>
                        </div>
                        <div class="card-body">
                            @if (existingEmployeeIds.Any())
                            {
                                <div class="row">
                                    @foreach (var empId in existingEmployeeIds)
                                    {
                                        <div class="col-md-2 mb-2">
                                            <span class="badge bg-secondary">@empId</span>
                                        </div>
                                    }
                                </div>
                            }
                            else
                            {
                                <p class="text-muted">No users found</p>
                            }
                        </div>
                    </div>

                    <!-- Test Results -->
                    @if (!string.IsNullOrEmpty(testResult))
                    {
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-clipboard-check me-2"></i>Test Results</h5>
                            </div>
                            <div class="card-body">
                                <pre class="bg-light p-3 rounded">@testResult</pre>
                            </div>
                        </div>
                    }

                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private ApplicationUser? currentUser = null;
    private List<string> existingEmployeeIds = new();
    private bool isCreating = false;
    private string testResult = "";
    
    private string testEmployeeId = "";
    private string testEmail = "";
    private string testEnglishName = "";
    private string testArabicName = "";

    protected override async Task OnInitializedAsync()
    {
        await LoadCurrentUser();
        await LoadExistingUsers();
        GenerateUniqueData();
    }

    private async Task LoadCurrentUser()
    {
        try
        {
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                var userEmail = authState.User.Identity.Name;
                if (!string.IsNullOrEmpty(userEmail))
                {
                    currentUser = await UserManager.FindByEmailAsync(userEmail);
                }
            }
        }
        catch (Exception ex)
        {
            testResult += $"Error loading current user: {ex.Message}\n";
        }
    }

    private async Task LoadExistingUsers()
    {
        try
        {
            existingEmployeeIds = await DbContext.Users
                .Where(u => !u.IsDeleted)
                .Select(u => u.EmployeeId)
                .OrderBy(id => id)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            testResult += $"Error loading existing users: {ex.Message}\n";
        }
    }

    private bool CanCreateUsers()
    {
        return currentUser?.Role == UserRole.SUPER_ADMIN || 
               currentUser?.Role == UserRole.EXCELLENCE_TEAM;
    }

    private string GetRoleDisplayName(UserRole role)
    {
        return role switch
        {
            UserRole.SUPER_ADMIN => "Super Admin (سوبر أدمين)",
            UserRole.MANAGER => "Manager (المدير)",
            UserRole.SUPERVISOR => "Direct Supervisor (المسؤول المباشر)",
            UserRole.EXCELLENCE_TEAM => "Excellence Team (فريق التميز)",
            UserRole.EMPLOYEE => "Employee (موظف)",
            _ => role.ToString()
        };
    }

    private string GetUniqueEmployeeId()
    {
        var timestamp = DateTime.Now.ToString("MMddHHmmss");
        return $"TEST{timestamp}";
    }

    private string GetUniqueEmail()
    {
        var timestamp = DateTime.Now.ToString("MMddHHmmss");
        return $"test{timestamp}@example.com";
    }

    private void GenerateUniqueData()
    {
        var timestamp = DateTime.Now.ToString("MMddHHmmss");
        testEmployeeId = $"TEST{timestamp}";
        testEmail = $"test{timestamp}@example.com";
        testEnglishName = $"Test User {timestamp}";
        testArabicName = $"مستخدم تجريبي {timestamp}";
        StateHasChanged();
    }

    private async Task CreateTestUser()
    {
        if (!CanCreateUsers())
        {
            testResult = "ERROR: Access denied. You do not have permission to create users.";
            return;
        }

        isCreating = true;
        testResult = "";
        StateHasChanged();

        try
        {
            testResult += $"Starting user creation test...\n";
            testResult += $"Employee ID: {testEmployeeId}\n";
            testResult += $"Email: {testEmail}\n";
            testResult += $"English Name: {testEnglishName}\n";
            testResult += $"Arabic Name: {testArabicName}\n\n";

            // Check for duplicates
            var existingUserByEmail = await UserManager.FindByEmailAsync(testEmail);
            if (existingUserByEmail != null)
            {
                testResult += "ERROR: A user with this email already exists.\n";
                return;
            }

            var existingUserByEmployeeId = await DbContext.Users
                .FirstOrDefaultAsync(u => u.EmployeeId == testEmployeeId && !u.IsDeleted);
            if (existingUserByEmployeeId != null)
            {
                testResult += "ERROR: A user with this Employee ID already exists.\n";
                return;
            }

            // Create new user
            var newUser = new ApplicationUser
            {
                EmployeeId = testEmployeeId,
                Email = testEmail,
                UserName = testEmail,
                EnglishName = testEnglishName,
                ArabicName = testArabicName,
                Role = UserRole.EMPLOYEE,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            var password = "Password123!";
            var result = await UserManager.CreateAsync(newUser, password);

            if (result.Succeeded)
            {
                testResult += "SUCCESS: User created successfully!\n";
                testResult += $"Generated Password: {password}\n";
                testResult += $"User ID: {newUser.Id}\n";
                testResult += $"Created At: {newUser.CreatedAt}\n\n";
                testResult += "The user should now appear in the Users page and be able to login.\n";
                
                // Reload existing users
                await LoadExistingUsers();
                
                // Generate new test data for next test
                GenerateUniqueData();
            }
            else
            {
                testResult += "ERROR: User creation failed.\n";
                testResult += "Errors:\n";
                foreach (var error in result.Errors)
                {
                    testResult += $"- {error.Description}\n";
                }
            }
        }
        catch (Exception ex)
        {
            testResult += $"EXCEPTION: {ex.Message}\n";
            testResult += $"Stack Trace: {ex.StackTrace}\n";
        }
        finally
        {
            isCreating = false;
            StateHasChanged();
        }
    }
}
