using EmployeeRatingSystem.Blazor.Components.Shared;
using static EmployeeRatingSystem.Blazor.Components.Shared.PageHeader;

namespace EmployeeRatingSystem.Blazor.Services
{
    /// <summary>
    /// Service for managing breadcrumb navigation
    /// </summary>
    public interface IBreadcrumbService
    {
        List<BreadcrumbItem> GetBreadcrumbs(string currentPath, ILocalizationService localizationService);
        List<BreadcrumbItem> CreateBreadcrumbs(params (string text, string? url, bool isActive)[] items);
        List<BreadcrumbItem> GetDashboardBreadcrumbs(ILocalizationService localizationService);
        List<BreadcrumbItem> GetDepartmentsBreadcrumbs(ILocalizationService localizationService, string? action = null);
        List<BreadcrumbItem> GetEvaluationsBreadcrumbs(ILocalizationService localizationService, string? action = null);
        List<BreadcrumbItem> GetUsersBreadcrumbs(ILocalizationService localizationService, string? action = null);
    }

    public class BreadcrumbService : IBreadcrumbService
    {
        public List<BreadcrumbItem> GetBreadcrumbs(string currentPath, ILocalizationService localizationService)
        {
            var breadcrumbs = new List<BreadcrumbItem>();
            
            // Always start with Home
            breadcrumbs.Add(new BreadcrumbItem(
                localizationService.GetLocalizedString("Home", "الرئيسية"), 
                "/", 
                false
            ));

            // Parse the current path
            var segments = currentPath.Trim('/').Split('/', StringSplitOptions.RemoveEmptyEntries);
            
            if (segments.Length == 0)
            {
                breadcrumbs[0].IsActive = true;
                return breadcrumbs;
            }

            var currentUrl = "";
            for (int i = 0; i < segments.Length; i++)
            {
                currentUrl += "/" + segments[i];
                var isLast = i == segments.Length - 1;
                
                var breadcrumbText = GetBreadcrumbText(segments[i], localizationService);
                breadcrumbs.Add(new BreadcrumbItem(breadcrumbText, isLast ? null : currentUrl, isLast));
            }

            return breadcrumbs;
        }

        public List<BreadcrumbItem> CreateBreadcrumbs(params (string text, string? url, bool isActive)[] items)
        {
            return items.Select(item => new BreadcrumbItem(item.text, item.url, item.isActive)).ToList();
        }

        public List<BreadcrumbItem> GetDashboardBreadcrumbs(ILocalizationService localizationService)
        {
            return CreateBreadcrumbs(
                (localizationService.GetLocalizedString("Home", "الرئيسية"), "/", false),
                (localizationService.GetLocalizedString("Dashboard", "لوحة التحكم"), null, true)
            );
        }

        public List<BreadcrumbItem> GetDepartmentsBreadcrumbs(ILocalizationService localizationService, string? action = null)
        {
            var breadcrumbs = CreateBreadcrumbs(
                (localizationService.GetLocalizedString("Home", "الرئيسية"), "/", false),
                (localizationService.GetLocalizedString("Departments", "الأقسام"), action != null ? "/departments" : null, action == null)
            );

            if (action != null)
            {
                var actionText = action.ToLower() switch
                {
                    "create" => localizationService.GetLocalizedString("Create Department", "إنشاء قسم"),
                    "edit" => localizationService.GetLocalizedString("Edit Department", "تعديل قسم"),
                    "details" => localizationService.GetLocalizedString("Department Details", "تفاصيل القسم"),
                    _ => action
                };
                breadcrumbs.Add(new BreadcrumbItem(actionText, null, true));
            }

            return breadcrumbs;
        }

        public List<BreadcrumbItem> GetEvaluationsBreadcrumbs(ILocalizationService localizationService, string? action = null)
        {
            var breadcrumbs = CreateBreadcrumbs(
                (localizationService.GetLocalizedString("Home", "الرئيسية"), "/", false),
                (localizationService.GetLocalizedString("Evaluations", "التقييمات"), action != null ? "/evaluations" : null, action == null)
            );

            if (action != null)
            {
                var actionText = action.ToLower() switch
                {
                    "create" => localizationService.GetLocalizedString("Create Evaluation", "إنشاء تقييم"),
                    "edit" => localizationService.GetLocalizedString("Edit Evaluation", "تعديل تقييم"),
                    "details" => localizationService.GetLocalizedString("Evaluation Details", "تفاصيل التقييم"),
                    "bulk" => localizationService.GetLocalizedString("Bulk Evaluation", "تقييم جماعي"),
                    "reports" => localizationService.GetLocalizedString("Reports", "التقارير"),
                    _ => action
                };
                breadcrumbs.Add(new BreadcrumbItem(actionText, null, true));
            }

            return breadcrumbs;
        }

        public List<BreadcrumbItem> GetUsersBreadcrumbs(ILocalizationService localizationService, string? action = null)
        {
            var breadcrumbs = CreateBreadcrumbs(
                (localizationService.GetLocalizedString("Home", "الرئيسية"), "/", false),
                (localizationService.GetLocalizedString("Users", "المستخدمون"), action != null ? "/users" : null, action == null)
            );

            if (action != null)
            {
                var actionText = action.ToLower() switch
                {
                    "create" => localizationService.GetLocalizedString("Create User", "إنشاء مستخدم"),
                    "edit" => localizationService.GetLocalizedString("Edit User", "تعديل مستخدم"),
                    "details" => localizationService.GetLocalizedString("User Details", "تفاصيل المستخدم"),
                    "profile" => localizationService.GetLocalizedString("Profile", "الملف الشخصي"),
                    _ => action
                };
                breadcrumbs.Add(new BreadcrumbItem(actionText, null, true));
            }

            return breadcrumbs;
        }

        private string GetBreadcrumbText(string segment, ILocalizationService localizationService)
        {
            return segment.ToLower() switch
            {
                "dashboard" => localizationService.GetLocalizedString("Dashboard", "لوحة التحكم"),
                "departments" => localizationService.GetLocalizedString("Departments", "الأقسام"),
                "evaluations" => localizationService.GetLocalizedString("Evaluations", "التقييمات"),
                "users" => localizationService.GetLocalizedString("Users", "المستخدمون"),
                "create" => localizationService.GetLocalizedString("Create", "إنشاء"),
                "edit" => localizationService.GetLocalizedString("Edit", "تعديل"),
                "details" => localizationService.GetLocalizedString("Details", "التفاصيل"),
                "bulk" => localizationService.GetLocalizedString("Bulk", "جماعي"),
                "reports" => localizationService.GetLocalizedString("Reports", "التقارير"),
                "profile" => localizationService.GetLocalizedString("Profile", "الملف الشخصي"),
                "settings" => localizationService.GetLocalizedString("Settings", "الإعدادات"),
                "login" => localizationService.GetLocalizedString("Login", "تسجيل الدخول"),
                "register" => localizationService.GetLocalizedString("Register", "التسجيل"),
                _ => segment.Replace("-", " ").Replace("_", " ")
            };
        }
    }

    /// <summary>
    /// Extension methods for breadcrumb management
    /// </summary>
    public static class BreadcrumbExtensions
    {
        /// <summary>
        /// Adds a breadcrumb item to the list
        /// </summary>
        public static List<BreadcrumbItem> AddBreadcrumb(this List<BreadcrumbItem> breadcrumbs, string text, string? url = null, bool isActive = false)
        {
            breadcrumbs.Add(new BreadcrumbItem(text, url, isActive));
            return breadcrumbs;
        }

        /// <summary>
        /// Sets the last breadcrumb as active
        /// </summary>
        public static List<BreadcrumbItem> SetLastAsActive(this List<BreadcrumbItem> breadcrumbs)
        {
            if (breadcrumbs.Any())
            {
                // Set all as inactive first
                foreach (var item in breadcrumbs)
                {
                    item.IsActive = false;
                }
                
                // Set last as active
                breadcrumbs.Last().IsActive = true;
                breadcrumbs.Last().Url = null;
            }
            return breadcrumbs;
        }

        /// <summary>
        /// Removes the last breadcrumb
        /// </summary>
        public static List<BreadcrumbItem> RemoveLast(this List<BreadcrumbItem> breadcrumbs)
        {
            if (breadcrumbs.Any())
            {
                breadcrumbs.RemoveAt(breadcrumbs.Count - 1);
            }
            return breadcrumbs;
        }
    }
}
