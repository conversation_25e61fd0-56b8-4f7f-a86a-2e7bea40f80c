using Microsoft.AspNetCore.Localization;
using Microsoft.Extensions.Localization;
using System.Globalization;

namespace EmployeeRatingSystem.Blazor.Services
{
    /// <summary>
    /// Service for handling localization and culture management
    /// </summary>
    public interface ILocalizationService
    {
        string CurrentLanguage { get; }
        bool IsArabic { get; }
        bool IsRTL { get; }
        string GetLocalizedString(string key);
        string GetLocalizedString(string englishText, string arabicText);
        Task SetLanguageAsync(string culture);
        string GetPageTitle(string englishTitle, string arabicTitle);
        string GetDirection();
        string GetTextAlign();
        CultureInfo GetCurrentCulture();
        string FormatDate(DateTime date);
        string FormatDateHijri(DateTime date);
        string FormatNumber(decimal number);
        string FormatPercentage(decimal percentage);
        DateTime ConvertFromUtc(DateTime utcDate);
        string FormatDateWithTime(DateTime date);
        string FormatTimeOnly(DateTime date);
    }

    public class LocalizationService : ILocalizationService
    {
        private readonly IStringLocalizer<SharedResource> _localizer;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public LocalizationService(
            IStringLocalizer<SharedResource> localizer,
            IHttpContextAccessor httpContextAccessor)
        {
            _localizer = localizer;
            _httpContextAccessor = httpContextAccessor;
        }

        public string CurrentLanguage => GetCurrentCulture().TwoLetterISOLanguageName;

        public bool IsArabic => CurrentLanguage == "ar";

        public bool IsRTL => IsArabic;

        public CultureInfo GetCurrentCulture()
        {
            return CultureInfo.CurrentCulture;
        }

        public string GetLocalizedString(string key)
        {
            return _localizer[key];
        }

        public string GetLocalizedString(string englishText, string arabicText)
        {
            return IsArabic ? arabicText : englishText;
        }

        public Task SetLanguageAsync(string culture)
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext != null)
            {
                httpContext.Response.Cookies.Append(
                    CookieRequestCultureProvider.DefaultCookieName,
                    CookieRequestCultureProvider.MakeCookieValue(new RequestCulture(culture)),
                    new CookieOptions
                    {
                        Expires = DateTimeOffset.UtcNow.AddYears(1),
                        HttpOnly = false,
                        Secure = false,
                        SameSite = SameSiteMode.Lax
                    }
                );
            }

            return Task.CompletedTask;
        }

        public string GetPageTitle(string englishTitle, string arabicTitle)
        {
            var title = GetLocalizedString(englishTitle, arabicTitle);
            var appName = GetLocalizedString("Employee Rating System", "نظام تقييم الموظفين");
            return $"{title} - {appName}";
        }

        public string GetDirection()
        {
            return IsRTL ? "rtl" : "ltr";
        }

        public string GetTextAlign()
        {
            return IsRTL ? "right" : "left";
        }

        public string FormatDate(DateTime date)
        {
            // Convert from UTC first to ensure consistent date values across languages
            var localDate = ConvertFromUtc(date);

            if (IsArabic)
            {
                // Use Arabic culture with consistent date format
                var arabicCulture = new System.Globalization.CultureInfo("ar-SA");
                return localDate.ToString("dd/MM/yyyy", arabicCulture);
            }
            else
            {
                return localDate.ToString("d", GetCurrentCulture());
            }
        }

        public string FormatDateHijri(DateTime date)
        {
            // Support for Hijri calendar as specified in PRD Section 5.3
            if (IsArabic)
            {
                var localDate = ConvertFromUtc(date);
                var hijriCalendar = new System.Globalization.HijriCalendar();
                var arabicCulture = new CultureInfo("ar-SA");
                arabicCulture.DateTimeFormat.Calendar = hijriCalendar;
                return localDate.ToString("d", arabicCulture);
            }
            return FormatDate(date);
        }

        public string FormatNumber(decimal number)
        {
            return number.ToString("N", GetCurrentCulture());
        }

        public string FormatPercentage(decimal percentage)
        {
            return percentage.ToString("P", GetCurrentCulture());
        }

        public DateTime ConvertFromUtc(DateTime utcDate)
        {
            // For now, we'll use a simple offset approach to avoid timezone conversion issues
            // In production, this should be user-specific timezone
            try
            {
                // Use a fixed offset for Middle East timezone (UTC+3)
                // This is safer than system timezone conversion which can cause date corruption
                return utcDate.AddHours(3);
            }
            catch (Exception)
            {
                // If conversion fails, return the original date
                return utcDate;
            }
        }

        public string FormatDateWithTime(DateTime date)
        {
            var localDate = ConvertFromUtc(date);
            return localDate.ToString("g", GetCurrentCulture());
        }

        public string FormatTimeOnly(DateTime date)
        {
            // Convert from UTC first to ensure consistent time values across languages
            var localDate = ConvertFromUtc(date);

            if (IsArabic)
            {
                var arabicCulture = new System.Globalization.CultureInfo("ar-SA");
                return localDate.ToString("HH:mm", arabicCulture);
            }
            else
            {
                return localDate.ToString("t", GetCurrentCulture());
            }
        }
    }

    /// <summary>
    /// Shared resource class for localization
    /// </summary>
    public class SharedResource
    {
        // This class is used as a marker for shared localization resources
    }

    /// <summary>
    /// Static class for common localized strings
    /// </summary>
    public static class LocalizedStrings
    {
        // Navigation
        public static readonly (string En, string Ar) Dashboard = ("Dashboard", "لوحة التحكم");
        public static readonly (string En, string Ar) Departments = ("Departments", "الأقسام");
        public static readonly (string En, string Ar) Evaluations = ("Evaluations", "التقييمات");
        public static readonly (string En, string Ar) Users = ("Users", "المستخدمون");
        public static readonly (string En, string Ar) Login = ("Login", "تسجيل الدخول");
        public static readonly (string En, string Ar) Logout = ("Logout", "تسجيل الخروج");
        public static readonly (string En, string Ar) Register = ("Register", "التسجيل");

        // Common Actions
        public static readonly (string En, string Ar) Create = ("Create", "إنشاء");
        public static readonly (string En, string Ar) Edit = ("Edit", "تعديل");
        public static readonly (string En, string Ar) Delete = ("Delete", "حذف");
        public static readonly (string En, string Ar) Save = ("Save", "حفظ");
        public static readonly (string En, string Ar) Cancel = ("Cancel", "إلغاء");
        public static readonly (string En, string Ar) Submit = ("Submit", "إرسال");
        public static readonly (string En, string Ar) Search = ("Search", "بحث");
        public static readonly (string En, string Ar) Filter = ("Filter", "تصفية");
        public static readonly (string En, string Ar) Export = ("Export", "تصدير");
        public static readonly (string En, string Ar) Import = ("Import", "استيراد");

        // Status
        public static readonly (string En, string Ar) Active = ("Active", "نشط");
        public static readonly (string En, string Ar) Inactive = ("Inactive", "غير نشط");
        public static readonly (string En, string Ar) Pending = ("Pending", "معلق");
        public static readonly (string En, string Ar) Completed = ("Completed", "مكتمل");
        public static readonly (string En, string Ar) Draft = ("Draft", "مسودة");
        public static readonly (string En, string Ar) Submitted = ("Submitted", "مرسل");
        public static readonly (string En, string Ar) Approved = ("Approved", "موافق عليه");
        public static readonly (string En, string Ar) Rejected = ("Rejected", "مرفوض");

        // Messages
        public static readonly (string En, string Ar) Success = ("Success", "نجح");
        public static readonly (string En, string Ar) Error = ("Error", "خطأ");
        public static readonly (string En, string Ar) Warning = ("Warning", "تحذير");
        public static readonly (string En, string Ar) Info = ("Information", "معلومات");
        public static readonly (string En, string Ar) Loading = ("Loading...", "جاري التحميل...");
        public static readonly (string En, string Ar) NoData = ("No data available", "لا توجد بيانات متاحة");

        // Employee Rating Specific
        public static readonly (string En, string Ar) EmployeeRatingSystem = ("Employee Rating System", "نظام تقييم الموظفين");
        public static readonly (string En, string Ar) Employee = ("Employee", "موظف");
        public static readonly (string En, string Ar) Employees = ("Employees", "الموظفون");
        public static readonly (string En, string Ar) Evaluation = ("Evaluation", "تقييم");
        public static readonly (string En, string Ar) Evaluator = ("Evaluator", "المقيم");
        public static readonly (string En, string Ar) Score = ("Score", "النتيجة");
        public static readonly (string En, string Ar) Category = ("Category", "الفئة");
        public static readonly (string En, string Ar) Question = ("Question", "السؤال");
        public static readonly (string En, string Ar) Answer = ("Answer", "الإجابة");
        public static readonly (string En, string Ar) Comments = ("Comments", "التعليقات");
        public static readonly (string En, string Ar) Department = ("Department", "القسم");
        public static readonly (string En, string Ar) Manager = ("Manager", "المدير");
        public static readonly (string En, string Ar) Supervisor = ("Supervisor", "المشرف");

        // Date and Time
        public static readonly (string En, string Ar) Date = ("Date", "التاريخ");
        public static readonly (string En, string Ar) Time = ("Time", "الوقت");
        public static readonly (string En, string Ar) CreatedAt = ("Created At", "تاريخ الإنشاء");
        public static readonly (string En, string Ar) UpdatedAt = ("Updated At", "تاريخ التحديث");
        public static readonly (string En, string Ar) StartDate = ("Start Date", "تاريخ البداية");
        public static readonly (string En, string Ar) EndDate = ("End Date", "تاريخ النهاية");

        // Validation
        public static readonly (string En, string Ar) Required = ("This field is required", "هذا الحقل مطلوب");
        public static readonly (string En, string Ar) InvalidEmail = ("Please enter a valid email address", "يرجى إدخال عنوان بريد إلكتروني صحيح");
        public static readonly (string En, string Ar) PasswordMismatch = ("Passwords do not match", "كلمات المرور غير متطابقة");
        public static readonly (string En, string Ar) MinLength = ("Minimum length is {0} characters", "الحد الأدنى للطول هو {0} أحرف");
        public static readonly (string En, string Ar) MaxLength = ("Maximum length is {0} characters", "الحد الأقصى للطول هو {0} أحرف");

        public static string Get((string En, string Ar) text, bool isArabic)
        {
            return isArabic ? text.Ar : text.En;
        }
    }
}
