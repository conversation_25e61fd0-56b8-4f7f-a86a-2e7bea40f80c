@inherits BaseChart
@inject IJSRuntime JSRuntime

@if (IsLoading)
{
    <div class="text-center py-4">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">@LoadingMessage</span>
        </div>
        <p class="mt-2 text-muted">@LoadingMessage</p>
    </div>
}
else if (Data?.Any() != true || Labels?.Any() != true)
{
    <div class="text-center py-4 text-muted">
        <i class="fas fa-chart-bar fa-2x mb-2"></i>
        <p>@L("No data available", "لا توجد بيانات متاحة")</p>
        <p style="font-size: 12px; color: red;">Debug: CanvasId=@CanvasId, Data count: @(Data?.Length ?? 0), Labels count: @(Labels?.Length ?? 0), IsLoading: @IsLoading</p>
        <p style="font-size: 12px; color: blue;">Data: @(Data != null ? string.Join(", ", Data.Take(3)) : "null")</p>
        <p style="font-size: 12px; color: green;">Labels: @(Labels != null ? string.Join(", ", Labels.Take(3)) : "null")</p>
    </div>
}
else
{
    <div class="chart-container @CssClass" style="@Style">
        <!-- Simplified Chart.js Canvas -->
        <canvas id="@CanvasId" style="width: 100%; height: 100%; display: block;"></canvas>

        <!-- Fallback HTML/CSS Chart (hidden by default) -->
        <div class="css-chart-fallback" style="display: none; padding: 20px;">
            <h6 class="text-center mb-4" style="color: #333; font-weight: 600;">@Title</h6>
            @if (Data != null && Labels != null && Data.Length == Labels.Length && Data.Length > 0)
            {
                var maxValue = Data.Max();
                <div class="chart-bars">
                    @for (int i = 0; i < Data.Length; i++)
                    {
                        var percentage = maxValue > 0 ? (Data[i] / maxValue) * 100 : 0;
                        var barColor = Color ?? "#6366F1";
                        <div class="bar-item" style="margin-bottom: 15px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                                <span style="font-size: 13px; font-weight: 500; color: #333;">@Labels[i]</span>
                                <span style="font-size: 12px; color: #666; font-weight: 600;">@Data[i].ToString("F1")%</span>
                            </div>
                            <div style="height: 28px; background-color: #f8f9fa; border-radius: 14px; overflow: hidden; position: relative;">
                                <div style="height: 100%; background: linear-gradient(90deg, @barColor, @(barColor)cc); border-radius: 14px; width: @(percentage)%; min-width: 3px; transition: width 0.6s ease;"></div>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="text-center text-muted" style="padding: 40px 0;">
                    <i class="fas fa-chart-bar fa-2x mb-2" style="opacity: 0.5;"></i>
                    <p>@L("No data available", "لا توجد بيانات متاحة")</p>
                </div>
            }
        </div>
    </div>
}

@code {
    /// <summary>
    /// Chart data labels
    /// </summary>
    [Parameter] public string[]? Labels { get; set; }

    /// <summary>
    /// Chart data values
    /// </summary>
    [Parameter] public double[]? Data { get; set; }

    /// <summary>
    /// Chart color
    /// </summary>
    [Parameter] public string Color { get; set; } = "#1e3a8a";

    /// <summary>
    /// Whether to show horizontal bars
    /// </summary>
    [Parameter] public bool Horizontal { get; set; } = false;

    /// <summary>
    /// Y-axis label
    /// </summary>
    [Parameter] public string YAxisLabel { get; set; } = "";

    /// <summary>
    /// X-axis label
    /// </summary>
    [Parameter] public string XAxisLabel { get; set; } = "";

    private bool _hasRendered = false;

    protected override async Task InitializeChart()
    {
        // Chart initialization will be handled in OnAfterRenderAsync
        await Task.CompletedTask;
    }

    public override async Task UpdateChart()
    {
        if (Data?.Any() == true && Labels?.Any() == true)
        {
            await CreateChart();
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            Console.WriteLine($"OnAfterRenderAsync called for {CanvasId} - firstRender: {firstRender}");
            _hasRendered = true;

            // Test Chart.js availability
            try
            {
                var chartJsAvailable = await JSRuntime.InvokeAsync<bool>("testChartJs");
                Console.WriteLine($"Chart.js available: {chartJsAvailable}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error testing Chart.js: {ex.Message}");
            }

            // Minimal delay for DOM readiness
            await Task.Delay(50);
        }

        // Always try to create chart if we have data and have rendered
        if (_hasRendered && Data?.Any() == true && Labels?.Any() == true)
        {
            Console.WriteLine($"Data and labels available for {CanvasId}, calling CreateChart()");
            await CreateChart();
        }
        else if (_hasRendered && firstRender)
        {
            Console.WriteLine($"Data or labels not available for {CanvasId} - Data: {Data?.Length ?? 0}, Labels: {Labels?.Length ?? 0}");

            // Single retry after a short delay for data to become available
            _ = Task.Run(async () =>
            {
                await Task.Delay(200);
                if (Data?.Any() == true && Labels?.Any() == true)
                {
                    Console.WriteLine($"Retry: Data now available for {CanvasId}, creating chart...");
                    await InvokeAsync(async () => await CreateChart());
                }
                else
                {
                    Console.WriteLine($"Retry failed for {CanvasId}, data still not available");
                }
            });
        }

        await base.OnAfterRenderAsync(firstRender);
    }



    private async Task CreateChart()
    {
        try
        {
            Console.WriteLine($"CreateChart called for {CanvasId} - Data: {Data?.Length ?? 0} items, Labels: {Labels?.Length ?? 0} items");
            if (Horizontal)
            {
                await JSRuntime.InvokeVoidAsync("createHorizontalBarChart", CanvasId, Labels, Data, Title, Color, XAxisLabel, YAxisLabel);
            }
            else
            {
                Console.WriteLine($"Calling createBarChart for {CanvasId}");
                await JSRuntime.InvokeVoidAsync("createBarChart", CanvasId, Labels, Data, Title, Color);
                Console.WriteLine($"createBarChart call completed for {CanvasId}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error creating bar chart {CanvasId}: {ex.Message}. Showing fallback chart.");
            // Show fallback chart if Chart.js fails
            try
            {
                await JSRuntime.InvokeVoidAsync("showFallbackChart", CanvasId);
            }
            catch (Exception fallbackEx)
            {
                Console.WriteLine($"Error showing fallback chart: {fallbackEx.Message}");
            }
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        Console.WriteLine($"OnParametersSetAsync called for {CanvasId} - HasRendered: {_hasRendered}, Data: {Data?.Length ?? 0}, Labels: {Labels?.Length ?? 0}");

        // Update chart if we've already rendered and have data
        if (_hasRendered && Data?.Any() == true && Labels?.Any() == true)
        {
            Console.WriteLine($"Updating chart for {CanvasId} in OnParametersSetAsync");
            await UpdateChart();
        }
        // If we haven't rendered yet but have data, we'll catch it in OnAfterRenderAsync
        else if (!_hasRendered && Data?.Any() == true && Labels?.Any() == true)
        {
            Console.WriteLine($"Data available for {CanvasId} but not yet rendered - will create in OnAfterRenderAsync");
        }

        await base.OnParametersSetAsync();
    }
}
