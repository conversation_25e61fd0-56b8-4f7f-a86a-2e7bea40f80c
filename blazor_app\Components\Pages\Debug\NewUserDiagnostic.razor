@page "/debug/new-user-diagnostic"
@rendermode InteractiveServer
@using EmployeeRatingSystem.Blazor.Data
@using EmployeeRatingSystem.Blazor.Models
@using Microsoft.EntityFrameworkCore
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.Components.Authorization
@inject ApplicationDbContext DbContext
@inject UserManager<ApplicationUser> UserManager
@inject IJSRuntime JSRuntime

<PageTitle>New User Authentication Diagnostic</PageTitle>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-user-plus me-2"></i>
                        New User Authentication Diagnostic
                    </h4>
                </div>
                <div class="card-body">
                    @if (isLoading)
                    {
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Analyzing new user accounts...</p>
                        </div>
                    }
                    else
                    {
                        <!-- Issue Summary -->
                        <div class="alert alert-danger mb-4">
                            <h5><i class="fas fa-exclamation-triangle me-2"></i>Authentication Issue Identified</h5>
                            <p><strong>Problem:</strong> New users created through the Users page are not properly registered with ASP.NET Core Identity.</p>
                            <p><strong>Root Cause:</strong> The SaveUser() method uses DbContext.Users.Add() instead of UserManager.CreateAsync().</p>
                            <p><strong>Impact:</strong> New users cannot authenticate because they lack password hashes and security stamps.</p>
                        </div>

                        <!-- Users Analysis -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h3>@totalUsers</h3>
                                        <p class="mb-0">Total Users</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h3>@properlyRegisteredUsers</h3>
                                        <p class="mb-0">Properly Registered</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-danger text-white">
                                    <div class="card-body text-center">
                                        <h3>@incompleteUsers</h3>
                                        <p class="mb-0">Incomplete Registration</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-dark">
                                    <div class="card-body text-center">
                                        <h3>@newlyCreatedUsers</h3>
                                        <p class="mb-0">Recently Created</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- User Details Table -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Employee ID</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Role</th>
                                        <th>Created</th>
                                        <th>Has Password</th>
                                        <th>Security Stamp</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var user in userAnalysis)
                                    {
                                        <tr class="@(user.HasAuthenticationIssue ? "table-danger" : "")">
                                            <td><strong>@user.User.EmployeeId</strong></td>
                                            <td>@user.User.EnglishName</td>
                                            <td>@user.User.Email</td>
                                            <td>
                                                <span class="badge @GetRoleBadgeClass(user.User.Role)">
                                                    @GetRoleDisplayName(user.User.Role)
                                                </span>
                                            </td>
                                            <td>@user.User.CreatedAt.ToString("yyyy-MM-dd HH:mm")</td>
                                            <td>
                                                @if (user.HasPassword)
                                                {
                                                    <span class="badge bg-success">✓ Yes</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">✗ No</span>
                                                }
                                            </td>
                                            <td>
                                                @if (user.HasSecurityStamp)
                                                {
                                                    <span class="badge bg-success">✓ Yes</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">✗ No</span>
                                                }
                                            </td>
                                            <td>
                                                @if (user.HasAuthenticationIssue)
                                                {
                                                    <span class="badge bg-danger">Authentication Issue</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-success">OK</span>
                                                }
                                            </td>
                                            <td>
                                                @if (user.HasAuthenticationIssue)
                                                {
                                                    <button class="btn btn-sm btn-primary" @onclick="() => FixUser(user.User)" disabled="@isFixing">
                                                        @if (isFixing)
                                                        {
                                                            <span class="spinner-border spinner-border-sm me-1"></span>
                                                        }
                                                        <span>Fix</span>
                                                    </button>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Fix All Button -->
                        @if (incompleteUsers > 0)
                        {
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-tools me-2"></i>Fix Authentication Issues</h6>
                                        <p>Click the button below to fix all users with authentication issues by properly registering them with ASP.NET Core Identity.</p>
                                        <button class="btn btn-success" @onclick="FixAllUsers" disabled="@isFixing">
                                            @if (isFixing)
                                            {
                                                <span class="spinner-border spinner-border-sm me-2"></span>
                                                <span>Fixing...</span>
                                            }
                                            else
                                            {
                                                <i class="fas fa-magic me-2"></i>
                                                <span>Fix All Authentication Issues</span>
                                            }
                                        </button>
                                    </div>
                                </div>
                            </div>
                        }

                        <!-- Refresh Button -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <button class="btn btn-primary" @onclick="RefreshAnalysis">
                                    <i class="fas fa-sync-alt me-1"></i>
                                    Refresh Analysis
                                </button>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private bool isLoading = true;
    private bool isFixing = false;
    private int totalUsers = 0;
    private int properlyRegisteredUsers = 0;
    private int incompleteUsers = 0;
    private int newlyCreatedUsers = 0;

    private List<UserAnalysisResult> userAnalysis = new();

    protected override async Task OnInitializedAsync()
    {
        await AnalyzeUsers();
    }

    private async Task RefreshAnalysis()
    {
        isLoading = true;
        StateHasChanged();
        await AnalyzeUsers();
    }

    private async Task AnalyzeUsers()
    {
        try
        {
            var users = await DbContext.Users.OrderByDescending(u => u.CreatedAt).ToListAsync();
            var recentThreshold = DateTime.UtcNow.AddDays(-7); // Users created in last 7 days

            userAnalysis.Clear();
            totalUsers = users.Count;
            properlyRegisteredUsers = 0;
            incompleteUsers = 0;
            newlyCreatedUsers = 0;

            foreach (var user in users)
            {
                var hasPassword = !string.IsNullOrEmpty(user.PasswordHash);
                var hasSecurityStamp = !string.IsNullOrEmpty(user.SecurityStamp);
                var hasAuthIssue = !hasPassword || !hasSecurityStamp;
                var isRecentlyCreated = user.CreatedAt >= recentThreshold;

                if (hasAuthIssue) incompleteUsers++;
                else properlyRegisteredUsers++;

                if (isRecentlyCreated) newlyCreatedUsers++;

                userAnalysis.Add(new UserAnalysisResult
                {
                    User = user,
                    HasPassword = hasPassword,
                    HasSecurityStamp = hasSecurityStamp,
                    HasAuthenticationIssue = hasAuthIssue,
                    IsRecentlyCreated = isRecentlyCreated
                });
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error analyzing users: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task FixUser(ApplicationUser user)
    {
        isFixing = true;
        StateHasChanged();

        try
        {
            // Set a default password for the user
            var defaultPassword = "Password123!";
            
            // Remove the user from Identity if it exists
            var existingUser = await UserManager.FindByEmailAsync(user.Email);
            if (existingUser != null && string.IsNullOrEmpty(existingUser.PasswordHash))
            {
                // Update the existing user with proper Identity registration
                var token = await UserManager.GeneratePasswordResetTokenAsync(existingUser);
                var result = await UserManager.ResetPasswordAsync(existingUser, token, defaultPassword);
                
                if (result.Succeeded)
                {
                    await JSRuntime.InvokeVoidAsync("alert", $"Fixed authentication for {user.EnglishName}. Default password: {defaultPassword}");
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", $"Failed to fix {user.EnglishName}: {string.Join(", ", result.Errors.Select(e => e.Description))}");
                }
            }
            else if (existingUser == null)
            {
                // Create new Identity user
                var newIdentityUser = new ApplicationUser
                {
                    EmployeeId = user.EmployeeId,
                    Email = user.Email,
                    UserName = user.Email,
                    EnglishName = user.EnglishName,
                    ArabicName = user.ArabicName,
                    Role = user.Role,
                    PrimaryDepartmentId = user.PrimaryDepartmentId,
                    IsActive = user.IsActive,
                    CreatedAt = user.CreatedAt,
                    UpdatedAt = DateTime.UtcNow
                };

                var result = await UserManager.CreateAsync(newIdentityUser, defaultPassword);
                
                if (result.Succeeded)
                {
                    // Remove the old database-only record
                    DbContext.Users.Remove(user);
                    await DbContext.SaveChangesAsync();
                    
                    await JSRuntime.InvokeVoidAsync("alert", $"Fixed authentication for {user.EnglishName}. Default password: {defaultPassword}");
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", $"Failed to fix {user.EnglishName}: {string.Join(", ", result.Errors.Select(e => e.Description))}");
                }
            }

            await AnalyzeUsers();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error fixing user: {ex.Message}");
        }
        finally
        {
            isFixing = false;
            StateHasChanged();
        }
    }

    private async Task FixAllUsers()
    {
        isFixing = true;
        StateHasChanged();

        try
        {
            var usersToFix = userAnalysis.Where(u => u.HasAuthenticationIssue).ToList();
            var fixedCount = 0;

            foreach (var userResult in usersToFix)
            {
                try
                {
                    await FixUser(userResult.User);
                    fixedCount++;
                }
                catch (Exception ex)
                {
                    await JSRuntime.InvokeVoidAsync("console.error", $"Failed to fix {userResult.User.EnglishName}: {ex.Message}");
                }
            }

            await JSRuntime.InvokeVoidAsync("alert", $"Fixed {fixedCount} out of {usersToFix.Count} users with authentication issues.");
            await AnalyzeUsers();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error fixing users: {ex.Message}");
        }
        finally
        {
            isFixing = false;
            StateHasChanged();
        }
    }

    private string GetRoleBadgeClass(UserRole role)
    {
        return role switch
        {
            UserRole.SUPER_ADMIN => "bg-danger",
            UserRole.MANAGER => "bg-primary",
            UserRole.SUPERVISOR => "bg-info",
            UserRole.EXCELLENCE_TEAM => "bg-warning",
            UserRole.EMPLOYEE => "bg-secondary",
            _ => "bg-light text-dark"
        };
    }

    private string GetRoleDisplayName(UserRole role)
    {
        return role switch
        {
            UserRole.SUPER_ADMIN => "Super Admin",
            UserRole.MANAGER => "Manager",
            UserRole.SUPERVISOR => "Supervisor",
            UserRole.EXCELLENCE_TEAM => "Excellence Team",
            UserRole.EMPLOYEE => "Employee",
            _ => role.ToString()
        };
    }

    public class UserAnalysisResult
    {
        public ApplicationUser User { get; set; } = new();
        public bool HasPassword { get; set; }
        public bool HasSecurityStamp { get; set; }
        public bool HasAuthenticationIssue { get; set; }
        public bool IsRecentlyCreated { get; set; }
    }
}
