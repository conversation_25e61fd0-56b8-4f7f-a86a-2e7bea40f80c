@page "/debug/database-query"
@using Microsoft.EntityFrameworkCore
@using EmployeeRatingSystem.Blazor.Data
@using EmployeeRatingSystem.Blazor.Models
@inject ApplicationDbContext DbContext
@inject IJSRuntime JSRuntime

<h3>Database Query Tool</h3>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Direct Database Query Results</h5>
                    <button class="btn btn-primary" @onclick="QueryDatabase">Refresh Data</button>
                </div>
                <div class="card-body">
                    @if (isLoading)
                    {
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p>Loading database data...</p>
                        </div>
                    }
                    else if (users != null)
                    {
                        <div class="row">
                            <div class="col-md-6">
                                <h6>All Users in Database (@users.Count total)</h6>
                                <div style="max-height: 400px; overflow-y: auto;">
                                    <table class="table table-sm table-striped">
                                        <thead>
                                            <tr>
                                                <th>Employee ID</th>
                                                <th>Email</th>
                                                <th>Name</th>
                                                <th>Active</th>
                                                <th>Deleted</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var user in users.OrderBy(u => u.EmployeeId))
                                            {
                                                <tr class="@(user.IsDeleted ? "text-muted" : "")">
                                                    <td><strong>@user.EmployeeId</strong></td>
                                                    <td>@user.Email</td>
                                                    <td>@user.EnglishName</td>
                                                    <td>@(user.IsActive ? "✓" : "✗")</td>
                                                    <td>@(user.IsDeleted ? "✓" : "✗")</td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>Employee ID Analysis</h6>
                                <ul class="list-group">
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Total Users:</span>
                                        <strong>@users.Count</strong>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Active Users:</span>
                                        <strong>@users.Count(u => !u.IsDeleted)</strong>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Deleted Users:</span>
                                        <strong>@users.Count(u => u.IsDeleted)</strong>
                                    </li>
                                </ul>
                                
                                <h6 class="mt-3">Existing Employee IDs</h6>
                                <div class="alert alert-info">
                                    <strong>Active Employee IDs:</strong><br/>
                                    @string.Join(", ", users.Where(u => !u.IsDeleted).Select(u => u.EmployeeId).OrderBy(id => id))
                                </div>
                                
                                @if (users.Any(u => u.IsDeleted))
                                {
                                    <div class="alert alert-warning">
                                        <strong>Deleted Employee IDs:</strong><br/>
                                        @string.Join(", ", users.Where(u => u.IsDeleted).Select(u => u.EmployeeId).OrderBy(id => id))
                                    </div>
                                }
                                
                                <h6 class="mt-3">Suggested Unique Employee IDs</h6>
                                <div class="alert alert-success">
                                    @foreach (var suggestion in suggestedIds)
                                    {
                                        <div>
                                            <strong>@suggestion</strong>
                                            <button class="btn btn-sm btn-outline-primary ms-2" @onclick="() => TestEmployeeId(suggestion)">
                                                Test This ID
                                            </button>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    }
                    else if (!string.IsNullOrEmpty(errorMessage))
                    {
                        <div class="alert alert-danger">
                            <strong>Error:</strong> @errorMessage
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Test Employee ID</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Employee ID to Test</label>
                                <input type="text" class="form-control" @bind="testEmployeeId" placeholder="Enter Employee ID" />
                            </div>
                            <button class="btn btn-primary" @onclick="() => TestEmployeeId(testEmployeeId)" disabled="@isTesting">
                                @if (isTesting)
                                {
                                    <span class="spinner-border spinner-border-sm me-1"></span>
                                }
                                Test Employee ID
                            </button>
                        </div>
                        <div class="col-md-6">
                            @if (!string.IsNullOrEmpty(testResult))
                            {
                                <div class="alert @(testResult.Contains("✓") ? "alert-success" : "alert-danger")">
                                    @testResult
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Query Log</h5>
                </div>
                <div class="card-body">
                    <div style="height: 200px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; font-family: monospace; font-size: 12px;">
                        @foreach (var log in queryLogs)
                        {
                            <div>@log</div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private List<ApplicationUser>? users;
    private List<string> suggestedIds = new();
    private string testEmployeeId = "";
    private string testResult = "";
    private string errorMessage = "";
    private bool isLoading = false;
    private bool isTesting = false;
    private List<string> queryLogs = new();

    protected override async Task OnInitializedAsync()
    {
        await QueryDatabase();
    }

    private async Task QueryDatabase()
    {
        isLoading = true;
        errorMessage = "";
        queryLogs.Clear();
        StateHasChanged();
        
        try
        {
            AddLog("Starting database query...");
            
            // Query all users directly from database
            users = await DbContext.Users.ToListAsync();
            AddLog($"Found {users.Count} users in database");
            
            // Generate suggested unique Employee IDs
            GenerateSuggestedIds();
            
            AddLog("Database query completed successfully");
        }
        catch (Exception ex)
        {
            errorMessage = ex.Message;
            AddLog($"Error querying database: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void GenerateSuggestedIds()
    {
        suggestedIds.Clear();
        
        if (users == null) return;
        
        var existingIds = users.Select(u => u.EmployeeId.ToUpper()).ToHashSet();
        
        // Generate timestamp-based unique ID
        var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
        suggestedIds.Add($"EMP{timestamp}");
        
        // Generate sequential IDs
        for (int i = 1; i <= 20; i++)
        {
            var candidateId = $"EMP{i:D3}";
            if (!existingIds.Contains(candidateId.ToUpper()))
            {
                suggestedIds.Add(candidateId);
                if (suggestedIds.Count >= 6) break; // Limit to 6 suggestions
            }
        }
        
        // Generate some alternative patterns
        var patterns = new[] { "USER", "TEST", "NEW" };
        foreach (var pattern in patterns)
        {
            for (int i = 1; i <= 5; i++)
            {
                var candidateId = $"{pattern}{i:D3}";
                if (!existingIds.Contains(candidateId.ToUpper()))
                {
                    suggestedIds.Add(candidateId);
                    break;
                }
            }
        }
        
        AddLog($"Generated {suggestedIds.Count} suggested unique Employee IDs");
    }

    private async Task TestEmployeeId(string employeeId)
    {
        if (string.IsNullOrWhiteSpace(employeeId))
        {
            testResult = "Please enter an Employee ID to test";
            return;
        }
        
        isTesting = true;
        testResult = "";
        StateHasChanged();
        
        try
        {
            AddLog($"Testing Employee ID: '{employeeId}'");
            
            // Test using the same logic as the SaveUser method
            var existingUser = await DbContext.Users
                .FirstOrDefaultAsync(u => u.EmployeeId.ToLower() == employeeId.ToLower() && !u.IsDeleted);
            
            if (existingUser != null)
            {
                testResult = $"❌ DUPLICATE: Employee ID '{employeeId}' already exists\n" +
                           $"Used by: {existingUser.Email} ({existingUser.EnglishName})";
                AddLog($"DUPLICATE FOUND: {existingUser.Email}");
            }
            else
            {
                testResult = $"✓ AVAILABLE: Employee ID '{employeeId}' is available for use";
                AddLog($"Employee ID '{employeeId}' is available");
            }
        }
        catch (Exception ex)
        {
            testResult = $"ERROR: {ex.Message}";
            AddLog($"Error testing Employee ID: {ex.Message}");
        }
        finally
        {
            isTesting = false;
            StateHasChanged();
        }
    }

    private void AddLog(string message)
    {
        var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
        queryLogs.Add($"[{timestamp}] {message}");
        
        // Keep only last 50 log entries
        if (queryLogs.Count > 50)
        {
            queryLogs.RemoveAt(0);
        }
    }
}
