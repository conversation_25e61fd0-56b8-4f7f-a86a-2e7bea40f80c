using EmployeeRatingSystem.Blazor.Data;
using EmployeeRatingSystem.Blazor.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EmployeeRatingSystem.Blazor.Services
{
    /// <summary>
    /// Service for seeding sample evaluation data for testing
    /// </summary>
    public interface IEvaluationDataSeedingService
    {
        Task SeedSampleDataAsync(string evaluationPeriod, int departmentId);
        Task SeedCurrentMonthDataAsync();
        Task SeedComprehensiveEvaluationDataAsync(string evaluationPeriod);
    }

    public class EvaluationDataSeedingService : IEvaluationDataSeedingService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<EvaluationDataSeedingService> _logger;
    private readonly IEvaluationCalculationService _evaluationCalculationService;

        public EvaluationDataSeedingService(
            ApplicationDbContext context,
            ILogger<EvaluationDataSeedingService> logger,
            IEvaluationCalculationService evaluationCalculationService)
        {
            _context = context;
            _logger = logger;
            _evaluationCalculationService = evaluationCalculationService;
        }
        public async Task SeedCurrentMonthDataAsync()
        {
            var currentPeriod = DateTime.Now.ToString("yyyy-MM");

            // Get the first active department
            var department = await _context.Departments.FirstOrDefaultAsync(d => d.IsActive);
            if (department == null)
            {
                _logger.LogWarning("No active departments found for seeding data");
                return;
            }

            await SeedSampleDataAsync(currentPeriod, department.Id);
        }

        public async Task SeedSampleDataAsync(string evaluationPeriod, int departmentId)
        {
            try
            {
                _logger.LogInformation($"Seeding sample evaluation data for period {evaluationPeriod}, department {departmentId}");

                // Clear existing data for fresh calculation
                await ClearExistingDataForDepartment(evaluationPeriod, departmentId);

                // Get employees in the department
                var employees = await _context.Users
                    .Where(u => u.PrimaryDepartmentId == departmentId && u.IsActive && !u.IsDeleted)
                    .ToListAsync();

                if (!employees.Any())
                {
                    _logger.LogWarning($"No employees found in department {departmentId}");
                    return;
                }

                // Seed monthly working days
                await SeedMonthlyWorkingDays(evaluationPeriod);

                // Seed employee work volume data first
                await SeedEmployeeWorkVolumeData(evaluationPeriod, departmentId, employees);

                // Save employee work volume data before calculating department totals
                await _context.SaveChangesAsync();

                // Then seed department work volume totals based on employee data
                await SeedDepartmentWorkVolumeTotals(evaluationPeriod, departmentId);

                // Seed attendance data
                await SeedAttendanceData(evaluationPeriod, departmentId, employees);

                // Seed supervisor evaluations
                await SeedSupervisorEvaluations(evaluationPeriod, departmentId, employees);

                await _context.SaveChangesAsync();

                _logger.LogInformation($"Successfully seeded sample data for {employees.Count} employees");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error seeding sample data for period {evaluationPeriod}, department {departmentId}");
                throw;
            }
        }

        private async Task SeedMonthlyWorkingDays(string evaluationPeriod)
        {
            var existing = await _context.MonthlyWorkingDays
                .FirstOrDefaultAsync(m => m.EvaluationPeriod == evaluationPeriod);

            if (existing == null)
            {
                var monthlyDays = new MonthlyWorkingDays
                {
                    EvaluationPeriod = evaluationPeriod,
                    TotalWorkingDays = 22, // Typical working days in a month
                    Holidays = 2,
                    Weekends = 8,
                    Comments = "Sample data for testing",
                    ConfiguredBy = "System"
                };

                _context.MonthlyWorkingDays.Add(monthlyDays);
            }
        }

        private async Task SeedDepartmentWorkVolumeTotals(string evaluationPeriod, int departmentId)
        {
            // Remove any existing department totals to recalculate with fresh data
            var existing = await _context.DepartmentWorkVolumeTotals
                .FirstOrDefaultAsync(d => d.DepartmentId == departmentId && d.EvaluationPeriod == evaluationPeriod);

            if (existing != null)
            {
                _logger.LogInformation($"Removing existing department work volume total for department {departmentId} to recalculate");
                _context.DepartmentWorkVolumeTotals.Remove(existing);
                await _context.SaveChangesAsync();
            }
            // Calculate totals based on actual employee work volumes in the department
            var employeeWorkVolumes = await _context.WorkVolumeData
                .Where(w => w.DepartmentId == departmentId && w.EvaluationPeriod == evaluationPeriod)
                .ToListAsync();

            _logger.LogInformation($"Found {employeeWorkVolumes.Count} employee work volume records for department {departmentId}, period {evaluationPeriod}");

            var totalQuality = employeeWorkVolumes.Sum(w => w.QualityProgramWork);
            var totalOracle = employeeWorkVolumes.Sum(w => w.OracleWork);
            var totalDocumented = employeeWorkVolumes.Sum(w => w.DocumentedWork);

            _logger.LogInformation($"Calculated department totals - Quality: {totalQuality}, Oracle: {totalOracle}, Documented: {totalDocumented}");

            // If no employee data exists yet, use reasonable defaults
            if (!employeeWorkVolumes.Any())
            {
                _logger.LogWarning($"No employee work volume data found for department {departmentId}, using defaults");
                totalQuality = 1000m;
                totalOracle = 800m;
                totalDocumented = 600m;
            }

            var departmentTotal = new DepartmentWorkVolumeTotal
            {
                DepartmentId = departmentId,
                EvaluationPeriod = evaluationPeriod,
                TotalQualityProgramWork = totalQuality,
                TotalOracleWork = totalOracle,
                TotalDocumentedWork = totalDocumented,
                RecordedBy = "System"
            };

            _context.DepartmentWorkVolumeTotals.Add(departmentTotal);
            await _context.SaveChangesAsync(); // Save immediately to ensure data is persisted

            _logger.LogInformation($"Added department work volume total for department {departmentId}:");
            _logger.LogInformation($"  - Quality: {departmentTotal.TotalQualityProgramWork}");
            _logger.LogInformation($"  - Oracle: {departmentTotal.TotalOracleWork}");
            _logger.LogInformation($"  - Documented: {departmentTotal.TotalDocumentedWork}");
            _logger.LogInformation($"  - Total (calculated): {departmentTotal.TotalDepartmentWork}");

            // Verify the data was saved correctly
            var savedTotal = await _context.DepartmentWorkVolumeTotals
                .FirstOrDefaultAsync(dt => dt.DepartmentId == departmentId && dt.EvaluationPeriod == evaluationPeriod);

            if (savedTotal != null)
            {
                _logger.LogInformation($"Verification: Saved department total = {savedTotal.TotalDepartmentWork}");
            }
            else
            {
                _logger.LogError($"Failed to save department work volume total for department {departmentId}");
            }
        }

        private async Task ClearExistingDataForDepartment(string evaluationPeriod, int departmentId)
        {
            _logger.LogInformation($"Clearing existing evaluation data for period {evaluationPeriod}, department {departmentId}");

            // Clear department work volume totals
            var departmentTotals = await _context.DepartmentWorkVolumeTotals
                .Where(d => d.EvaluationPeriod == evaluationPeriod && d.DepartmentId == departmentId)
                .ToListAsync();
            _context.DepartmentWorkVolumeTotals.RemoveRange(departmentTotals);

            // Clear work volume data
            var workVolumeData = await _context.WorkVolumeData
                .Where(w => w.EvaluationPeriod == evaluationPeriod && w.DepartmentId == departmentId)
                .ToListAsync();
            _context.WorkVolumeData.RemoveRange(workVolumeData);

            // Clear comprehensive evaluations
            var comprehensiveEvals = await _context.ComprehensiveEvaluations
                .Where(c => c.EvaluationPeriod == evaluationPeriod && c.DepartmentId == departmentId)
                .ToListAsync();
            _context.ComprehensiveEvaluations.RemoveRange(comprehensiveEvals);

            await _context.SaveChangesAsync();
            _logger.LogInformation($"Cleared {departmentTotals.Count} department totals, {workVolumeData.Count} work volume records, and {comprehensiveEvals.Count} comprehensive evaluations");
        }

        private async Task SeedEmployeeWorkVolumeData(string evaluationPeriod, int departmentId, List<ApplicationUser> employees)
        {
            var random = new Random(42); // Fixed seed for consistent results

            foreach (var employee in employees)
            {
                var existing = await _context.WorkVolumeData
                    .FirstOrDefaultAsync(w => w.EmployeeId == employee.Id && w.EvaluationPeriod == evaluationPeriod);

                if (existing == null)
                {
                    var workData = new WorkVolumeData
                    {
                        EmployeeId = employee.Id,
                        DepartmentId = departmentId,
                        EvaluationPeriod = evaluationPeriod,
                        QualityProgramWork = (decimal)(random.NextDouble() * 200 + 50), // 50-250
                        OracleWork = (decimal)(random.NextDouble() * 150 + 30), // 30-180
                        DocumentedWork = (decimal)(random.NextDouble() * 100 + 20), // 20-120
                        Comments = "Sample work volume data",
                        RecordedBy = "System"
                    };

                    _context.WorkVolumeData.Add(workData);
                }
            }
        }

        private async Task SeedAttendanceData(string evaluationPeriod, int departmentId, List<ApplicationUser> employees)
        {
            var random = new Random(42);

            foreach (var employee in employees)
            {
                var existing = await _context.AttendanceData
                    .FirstOrDefaultAsync(a => a.EmployeeId == employee.Id && a.EvaluationPeriod == evaluationPeriod);

                if (existing == null)
                {
                    var attendanceData = new AttendanceData
                    {
                        EmployeeId = employee.Id,
                        DepartmentId = departmentId,
                        EvaluationPeriod = evaluationPeriod,
                        AttendanceDays = random.Next(18, 23), // 18-22 days out of 22
                        TotalWorkingDays = 22,
                        AbsenceDays = random.Next(0, 4),
                        LateArrivals = random.Next(0, 5),
                        EarlyDepartures = random.Next(0, 3),
                        Comments = "Sample attendance data",
                        RecordedBy = "System"
                    };

                    _context.AttendanceData.Add(attendanceData);
                }
            }
        }

        private async Task SeedSupervisorEvaluations(string evaluationPeriod, int departmentId, List<ApplicationUser> employees)
        {
            var random = new Random(42);
            
            // Get a supervisor (first manager or admin)
            var supervisor = await _context.Users
                .FirstOrDefaultAsync(u => (u.Role == UserRole.MANAGER || u.Role == UserRole.SUPER_ADMIN) && u.IsActive);

            if (supervisor == null)
            {
                _logger.LogWarning("No supervisor found for seeding supervisor evaluations");
                return;
            }

            foreach (var employee in employees)
            {
                var existing = await _context.SupervisorEvaluations
                    .FirstOrDefaultAsync(s => s.EmployeeId == employee.Id && s.EvaluationPeriod == evaluationPeriod);

                if (existing == null)
                {
                    var evaluation = new SupervisorEvaluation
                    {
                        EmployeeId = employee.Id,
                        SupervisorId = supervisor.Id,
                        DepartmentId = departmentId,
                        EvaluationPeriod = evaluationPeriod,
                        EfficiencyAndQualityScore = (decimal)(random.NextDouble() * 2 + 3), // 3-5
                        LeadershipAbilityScore = (decimal)(random.NextDouble() * 2 + 3), // 3-5
                        PlanningAndInnovationScore = (decimal)(random.NextDouble() * 2 + 3), // 3-5
                        TeamworkParticipationScore = (decimal)(random.NextDouble() * 2 + 3), // 3-5
                        ResponsibilityAndPressureScore = (decimal)(random.NextDouble() * 2 + 3), // 3-5
                        EmergencyHandlingScore = (decimal)(random.NextDouble() * 2 + 3), // 3-5
                        GeneralBehaviorScore = (decimal)(random.NextDouble() * 2 + 3), // 3-5
                        RelationshipWithSuperiorsScore = (decimal)(random.NextDouble() * 2 + 3), // 3-5
                        DisciplineAndCommitmentScore = (decimal)(random.NextDouble() * 2 + 3), // 3-5
                        WorkDevelopmentScore = (decimal)(random.NextDouble() * 2 + 3), // 3-5
                        Comments = "Sample supervisor evaluation",
                        Status = EvaluationStatus.APPROVED
                    };

                    _context.SupervisorEvaluations.Add(evaluation);
                }
            }
        }

        /// <summary>
        /// Generate comprehensive evaluations based on seeded data
        /// </summary>
        private async Task GenerateComprehensiveEvaluationsAsync(string evaluationPeriod)
        {
            var employees = await _context.Users
                .Where(u => u.IsActive && !u.IsDeleted && u.PrimaryDepartmentId.HasValue)
                .ToListAsync();

            foreach (var employee in employees)
            {
                var existing = await _context.ComprehensiveEvaluations
                    .FirstOrDefaultAsync(ce => ce.EmployeeId == employee.Id && ce.EvaluationPeriod == evaluationPeriod);

                if (existing == null)
                {
                    // Get related data
                    var workVolume = await _context.WorkVolumeData
                        .FirstOrDefaultAsync(w => w.EmployeeId == employee.Id && w.EvaluationPeriod == evaluationPeriod);

                    var attendance = await _context.AttendanceData
                        .FirstOrDefaultAsync(a => a.EmployeeId == employee.Id && a.EvaluationPeriod == evaluationPeriod);

                    var supervisorEval = await _context.SupervisorEvaluations
                        .FirstOrDefaultAsync(s => s.EmployeeId == employee.Id && s.EvaluationPeriod == evaluationPeriod);

                    if (workVolume != null && attendance != null && supervisorEval != null)
                    {
                        var attendancePercentage = (decimal)attendance.AttendanceDays / attendance.TotalWorkingDays;
                        var workVolumeScore = (workVolume.TotalEmployeeWork / 500m) * 0.6m; // 60% weight for work volume
                        var attendanceScore = attendancePercentage * 0.2m; // 20% weight for attendance
                        var supervisorScore = (supervisorEval.TotalScore / 5m) * 0.2m; // 20% weight for supervisor evaluation

                        var totalScore = workVolumeScore + attendanceScore + supervisorScore;

                        var comprehensiveEval = new ComprehensiveEvaluation
                        {
                            EmployeeId = employee.Id,
                            DepartmentId = employee.PrimaryDepartmentId.Value,
                            EvaluationPeriod = evaluationPeriod,
                            WorkVolumeScore = workVolumeScore,
                            WorkVolumePercentage = (workVolume?.TotalEmployeeWork ?? 0) / 500m * 100, // Calculate percentage
                            AttendanceScore = attendanceScore,
                            AttendancePercentage = attendancePercentage,
                            SupervisorScore = supervisorScore,
                            TotalScore = totalScore,
                            Status = EvaluationStatus.APPROVED,
                            CalculatedBy = "System",
                            CalculatedAt = DateTime.UtcNow
                        };

                        _context.ComprehensiveEvaluations.Add(comprehensiveEval);
                    }
                }
            }
        }

        /// <summary>
        /// Seed comprehensive evaluation data for all employees for a specific period
        /// </summary>
        public async Task SeedComprehensiveEvaluationDataAsync(string evaluationPeriod)
        {
            _logger.LogInformation($"Seeding comprehensive evaluation data for period: {evaluationPeriod}");

            // Get all active employees
            var employees = await _context.Users
                .Where(u => u.IsActive && !u.IsDeleted && u.PrimaryDepartmentId.HasValue)
                .ToListAsync();

            _logger.LogInformation($"Found {employees.Count} active employees to evaluate");

            foreach (var employee in employees)
            {
                // Check if comprehensive evaluation already exists
                var existing = await _context.ComprehensiveEvaluations
                    .FirstOrDefaultAsync(ce => ce.EmployeeId == employee.Id && ce.EvaluationPeriod == evaluationPeriod);

                if (existing != null)
                {
                    _logger.LogInformation($"Comprehensive evaluation already exists for {employee.EnglishName}");
                    continue;
                }

                // Seed work volume data
                await SeedEmployeeWorkVolumeData(evaluationPeriod, employee.PrimaryDepartmentId.Value, employee.Id);

                // Seed attendance data
                await SeedEmployeeAttendanceData(evaluationPeriod, employee.PrimaryDepartmentId.Value, employee.Id);

                // Seed supervisor evaluation
                await SeedEmployeeSupervisorEvaluation(evaluationPeriod, employee.PrimaryDepartmentId.Value, employee.Id);

                // Generate comprehensive evaluation
                await _evaluationCalculationService.CalculateEmployeeEvaluationAsync(employee.Id, evaluationPeriod);

                _logger.LogInformation($"Created comprehensive evaluation for {employee.EnglishName}");
            }

            // Seed department work volume totals
            var departments = await _context.Departments.Where(d => d.IsActive).ToListAsync();
            foreach (var department in departments)
            {
                await SeedDepartmentWorkVolumeTotals(evaluationPeriod, department.Id);
            }

            _logger.LogInformation($"Comprehensive evaluation data seeding completed for period: {evaluationPeriod}");
        }

        private async Task SeedEmployeeWorkVolumeData(string evaluationPeriod, int departmentId, string employeeId)
        {
            var existing = await _context.WorkVolumeData
                .FirstOrDefaultAsync(w => w.EmployeeId == employeeId && w.EvaluationPeriod == evaluationPeriod);

            if (existing == null)
            {
                var random = new Random();
                var workVolumeData = new WorkVolumeData
                {
                    EmployeeId = employeeId,
                    DepartmentId = departmentId,
                    EvaluationPeriod = evaluationPeriod,
                    QualityProgramWork = random.Next(50, 200),
                    OracleWork = random.Next(30, 150),
                    DocumentedWork = random.Next(20, 100),
                    RecordedBy = "system",
                    RecordedAt = DateTime.UtcNow
                };

                _context.WorkVolumeData.Add(workVolumeData);
                await _context.SaveChangesAsync();
            }
        }

        private async Task SeedEmployeeAttendanceData(string evaluationPeriod, int departmentId, string employeeId)
        {
            var existing = await _context.AttendanceData
                .FirstOrDefaultAsync(a => a.EmployeeId == employeeId && a.EvaluationPeriod == evaluationPeriod);

            if (existing == null)
            {
                var random = new Random();
                var totalWorkingDays = 22; // Typical month
                var attendanceDays = random.Next(18, 23); // 18-22 days (82-100% attendance)

                var attendanceData = new AttendanceData
                {
                    EmployeeId = employeeId,
                    DepartmentId = departmentId,
                    EvaluationPeriod = evaluationPeriod,
                    TotalWorkingDays = totalWorkingDays,
                    AttendanceDays = attendanceDays,
                    AbsenceDays = totalWorkingDays - attendanceDays,
                    LateArrivals = random.Next(0, 3),
                    EarlyDepartures = random.Next(0, 2),
                    RecordedBy = "system",
                    RecordedAt = DateTime.UtcNow
                };

                _context.AttendanceData.Add(attendanceData);
                await _context.SaveChangesAsync();
            }
        }

        private async Task SeedEmployeeSupervisorEvaluation(string evaluationPeriod, int departmentId, string employeeId)
        {
            var existing = await _context.SupervisorEvaluations
                .FirstOrDefaultAsync(s => s.EmployeeId == employeeId && s.EvaluationPeriod == evaluationPeriod);

            if (existing == null)
            {
                // Find a supervisor for this department
                var supervisor = await _context.Users
                    .FirstOrDefaultAsync(u => u.PrimaryDepartmentId == departmentId &&
                                            (u.Role == UserRole.SUPERVISOR || u.Role == UserRole.MANAGER) &&
                                            u.IsActive && !u.IsDeleted);

                if (supervisor != null)
                {
                    var random = new Random();
                    var supervisorEval = new SupervisorEvaluation
                    {
                        EmployeeId = employeeId,
                        SupervisorId = supervisor.Id,
                        DepartmentId = departmentId,
                        EvaluationPeriod = evaluationPeriod,
                        EfficiencyAndQualityScore = random.Next(3, 6), // 3-5 points
                        LeadershipAbilityScore = random.Next(3, 6),
                        PlanningAndInnovationScore = random.Next(3, 6),
                        TeamworkParticipationScore = random.Next(3, 6),
                        ResponsibilityAndPressureScore = random.Next(3, 6),
                        EmergencyHandlingScore = random.Next(3, 6),
                        GeneralBehaviorScore = random.Next(3, 6),
                        RelationshipWithSuperiorsScore = random.Next(3, 6),
                        DisciplineAndCommitmentScore = random.Next(3, 6),
                        WorkDevelopmentScore = random.Next(3, 6),
                        Status = EvaluationStatus.APPROVED,
                        SubmittedAt = DateTime.UtcNow,
                        ApprovedAt = DateTime.UtcNow,
                        ApprovedBy = supervisor.Id
                    };

                    _context.SupervisorEvaluations.Add(supervisorEval);
                    await _context.SaveChangesAsync();
                }
            }
        }
    }
}
