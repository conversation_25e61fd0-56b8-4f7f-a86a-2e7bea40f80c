﻿@page "/"
@using System.Globalization
@using EmployeeRatingSystem.Blazor.Components.Shared
@using EmployeeRatingSystem.Blazor.Services
@inherits LocalizedComponentBase

<PageTitle>@GetPageTitle("Home", "الرئيسية")</PageTitle>

<div class="hero-section text-center py-5 mb-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-4 text-primary">
                    @L(LocalizedStrings.EmployeeRatingSystem)
                </h1>
                <p class="lead mb-5 text-secondary">
                    @L("Comprehensive employee performance evaluation system with Arabic and English support",
                       "نظام شامل لتقييم أداء الموظفين مع دعم اللغة العربية والإنجليزية")
                </p>
                <div class="d-flex gap-3 justify-content-center flex-wrap">
                    <a href="/dashboard" class="btn btn-primary btn-lg px-4 py-3">
                        <i class="fas fa-tachometer-alt @GetMarginEnd()"></i>
                        @L(LocalizedStrings.Dashboard)
                    </a>
                    <a href="/login" class="btn btn-outline-primary btn-lg px-4 py-3">
                        <i class="fas fa-sign-in-alt @GetMarginEnd()"></i>
                        @L(LocalizedStrings.Login)
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>



@code {
    // All localization functionality is now inherited from LocalizedComponentBase
}
