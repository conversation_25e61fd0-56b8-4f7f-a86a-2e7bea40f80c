using EmployeeRatingSystem.Blazor.Data;
using EmployeeRatingSystem.Blazor.Models;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.EntityFrameworkCore;

namespace EmployeeRatingSystem.Blazor.Services
{
    /// <summary>
    /// Service for managing evaluation file attachments
    /// </summary>
    public interface IEvaluationAttachmentService
    {
        Task<EvaluationAttachment> UploadAttachmentAsync(int evaluationId, IBrowserFile file, string attachmentType, string descriptionEn, string descriptionAr, string uploadedBy);
        Task<EvaluationAttachment> UploadSupervisorEvaluationAttachmentAsync(int supervisorEvaluationId, IBrowserFile file, string attachmentType, string descriptionEn, string descriptionAr, string uploadedBy);
        Task<List<EvaluationAttachment>> GetAttachmentsByEvaluationIdAsync(int evaluationId);
        Task<List<EvaluationAttachment>> GetAttachmentsBySupervisorEvaluationIdAsync(int supervisorEvaluationId);
        Task<List<EvaluationAttachment>> GetAttachmentsByTypeAsync(int evaluationId, string attachmentType);
        Task<List<EvaluationAttachment>> GetSupervisorEvaluationAttachmentsByTypeAsync(int supervisorEvaluationId, string attachmentType);
        Task<bool> DeleteAttachmentAsync(int attachmentId, string userId);
        Task<EvaluationAttachment?> GetAttachmentByIdAsync(int attachmentId);
        Task<byte[]> GetAttachmentContentAsync(int attachmentId);
        Task<int> GetAttachmentCountAsync(int evaluationId, string? attachmentType = null);
        Task<int> GetSupervisorEvaluationAttachmentCountAsync(int supervisorEvaluationId, string? attachmentType = null);
    }

    public class EvaluationAttachmentService : IEvaluationAttachmentService
    {
        private readonly ApplicationDbContext _context;
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<EvaluationAttachmentService> _logger;

        // Maximum file size: 10MB
        private const long MaxFileSize = 10 * 1024 * 1024;

        // Allowed file extensions
        private static readonly string[] AllowedExtensions = { ".pdf", ".doc", ".docx", ".jpg", ".jpeg", ".png", ".txt", ".xlsx", ".xls" };

        public EvaluationAttachmentService(
            ApplicationDbContext context,
            IWebHostEnvironment environment,
            ILogger<EvaluationAttachmentService> logger)
        {
            _context = context;
            _environment = environment;
            _logger = logger;
        }

        /// <summary>
        /// Upload a new attachment for evaluation exceptional work documentation
        /// </summary>
        public async Task<EvaluationAttachment> UploadAttachmentAsync(
            int evaluationId,
            IBrowserFile file,
            string attachmentType,
            string descriptionEn,
            string descriptionAr,
            string uploadedBy)
        {
            try
            {
                _logger.LogInformation($"Starting file upload for Evaluation ID {evaluationId}, file: {file?.Name}");

                if (file == null || file.Size == 0)
                    throw new ArgumentException("File is required");

                if (file.Size > MaxFileSize)
                    throw new ArgumentException($"File size exceeds maximum limit of {MaxFileSize / (1024 * 1024)}MB");

                var fileExtension = Path.GetExtension(file.Name).ToLowerInvariant();
                if (!AllowedExtensions.Contains(fileExtension))
                    throw new ArgumentException($"File type {fileExtension} is not allowed");

                // Verify evaluation exists
                var evaluation = await _context.Evaluations.FindAsync(evaluationId);
                if (evaluation == null)
                    throw new ArgumentException($"Evaluation with ID {evaluationId} not found");

                _logger.LogInformation($"Evaluation found: {evaluation.EmployeeId}");

                // Create uploads directory if it doesn't exist
                var uploadsPath = Path.Combine(_environment.WebRootPath, "uploads", "evaluations");
                _logger.LogInformation($"Creating uploads directory: {uploadsPath}");
                Directory.CreateDirectory(uploadsPath);

                // Generate unique filename
                var uniqueFileName = $"{Guid.NewGuid()}{fileExtension}";
                var filePath = Path.Combine(uploadsPath, uniqueFileName);
                _logger.LogInformation($"Generated file path: {filePath}");

                // Save file to disk
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.OpenReadStream(MaxFileSize).CopyToAsync(stream);
                }
                _logger.LogInformation($"File saved to disk: {filePath}");

                // Create attachment record
                var attachment = new EvaluationAttachment
                {
                    EvaluationId = evaluationId,
                    AttachmentType = attachmentType,
                    FileName = file.Name,
                    FilePath = Path.Combine("uploads", "evaluations", uniqueFileName),
                    FileSize = file.Size,
                    ContentType = file.ContentType,
                    DescriptionEn = descriptionEn ?? string.Empty,
                    DescriptionAr = descriptionAr ?? string.Empty,
                    UploadedBy = uploadedBy,
                    UploadedAt = DateTime.UtcNow,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.EvaluationAttachments.Add(attachment);
                await _context.SaveChangesAsync();

                _logger.LogInformation($"Successfully uploaded attachment {file.Name} for Evaluation ID {evaluationId}");

                return attachment;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error uploading attachment for Evaluation ID {evaluationId}");
                throw;
            }
        }

        /// <summary>
        /// Upload a new attachment for supervisor evaluation exceptional work documentation
        /// </summary>
        public async Task<EvaluationAttachment> UploadSupervisorEvaluationAttachmentAsync(
            int supervisorEvaluationId,
            IBrowserFile file,
            string attachmentType,
            string descriptionEn,
            string descriptionAr,
            string uploadedBy)
        {
            try
            {
                _logger.LogInformation($"Starting file upload for Supervisor Evaluation ID {supervisorEvaluationId}, file: {file?.Name}");

                if (file == null || file.Size == 0)
                    throw new ArgumentException("File is required");

                if (file.Size > MaxFileSize)
                    throw new ArgumentException($"File size exceeds maximum limit of {MaxFileSize / (1024 * 1024)}MB");

                var fileExtension = Path.GetExtension(file.Name).ToLowerInvariant();
                if (!AllowedExtensions.Contains(fileExtension))
                    throw new ArgumentException($"File type {fileExtension} is not allowed");

                // Verify supervisor evaluation exists
                var supervisorEvaluation = await _context.SupervisorEvaluations.FindAsync(supervisorEvaluationId);
                if (supervisorEvaluation == null)
                    throw new ArgumentException($"Supervisor Evaluation with ID {supervisorEvaluationId} not found");

                _logger.LogInformation($"Supervisor Evaluation found: {supervisorEvaluation.EmployeeId}");

                // Create uploads directory if it doesn't exist
                var uploadsPath = Path.Combine(_environment.WebRootPath, "uploads", "evaluations");
                _logger.LogInformation($"Creating uploads directory: {uploadsPath}");
                Directory.CreateDirectory(uploadsPath);

                // Generate unique filename
                var uniqueFileName = $"{Guid.NewGuid()}{fileExtension}";
                var filePath = Path.Combine(uploadsPath, uniqueFileName);
                _logger.LogInformation($"Generated file path: {filePath}");

                // Save file to disk
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.OpenReadStream(MaxFileSize).CopyToAsync(stream);
                }
                _logger.LogInformation($"File saved to disk: {filePath}");

                // Create attachment record
                var attachment = new EvaluationAttachment
                {
                    SupervisorEvaluationId = supervisorEvaluationId,
                    AttachmentType = attachmentType,
                    FileName = file.Name,
                    FilePath = Path.Combine("uploads", "evaluations", uniqueFileName),
                    FileSize = file.Size,
                    ContentType = file.ContentType,
                    DescriptionEn = descriptionEn ?? string.Empty,
                    DescriptionAr = descriptionAr ?? string.Empty,
                    UploadedBy = uploadedBy,
                    UploadedAt = DateTime.UtcNow,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.EvaluationAttachments.Add(attachment);
                await _context.SaveChangesAsync();

                _logger.LogInformation($"Successfully uploaded attachment {file.Name} for Supervisor Evaluation ID {supervisorEvaluationId}");

                return attachment;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error uploading attachment for Supervisor Evaluation ID {supervisorEvaluationId}");
                throw;
            }
        }

        /// <summary>
        /// Get all attachments for a specific evaluation
        /// </summary>
        public async Task<List<EvaluationAttachment>> GetAttachmentsByEvaluationIdAsync(int evaluationId)
        {
            return await _context.EvaluationAttachments
                .Include(a => a.Uploader)
                .Where(a => a.EvaluationId == evaluationId && a.IsActive)
                .OrderByDescending(a => a.UploadedAt)
                .ToListAsync();
        }

        /// <summary>
        /// Get all attachments for a specific supervisor evaluation
        /// </summary>
        public async Task<List<EvaluationAttachment>> GetAttachmentsBySupervisorEvaluationIdAsync(int supervisorEvaluationId)
        {
            return await _context.EvaluationAttachments
                .Include(a => a.Uploader)
                .Where(a => a.SupervisorEvaluationId == supervisorEvaluationId && a.IsActive)
                .OrderByDescending(a => a.UploadedAt)
                .ToListAsync();
        }

        /// <summary>
        /// Get attachments by evaluation ID and type
        /// </summary>
        public async Task<List<EvaluationAttachment>> GetAttachmentsByTypeAsync(int evaluationId, string attachmentType)
        {
            return await _context.EvaluationAttachments
                .Include(a => a.Uploader)
                .Where(a => a.EvaluationId == evaluationId && a.AttachmentType == attachmentType && a.IsActive)
                .OrderByDescending(a => a.UploadedAt)
                .ToListAsync();
        }

        /// <summary>
        /// Get supervisor evaluation attachments by type
        /// </summary>
        public async Task<List<EvaluationAttachment>> GetSupervisorEvaluationAttachmentsByTypeAsync(int supervisorEvaluationId, string attachmentType)
        {
            return await _context.EvaluationAttachments
                .Include(a => a.Uploader)
                .Where(a => a.SupervisorEvaluationId == supervisorEvaluationId && a.AttachmentType == attachmentType && a.IsActive)
                .OrderByDescending(a => a.UploadedAt)
                .ToListAsync();
        }

        /// <summary>
        /// Delete an attachment (soft delete)
        /// </summary>
        public async Task<bool> DeleteAttachmentAsync(int attachmentId, string userId)
        {
            try
            {
                var attachment = await _context.EvaluationAttachments.FindAsync(attachmentId);
                if (attachment == null || !attachment.IsActive)
                    return false;

                // Soft delete
                attachment.IsActive = false;
                attachment.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation($"Attachment {attachmentId} soft deleted by user {userId}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting attachment {attachmentId}");
                return false;
            }
        }

        /// <summary>
        /// Get attachment by ID
        /// </summary>
        public async Task<EvaluationAttachment?> GetAttachmentByIdAsync(int attachmentId)
        {
            return await _context.EvaluationAttachments
                .Include(a => a.Uploader)
                .Include(a => a.Evaluation)
                .FirstOrDefaultAsync(a => a.Id == attachmentId && a.IsActive);
        }

        /// <summary>
        /// Get attachment file content
        /// </summary>
        public async Task<byte[]> GetAttachmentContentAsync(int attachmentId)
        {
            var attachment = await GetAttachmentByIdAsync(attachmentId);
            if (attachment == null)
                throw new FileNotFoundException("Attachment not found");

            var fullPath = Path.Combine(_environment.WebRootPath, attachment.FilePath);
            if (!File.Exists(fullPath))
                throw new FileNotFoundException("File not found on disk");

            return await File.ReadAllBytesAsync(fullPath);
        }

        /// <summary>
        /// Get count of attachments for an evaluation
        /// </summary>
        public async Task<int> GetAttachmentCountAsync(int evaluationId, string? attachmentType = null)
        {
            var query = _context.EvaluationAttachments
                .Where(a => a.EvaluationId == evaluationId && a.IsActive);

            if (!string.IsNullOrEmpty(attachmentType))
            {
                query = query.Where(a => a.AttachmentType == attachmentType);
            }

            return await query.CountAsync();
        }

        /// <summary>
        /// Get count of attachments for a supervisor evaluation
        /// </summary>
        public async Task<int> GetSupervisorEvaluationAttachmentCountAsync(int supervisorEvaluationId, string? attachmentType = null)
        {
            var query = _context.EvaluationAttachments
                .Where(a => a.SupervisorEvaluationId == supervisorEvaluationId && a.IsActive);

            if (!string.IsNullOrEmpty(attachmentType))
            {
                query = query.Where(a => a.AttachmentType == attachmentType);
            }

            return await query.CountAsync();
        }
    }
}
