@page "/debug/user-registration"
@using Microsoft.AspNetCore.Identity
@using Microsoft.EntityFrameworkCore
@using EmployeeRatingSystem.Blazor.Models
@using EmployeeRatingSystem.Blazor.Data
@using EmployeeRatingSystem.Blazor.Components.Shared

@inherits LocalizedComponentBase
@inject ApplicationDbContext DbContext
@inject UserManager<ApplicationUser> UserManager
@inject RoleManager<IdentityRole> RoleManager
@inject ILogger<UserRegistrationDiagnostic> Logger

<PageTitle>User Registration Diagnostic</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-user-check me-2"></i>
                    User Registration Diagnostic & Fix
                </h1>
                <div class="btn-group">
                    <button class="btn btn-primary" @onclick="LoadData" disabled="@isLoading">
                        <i class="fas fa-sync-alt me-2"></i>
                        Refresh Data
                    </button>
                    <button class="btn btn-success" @onclick="CreateMissingUsers" disabled="@isLoading">
                        <i class="fas fa-user-plus me-2"></i>
                        Create Missing Users
                    </button>
                </div>
            </div>

            @if (!string.IsNullOrEmpty(message))
            {
                <div class="alert alert-@(isError ? "danger" : "success") alert-dismissible fade show">
                    <i class="fas fa-@(isError ? "exclamation-triangle" : "check-circle") me-2"></i>
                    @message
                    <button type="button" class="btn-close" @onclick="ClearMessage"></button>
                </div>
            }

            <!-- Expected vs Actual Users -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-list-check me-2"></i>
                                Expected Users (From DataSeedingService)
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Employee ID</th>
                                            <th>Name</th>
                                            <th>Role</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var expectedUser in expectedUsers)
                                        {
                                            var exists = actualUsers.Any(u => u.EmployeeId == expectedUser.EmployeeId);
                                            <tr class="@(exists ? "table-success" : "table-danger")">
                                                <td><strong>@expectedUser.EmployeeId</strong></td>
                                                <td>@expectedUser.EnglishName</td>
                                                <td>@expectedUser.Role</td>
                                                <td>
                                                    @if (exists)
                                                    {
                                                        <span class="badge bg-success">EXISTS</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-danger">MISSING</span>
                                                    }
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-database me-2"></i>
                                Actual Users (From Database)
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Employee ID</th>
                                            <th>Name</th>
                                            <th>Has Password</th>
                                            <th>Identity Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var user in actualUsers)
                                        {
                                            var isExpected = expectedUsers.Any(u => u.EmployeeId == user.EmployeeId);
                                            <tr class="@(isExpected ? "table-success" : "table-warning")">
                                                <td><strong>@user.EmployeeId</strong></td>
                                                <td>@user.EnglishName</td>
                                                <td>
                                                    @if (!string.IsNullOrEmpty(user.PasswordHash))
                                                    {
                                                        <span class="badge bg-success">YES</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-danger">NO</span>
                                                    }
                                                </td>
                                                <td>
                                                    @if (!string.IsNullOrEmpty(user.SecurityStamp) && !string.IsNullOrEmpty(user.ConcurrencyStamp))
                                                    {
                                                        <span class="badge bg-success">PROPER</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-danger">INCOMPLETE</span>
                                                    }
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Missing Users Analysis -->
            @if (missingUsers.Any())
            {
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Missing Users Analysis
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <strong>Found @missingUsers.Count missing users:</strong>
                            @string.Join(", ", missingUsers.Select(u => u.EmployeeId))
                        </div>
                        <p>These users are expected by the DataSeedingService but are not properly registered in the Identity system.</p>
                    </div>
                </div>
            }

            <!-- Incomplete Users Analysis -->
            @if (incompleteUsers.Any())
            {
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-user-times me-2"></i>
                            Incomplete Identity Registration
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-danger">
                            <strong>Found @incompleteUsers.Count users with incomplete Identity registration:</strong>
                            @string.Join(", ", incompleteUsers.Select(u => u.EmployeeId))
                        </div>
                        <p>These users exist in the database but lack proper Identity fields (PasswordHash, SecurityStamp, etc.)</p>
                    </div>
                </div>
            }

            <!-- Debug Logs -->
            @if (debugLogs.Any())
            {
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-terminal me-2"></i>
                            Debug Logs
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="bg-dark text-light p-3 rounded" style="max-height: 300px; overflow-y: auto;">
                            @foreach (var log in debugLogs.TakeLast(50))
                            {
                                <div class="mb-1">
                                    <small class="text-muted">[@log.Timestamp.ToString("HH:mm:ss")]</small>
                                    <span>@log.Message</span>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@code {
    private List<ApplicationUser> expectedUsers = new();
    private List<ApplicationUser> actualUsers = new();
    private List<ApplicationUser> missingUsers = new();
    private List<ApplicationUser> incompleteUsers = new();
    private List<DebugLog> debugLogs = new();
    private string message = string.Empty;
    private bool isError = false;
    private bool isLoading = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        isLoading = true;
        try
        {
            AddLog("Loading user registration data...");
            
            // Load expected users (from DataSeedingService logic)
            await LoadExpectedUsers();
            
            // Load actual users from database
            await LoadActualUsers();
            
            // Analyze differences
            AnalyzeDifferences();
            
            AddLog($"Analysis complete: {expectedUsers.Count} expected, {actualUsers.Count} actual, {missingUsers.Count} missing, {incompleteUsers.Count} incomplete");
        }
        catch (Exception ex)
        {
            AddLog($"Error loading data: {ex.Message}");
            ShowMessage($"Error loading data: {ex.Message}", true);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadExpectedUsers()
    {
        AddLog("Loading expected users from DataSeedingService specification...");
        
        // Get departments (needed for user creation)
        var itDepartment = await DbContext.Departments.FirstOrDefaultAsync(d => d.Code == "IT");
        var hrDepartment = await DbContext.Departments.FirstOrDefaultAsync(d => d.Code == "HR");
        
        if (itDepartment == null || hrDepartment == null)
        {
            AddLog("Warning: IT or HR department not found. This may cause issues.");
        }

        // These are the users that SHOULD exist according to DataSeedingService
        expectedUsers = new List<ApplicationUser>
        {
            new ApplicationUser
            {
                EmployeeId = "EMP001",
                EnglishName = "System Administrator",
                ArabicName = "مدير النظام",
                Email = "<EMAIL>",
                UserName = "<EMAIL>",
                Role = UserRole.SUPER_ADMIN,
                IsActive = true,
                PrimaryDepartmentId = itDepartment?.Id,
                PreferredLanguage = "en"
            },
            new ApplicationUser
            {
                EmployeeId = "EMP002",
                EnglishName = "Sarah Ahmed",
                ArabicName = "سارة أحمد",
                Email = "<EMAIL>",
                UserName = "<EMAIL>",
                Role = UserRole.MANAGER,
                IsActive = true,
                PrimaryDepartmentId = hrDepartment?.Id,
                PreferredLanguage = "ar"
            },
            new ApplicationUser
            {
                EmployeeId = "EMP003",
                EnglishName = "Mohamed Hassan",
                ArabicName = "محمد حسن",
                Email = "<EMAIL>",
                UserName = "<EMAIL>",
                Role = UserRole.SUPERVISOR,
                IsActive = true,
                PrimaryDepartmentId = itDepartment?.Id,
                PreferredLanguage = "ar"
            },
            new ApplicationUser
            {
                EmployeeId = "EMP004",
                EnglishName = "Ahmed Mohamed",
                ArabicName = "أحمد محمد",
                Email = "<EMAIL>",
                UserName = "<EMAIL>",
                Role = UserRole.EMPLOYEE,
                IsActive = true,
                PrimaryDepartmentId = itDepartment?.Id,
                PreferredLanguage = "ar"
            },
            new ApplicationUser
            {
                EmployeeId = "EMP005",
                EnglishName = "Fatima Ali",
                ArabicName = "فاطمة علي",
                Email = "<EMAIL>",
                UserName = "<EMAIL>",
                Role = UserRole.EMPLOYEE,
                IsActive = true,
                PrimaryDepartmentId = hrDepartment?.Id,
                PreferredLanguage = "ar"
            }
        };
        
        AddLog($"Expected users loaded: {expectedUsers.Count} users (EMP001-EMP005)");
    }

    private async Task LoadActualUsers()
    {
        AddLog("Loading actual users from database...");
        actualUsers = await UserManager.Users.OrderBy(u => u.EmployeeId).ToListAsync();
        AddLog($"Actual users loaded: {actualUsers.Count} users");
        
        foreach (var user in actualUsers)
        {
            AddLog($"  {user.EmployeeId}: {user.EnglishName}, HasPassword: {!string.IsNullOrEmpty(user.PasswordHash)}, " +
                   $"SecurityStamp: {!string.IsNullOrEmpty(user.SecurityStamp)}, IsActive: {user.IsActive}");
        }
    }

    private void AnalyzeDifferences()
    {
        AddLog("Analyzing differences between expected and actual users...");
        
        // Find missing users (expected but not in database)
        missingUsers = expectedUsers
            .Where(expected => !actualUsers.Any(actual => actual.EmployeeId == expected.EmployeeId))
            .ToList();
            
        // Find incomplete users (in database but missing Identity fields)
        incompleteUsers = actualUsers
            .Where(user => string.IsNullOrEmpty(user.PasswordHash) || 
                          string.IsNullOrEmpty(user.SecurityStamp) || 
                          string.IsNullOrEmpty(user.ConcurrencyStamp))
            .ToList();
            
        AddLog($"Missing users: {missingUsers.Count}");
        AddLog($"Incomplete users: {incompleteUsers.Count}");
    }

    private async Task CreateMissingUsers()
    {
        isLoading = true;
        try
        {
            AddLog("Starting user creation process...");
            
            int createdCount = 0;
            int fixedCount = 0;
            
            // Create missing users
            foreach (var missingUser in missingUsers)
            {
                AddLog($"Creating missing user: {missingUser.EmployeeId} - {missingUser.EnglishName}");
                
                var result = await UserManager.CreateAsync(missingUser, "Password123!");
                if (result.Succeeded)
                {
                    await UserManager.AddToRoleAsync(missingUser, missingUser.Role.ToString());
                    AddLog($"  ✓ Successfully created {missingUser.EmployeeId}");
                    createdCount++;
                }
                else
                {
                    AddLog($"  ✗ Failed to create {missingUser.EmployeeId}: {string.Join(", ", result.Errors.Select(e => e.Description))}");
                }
            }
            
            // Fix incomplete users
            foreach (var incompleteUser in incompleteUsers)
            {
                AddLog($"Fixing incomplete user: {incompleteUser.EmployeeId} - {incompleteUser.EnglishName}");
                
                // Reset password to ensure proper Identity fields are set
                var token = await UserManager.GeneratePasswordResetTokenAsync(incompleteUser);
                var result = await UserManager.ResetPasswordAsync(incompleteUser, token, "Password123!");
                
                if (result.Succeeded)
                {
                    AddLog($"  ✓ Successfully fixed {incompleteUser.EmployeeId}");
                    fixedCount++;
                }
                else
                {
                    AddLog($"  ✗ Failed to fix {incompleteUser.EmployeeId}: {string.Join(", ", result.Errors.Select(e => e.Description))}");
                }
            }
            
            AddLog($"User creation/fix process completed: {createdCount} created, {fixedCount} fixed");
            ShowMessage($"Successfully created {createdCount} users and fixed {fixedCount} incomplete users", false);
            
            // Reload data to show updated state
            await LoadData();
        }
        catch (Exception ex)
        {
            AddLog($"Error during user creation: {ex.Message}");
            ShowMessage($"Error during user creation: {ex.Message}", true);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void AddLog(string logMessage)
    {
        debugLogs.Add(new DebugLog { Message = logMessage, Timestamp = DateTime.Now });
        StateHasChanged();
    }

    private void ShowMessage(string msg, bool error)
    {
        message = msg;
        isError = error;
        StateHasChanged();
    }

    private void ClearMessage()
    {
        message = string.Empty;
        StateHasChanged();
    }

    private class DebugLog
    {
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }
}
