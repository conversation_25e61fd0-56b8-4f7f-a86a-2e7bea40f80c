@page "/register"
@rendermode InteractiveServer
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.Components.Authorization
@using EmployeeRatingSystem.Blazor.Models
@using EmployeeRatingSystem.Blazor.Components.Shared
@using EmployeeRatingSystem.Blazor.Services
@using System.ComponentModel.DataAnnotations

@inherits LocalizedComponentBase
@inject IEmployeeAuthenticationService EmployeeAuthService
@inject IEmployeeManagementService EmployeeManagementService
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider

<PageTitle>@GetPageTitle("Register", "التسجيل")</PageTitle>

<div class="register-page">
    <div class="container-fluid h-100">
        <div class="row h-100">
            <!-- Left Side - Branding -->
            <div class="col-lg-6 d-none d-lg-flex align-items-center justify-content-center bg-primary">
                <div class="text-center text-white p-5">
                    <div class="mb-4">
                        <i class="fas fa-user-plus fa-5x mb-4"></i>
                        <h1 class="display-4 fw-bold">
                            @L("Join Our Team", "انضم إلى فريقنا")
                        </h1>
                        <p class="lead">
                            @L("Create your account to access the Employee Rating System", "أنشئ حسابك للوصول إلى نظام تقييم الموظفين")
                        </p>
                    </div>
                    <div class="row text-center">
                        <div class="col-4">
                            <i class="fas fa-shield-alt fa-2x mb-2"></i>
                            <h6>@L("Secure", "آمن")</h6>
                        </div>
                        <div class="col-4">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <h6>@L("Role-Based", "قائم على الأدوار")</h6>
                        </div>
                        <div class="col-4">
                            <i class="fas fa-chart-line fa-2x mb-2"></i>
                            <h6>@L("Performance", "الأداء")</h6>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Registration Form -->
            <div class="col-lg-6 d-flex align-items-center justify-content-center">
                <div class="register-form-container w-100" style="max-width: 500px;">
                    <div class="text-center mb-4">
                        <div class="d-lg-none mb-3">
                            <i class="fas fa-user-plus fa-3x text-primary"></i>
                        </div>
                        <h2 class="fw-bold">@L("Create Account", "إنشاء حساب")</h2>
                        <p class="text-muted">@L("Register with your Employee ID", "سجل باستخدام رقم الموظف")</p>
                    </div>

                    @if (!string.IsNullOrEmpty(errorMessage))
                    {
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle @GetMarginEnd(1)"></i>
                            @errorMessage
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(successMessage))
                    {
                        <div class="alert alert-success" role="alert">
                            <i class="fas fa-check-circle @GetMarginEnd(1)"></i>
                            @successMessage
                        </div>
                    }

                    <EditForm Model="registerModel" OnValidSubmit="HandleRegister" OnInvalidSubmit="HandleInvalidSubmit" FormName="registerForm">
                        <DataAnnotationsValidator />
                        <ValidationSummary />

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="employeeId" class="form-label">
                                    <i class="fas fa-id-card @GetMarginEnd(1)"></i>
                                    @L("Employee ID", "رقم الموظف") *
                                </label>
                                <InputText @bind-Value="registerModel.EmployeeId" class="form-control"
                                          id="employeeId" placeholder="@L("Enter Employee ID", "أدخل رقم الموظف")" />
                                <ValidationMessage For="@(() => registerModel.EmployeeId)" />
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope @GetMarginEnd(1)"></i>
                                    @L("Email Address", "البريد الإلكتروني") *
                                </label>
                                <InputText @bind-Value="registerModel.Email" class="form-control"
                                          id="email" placeholder="@L("Enter email", "أدخل البريد الإلكتروني")" />
                                <ValidationMessage For="@(() => registerModel.Email)" />
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="englishName" class="form-label">
                                    <i class="fas fa-user @GetMarginEnd(1)"></i>
                                    @L("English Name", "الاسم بالإنجليزية") *
                                </label>
                                <InputText @bind-Value="registerModel.EnglishName" class="form-control"
                                          id="englishName" placeholder="@L("Enter English name", "أدخل الاسم بالإنجليزية")" />
                                <ValidationMessage For="@(() => registerModel.EnglishName)" />
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="arabicName" class="form-label">
                                    <i class="fas fa-user @GetMarginEnd(1)"></i>
                                    @L("Arabic Name", "الاسم بالعربية") *
                                </label>
                                <InputText @bind-Value="registerModel.ArabicName" class="form-control"
                                          id="arabicName" placeholder="@L("Enter Arabic name", "أدخل الاسم بالعربية")" />
                                <ValidationMessage For="@(() => registerModel.ArabicName)" />
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock @GetMarginEnd(1)"></i>
                                    @L("Password", "كلمة المرور") *
                                </label>
                                <div class="input-group">
                                    <InputText @bind-Value="registerModel.Password" type="@(showPassword ? "text" : "password")"
                                              class="form-control" id="password"
                                              placeholder="@L("Enter password", "أدخل كلمة المرور")" />
                                    <button type="button" class="btn btn-outline-secondary" @onclick="TogglePasswordVisibility">
                                        <i class="fas fa-@(showPassword ? "eye-slash" : "eye")"></i>
                                    </button>
                                </div>
                                <ValidationMessage For="@(() => registerModel.Password)" />
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="confirmPassword" class="form-label">
                                    <i class="fas fa-lock @GetMarginEnd(1)"></i>
                                    @L("Confirm Password", "تأكيد كلمة المرور") *
                                </label>
                                <div class="input-group">
                                    <InputText @bind-Value="registerModel.ConfirmPassword" type="@(showConfirmPassword ? "text" : "password")"
                                              class="form-control" id="confirmPassword"
                                              placeholder="@L("Confirm password", "أكد كلمة المرور")" />
                                    <button type="button" class="btn btn-outline-secondary" @onclick="ToggleConfirmPasswordVisibility">
                                        <i class="fas fa-@(showConfirmPassword ? "eye-slash" : "eye")"></i>
                                    </button>
                                </div>
                                <ValidationMessage For="@(() => registerModel.ConfirmPassword)" />
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="preferredLanguage" class="form-label">
                                <i class="fas fa-language @GetMarginEnd(1)"></i>
                                @L("Preferred Language", "اللغة المفضلة")
                            </label>
                            <InputSelect @bind-Value="registerModel.PreferredLanguage" class="form-select" id="preferredLanguage">
                                <option value="en">@L("English", "الإنجليزية")</option>
                                <option value="ar">@L("Arabic", "العربية")</option>
                            </InputSelect>
                        </div>

                        @if (showRoleInfo)
                        {
                            <div class="alert alert-info">
                                <h6 class="fw-bold mb-2">
                                    <i class="fas fa-info-circle @GetMarginEnd(1)"></i>
                                    @L("Role Assignment Information", "معلومات تعيين الدور")
                                </h6>
                                <p class="mb-1">
                                    <strong>@L("Assigned Role", "الدور المعين"):</strong> @roleInfo.role.GetDisplayName(CurrentLanguage)
                                </p>
                                @if (roleInfo.departmentName != null)
                                {
                                    <p class="mb-0">
                                        <strong>@L("Department", "القسم"):</strong> @roleInfo.departmentName
                                    </p>
                                }
                            </div>
                        }

                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary btn-lg" disabled="@isLoading">
                                @if (isLoading)
                                {
                                    <span class="spinner-border spinner-border-sm @GetMarginEnd(1)" role="status"></span>
                                }
                                <i class="fas fa-user-plus @GetMarginEnd(1)"></i>
                                @L("Create Account", "إنشاء حساب")
                            </button>
                        </div>
                    </EditForm>

                    <div class="text-center">
                        <div class="mb-3">
                            <span class="text-muted">@L("Already have an account?", "لديك حساب بالفعل؟")</span>
                            <a href="/login" class="text-decoration-none @GetMarginStart(1)">
                                @L("Sign In", "تسجيل الدخول")
                            </a>
                        </div>

                        <!-- Language Switcher -->
                        <div class="border-top pt-3">
                            <small class="text-muted d-block mb-2">@L("Language", "اللغة")</small>
                            <LanguageSwitcher ShowDropdown="false" Size="sm" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private RegisterModel registerModel = new();
    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;
    private bool isLoading = false;
    private bool showPassword = false;
    private bool showConfirmPassword = false;
    private bool showRoleInfo = false;
    private (UserRole role, string? departmentName) roleInfo;

    protected override async Task OnInitializedAsync()
    {
        // Check if user is already authenticated
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true)
        {
            Navigation.NavigateTo("/dashboard");
        }
    }

    private async Task HandleRegister()
    {
        isLoading = true;
        errorMessage = string.Empty;
        successMessage = string.Empty;
        StateHasChanged();

        try
        {
            Console.WriteLine($"Starting registration for Employee ID: {registerModel.EmployeeId}");

            // Basic validation
            if (string.IsNullOrWhiteSpace(registerModel.EmployeeId))
            {
                errorMessage = L("Employee ID is required.", "رقم الموظف مطلوب.");
                return;
            }

            if (string.IsNullOrWhiteSpace(registerModel.Email))
            {
                errorMessage = L("Email is required.", "البريد الإلكتروني مطلوب.");
                return;
            }

            if (string.IsNullOrWhiteSpace(registerModel.EnglishName))
            {
                errorMessage = L("English name is required.", "الاسم بالإنجليزية مطلوب.");
                return;
            }

            if (string.IsNullOrWhiteSpace(registerModel.ArabicName))
            {
                errorMessage = L("Arabic name is required.", "الاسم بالعربية مطلوب.");
                return;
            }

            if (string.IsNullOrWhiteSpace(registerModel.Password))
            {
                errorMessage = L("Password is required.", "كلمة المرور مطلوبة.");
                return;
            }

            if (registerModel.Password != registerModel.ConfirmPassword)
            {
                errorMessage = L("Passwords do not match.", "كلمات المرور غير متطابقة.");
                return;
            }

            Console.WriteLine("Basic validation passed");

            // Validate Employee ID format
            if (!EmployeeAuthService.IsValidEmployeeIdFormat(registerModel.EmployeeId))
            {
                errorMessage = L("Invalid Employee ID format. Please use 3-50 alphanumeric characters.",
                               "تنسيق رقم الموظف غير صحيح. يرجى استخدام 3-50 حرف أو رقم.");
                return;
            }

            Console.WriteLine("Employee ID format validation passed");

            // Check if Employee ID is available
            var isAvailable = await EmployeeAuthService.IsEmployeeIdAvailableAsync(registerModel.EmployeeId);
            Console.WriteLine($"Employee ID availability check: {isAvailable}");

            if (!isAvailable)
            {
                errorMessage = L("Employee ID is already registered. Please use a different Employee ID.",
                               "رقم الموظف مسجل بالفعل. يرجى استخدام رقم موظف مختلف.");
                return;
            }

            Console.WriteLine("Starting user registration");

            // Register user
            var (result, user) = await EmployeeAuthService.RegisterWithEmployeeIdAsync(
                registerModel.EmployeeId,
                registerModel.Password,
                registerModel.EnglishName,
                registerModel.ArabicName,
                registerModel.Email,
                registerModel.PreferredLanguage);

            Console.WriteLine($"Registration result: {result.Succeeded}");

            if (result.Succeeded && user != null)
            {
                successMessage = L("Account created successfully! You can now sign in with your Employee ID.",
                                 "تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول برقم الموظف.");

                Console.WriteLine("Registration successful, redirecting to login");

                // Clear form
                registerModel = new RegisterModel();

                // Redirect to login after a short delay
                await Task.Delay(2000);
                Navigation.NavigateTo("/login");
            }
            else
            {
                var errors = result.Errors?.Select(e => e.Description) ?? new[] { "Unknown error occurred" };
                errorMessage = string.Join(", ", errors);
                Console.WriteLine($"Registration failed: {errorMessage}");
            }
        }
        catch (Exception ex)
        {
            errorMessage = L("An error occurred during registration. Please try again.",
                           "حدث خطأ أثناء التسجيل. يرجى المحاولة مرة أخرى.");
            Console.WriteLine($"Registration exception: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void HandleInvalidSubmit()
    {
        errorMessage = L("Please correct the validation errors and try again.", "يرجى تصحيح أخطاء التحقق والمحاولة مرة أخرى.");
        Console.WriteLine("Form validation failed");
        StateHasChanged();
    }

    private async Task OnEmployeeIdChanged()
    {
        if (!string.IsNullOrWhiteSpace(registerModel.EmployeeId) &&
            EmployeeAuthService.IsValidEmployeeIdFormat(registerModel.EmployeeId))
        {
            try
            {
                var (role, departmentId) = await EmployeeManagementService.GetRoleAndDepartmentAssignmentAsync(registerModel.EmployeeId);

                string? departmentName = null;
                if (departmentId.HasValue)
                {
                    // Get department name - this would need a method in the service
                    // For now, we'll leave it as null
                }

                roleInfo = (role, departmentName);
                showRoleInfo = true;
            }
            catch
            {
                showRoleInfo = false;
            }
        }
        else
        {
            showRoleInfo = false;
        }

        StateHasChanged();
    }

    private void TogglePasswordVisibility()
    {
        showPassword = !showPassword;
    }

    private void ToggleConfirmPasswordVisibility()
    {
        showConfirmPassword = !showConfirmPassword;
    }

    public class RegisterModel
    {
        [Required(ErrorMessage = "Employee ID is required")]
        [StringLength(50, MinimumLength = 3, ErrorMessage = "Employee ID must be between 3 and 50 characters")]
        public string EmployeeId { get; set; } = string.Empty;

        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Please enter a valid email address")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "English name is required")]
        [StringLength(255, ErrorMessage = "English name cannot exceed 255 characters")]
        public string EnglishName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Arabic name is required")]
        [StringLength(255, ErrorMessage = "Arabic name cannot exceed 255 characters")]
        public string ArabicName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Password is required")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "Password must be at least 6 characters long")]
        [RegularExpression(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$",
            ErrorMessage = "Password must contain at least one lowercase letter, one uppercase letter, and one digit")]
        public string Password { get; set; } = string.Empty;

        [Required(ErrorMessage = "Password confirmation is required")]
        [Compare("Password", ErrorMessage = "Password and confirmation password do not match")]
        public string ConfirmPassword { get; set; } = string.Empty;

        public string PreferredLanguage { get; set; } = "en";
    }
}