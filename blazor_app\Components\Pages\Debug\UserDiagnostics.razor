@page "/debug/users"
@using Microsoft.AspNetCore.Identity
@using Microsoft.EntityFrameworkCore
@using EmployeeRatingSystem.Blazor.Models
@using EmployeeRatingSystem.Blazor.Data
@using EmployeeRatingSystem.Blazor.Components.Shared

@inherits LocalizedComponentBase
@inject ApplicationDbContext DbContext
@inject UserManager<ApplicationUser> UserManager

<PageTitle>User Diagnostics</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-user-cog me-2"></i>
                    User Account Diagnostics
                </h1>
                <div class="btn-group">
                    <button class="btn btn-primary" @onclick="RefreshData">
                        <i class="fas fa-sync-alt me-1"></i>
                        Refresh
                    </button>
                    <button class="btn btn-success" @onclick="ActivateAllUsers">
                        <i class="fas fa-user-check me-1"></i>
                        Activate All Users
                    </button>
                </div>
            </div>

            @if (users == null)
            {
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading user data...</p>
                </div>
            }
            else
            {
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-users me-2"></i>
                                    User Accounts Summary
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="border-end">
                                            <h3 class="text-primary">@users.Count</h3>
                                            <small class="text-muted">Total Users</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <h3 class="text-success">@users.Count(u => u.IsActive)</h3>
                                        <small class="text-muted">Active Users</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    Role Distribution
                                </h5>
                            </div>
                            <div class="card-body">
                                @foreach (var roleGroup in users.GroupBy(u => u.Role))
                                {
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span class="badge bg-@GetRoleBadgeColor(roleGroup.Key)">
                                            @roleGroup.Key.GetDisplayName("en")
                                        </span>
                                        <span class="fw-bold">@roleGroup.Count()</span>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-table me-2"></i>
                            Detailed User Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Employee ID</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Role</th>
                                        <th>Status</th>
                                        <th>Lockout</th>
                                        <th>Failed Attempts</th>
                                        <th>Email Confirmed</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var user in users.OrderBy(u => u.EmployeeId))
                                    {
                                        <tr class="@(user.IsActive ? "" : "table-warning")">
                                            <td>
                                                <strong>@user.EmployeeId</strong>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong>@user.EnglishName</strong>
                                                    <br>
                                                    <small class="text-muted">@user.ArabicName</small>
                                                </div>
                                            </td>
                                            <td>@user.Email</td>
                                            <td>
                                                <span class="badge bg-@GetRoleBadgeColor(user.Role)">
                                                    @user.Role.GetDisplayName("en")
                                                </span>
                                            </td>
                                            <td>
                                                @if (user.IsActive)
                                                {
                                                    <span class="badge bg-success">Active</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-warning">Inactive</span>
                                                }
                                            </td>
                                            <td>
                                                @if (user.LockoutEnd.HasValue && user.LockoutEnd > DateTimeOffset.UtcNow)
                                                {
                                                    <span class="badge bg-danger">
                                                        Locked until @user.LockoutEnd.Value.ToString("yyyy-MM-dd HH:mm")
                                                    </span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-success">Not Locked</span>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge bg-@(user.AccessFailedCount > 0 ? "warning" : "success")">
                                                    @user.AccessFailedCount
                                                </span>
                                            </td>
                                            <td>
                                                @if (user.EmailConfirmed)
                                                {
                                                    <span class="badge bg-success">Yes</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-warning">No</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    @if (user.LockoutEnd.HasValue && user.LockoutEnd > DateTimeOffset.UtcNow)
                                                    {
                                                        <button class="btn btn-outline-success" @onclick="() => UnlockUser(user.Id)">
                                                            <i class="fas fa-unlock"></i>
                                                        </button>
                                                    }
                                                    @if (user.AccessFailedCount > 0)
                                                    {
                                                        <button class="btn btn-outline-warning" @onclick="() => ResetFailedAttempts(user.Id)">
                                                            <i class="fas fa-redo"></i>
                                                        </button>
                                                    }
                                                    @if (!user.IsActive)
                                                    {
                                                        <button class="btn btn-outline-success" @onclick="() => ActivateUser(user.Id)">
                                                            <i class="fas fa-user-check"></i>
                                                        </button>
                                                    }
                                                    <button class="btn btn-outline-primary" @onclick="() => ResetPassword(user.Id)">
                                                        <i class="fas fa-key"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                @if (!string.IsNullOrEmpty(message))
                {
                    <div class="alert alert-@(isError ? "danger" : "success") alert-dismissible fade show mt-3">
                        <i class="fas fa-@(isError ? "exclamation-triangle" : "check-circle") me-2"></i>
                        @message
                        <button type="button" class="btn-close" @onclick="ClearMessage"></button>
                    </div>
                }
            }
        </div>
    </div>
</div>

@code {
    private List<ApplicationUser> users = new();
    private string message = string.Empty;
    private bool isError = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadUsers();
    }

    private async Task LoadUsers()
    {
        try
        {
            users = await DbContext.Users
                .Include(u => u.PrimaryDepartment)
                .OrderBy(u => u.EmployeeId)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            ShowMessage($"Error loading users: {ex.Message}", true);
        }
    }

    private async Task RefreshData()
    {
        await LoadUsers();
        ShowMessage("Data refreshed successfully", false);
    }

    private async Task UnlockUser(string userId)
    {
        try
        {
            var user = await UserManager.FindByIdAsync(userId);
            if (user != null)
            {
                await UserManager.SetLockoutEndDateAsync(user, null);
                await LoadUsers();
                ShowMessage($"User {user.EmployeeId} unlocked successfully", false);
            }
        }
        catch (Exception ex)
        {
            ShowMessage($"Error unlocking user: {ex.Message}", true);
        }
    }

    private async Task ResetFailedAttempts(string userId)
    {
        try
        {
            var user = await UserManager.FindByIdAsync(userId);
            if (user != null)
            {
                await UserManager.ResetAccessFailedCountAsync(user);
                await LoadUsers();
                ShowMessage($"Failed attempts reset for user {user.EmployeeId}", false);
            }
        }
        catch (Exception ex)
        {
            ShowMessage($"Error resetting failed attempts: {ex.Message}", true);
        }
    }

    private async Task ResetPassword(string userId)
    {
        try
        {
            var user = await UserManager.FindByIdAsync(userId);
            if (user != null)
            {
                var token = await UserManager.GeneratePasswordResetTokenAsync(user);
                var result = await UserManager.ResetPasswordAsync(user, token, "Password123!");

                if (result.Succeeded)
                {
                    ShowMessage($"Password reset to 'Password123!' for user {user.EmployeeId}", false);
                }
                else
                {
                    ShowMessage($"Error resetting password: {string.Join(", ", result.Errors.Select(e => e.Description))}", true);
                }
            }
        }
        catch (Exception ex)
        {
            ShowMessage($"Error resetting password: {ex.Message}", true);
        }
    }

    private async Task ActivateUser(string userId)
    {
        try
        {
            var user = await UserManager.FindByIdAsync(userId);
            if (user != null)
            {
                user.IsActive = true;
                user.IsDeleted = false;
                var result = await UserManager.UpdateAsync(user);

                if (result.Succeeded)
                {
                    await LoadUsers();
                    ShowMessage($"User {user.EmployeeId} activated successfully", false);
                }
                else
                {
                    ShowMessage($"Error activating user: {string.Join(", ", result.Errors.Select(e => e.Description))}", true);
                }
            }
        }
        catch (Exception ex)
        {
            ShowMessage($"Error activating user: {ex.Message}", true);
        }
    }

    private string GetRoleBadgeColor(UserRole role)
    {
        return role switch
        {
            UserRole.SUPER_ADMIN => "danger",
            UserRole.MANAGER => "primary",
            UserRole.SUPERVISOR => "info",
            UserRole.EXCELLENCE_TEAM => "warning",
            UserRole.EMPLOYEE => "success",
            _ => "secondary"
        };
    }

    private void ShowMessage(string msg, bool error)
    {
        message = msg;
        isError = error;
        StateHasChanged();
    }

    private void ClearMessage()
    {
        message = string.Empty;
        StateHasChanged();
    }

    private async Task ActivateAllUsers()
    {
        try
        {
            var inactiveUsers = users.Where(u => !u.IsActive || u.IsDeleted).ToList();
            var activatedCount = 0;

            foreach (var user in inactiveUsers)
            {
                user.IsActive = true;
                user.IsDeleted = false;
                var result = await UserManager.UpdateAsync(user);

                if (result.Succeeded)
                {
                    activatedCount++;
                }
            }

            await LoadUsers();
            ShowMessage($"Successfully activated {activatedCount} users", false);
        }
        catch (Exception ex)
        {
            ShowMessage($"Error activating users: {ex.Message}", true);
        }
    }
}
