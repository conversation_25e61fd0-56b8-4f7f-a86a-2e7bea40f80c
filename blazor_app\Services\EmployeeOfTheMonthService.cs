using EmployeeRatingSystem.Blazor.Data;
using EmployeeRatingSystem.Blazor.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EmployeeRatingSystem.Blazor.Services
{
    /// <summary>
    /// Service interface for Employee of the Month functionality
    /// </summary>
    public interface IEmployeeOfTheMonthService
    {
        Task<List<EmployeeOfTheMonthViewModel>> GetEmployeeOfTheMonthByPeriodAsync(string monthYear);
        Task<List<EmployeeOfTheMonthViewModel>> GetAllEmployeeOfTheMonthRecordsAsync();
        Task<List<EmployeeOfTheMonthViewModel>> GetAllEvaluatedEmployeesForPeriodAsync(string monthYear);
        Task<EmployeeOfTheMonthViewModel?> GetTopEmployeeByDepartmentAsync(int departmentId, string monthYear);
        Task<bool> CalculateAndSelectEmployeeOfTheMonthAsync(string monthYear, string selectedBy);
        Task<bool> CalculateAndSelectSingleEmployeeOfTheMonthAsync(string monthYear, string selectedBy);
        Task<List<DepartmentEmployeeRanking>> GetDepartmentRankingsAsync(string monthYear);
        Task<EmployeeCalculationDebugInfo> DebugEmployeeCalculationAsync(string employeeId, string monthYear);
        Task<DataIntegrityReport> VerifyDataIntegrityAsync(string monthYear);
        Task<bool> FixDataIntegrityIssuesAsync(string monthYear);
    }

    /// <summary>
    /// Service for managing Employee of the Month functionality
    /// </summary>
    public class EmployeeOfTheMonthService : IEmployeeOfTheMonthService
    {
        private readonly ApplicationDbContext _context;
        private readonly IEvaluationCalculationService _evaluationService;
        private readonly ILogger<EmployeeOfTheMonthService> _logger;

        public EmployeeOfTheMonthService(
            ApplicationDbContext context,
            IEvaluationCalculationService evaluationService,
            ILogger<EmployeeOfTheMonthService> logger)
        {
            _context = context;
            _evaluationService = evaluationService;
            _logger = logger;
        }

        /// <summary>
        /// Get Employee of the Month records for a specific period with comprehensive evaluation data
        /// </summary>
        public async Task<List<EmployeeOfTheMonthViewModel>> GetEmployeeOfTheMonthByPeriodAsync(string monthYear)
        {
            var records = await _context.EmployeeOfTheMonth
                .Include(e => e.Employee)
                .Include(e => e.Department)
                .Include(e => e.Selector)
                .Where(e => e.MonthYear == monthYear && e.IsActive && !e.IsDeleted)
                .OrderBy(e => e.Department.NameEn)
                .ToListAsync();

            var viewModels = new List<EmployeeOfTheMonthViewModel>();

            foreach (var record in records)
            {
                // Get comprehensive evaluation data for additional details
                var comprehensiveEval = await _context.ComprehensiveEvaluations
                    .FirstOrDefaultAsync(ce => ce.EmployeeId == record.EmployeeId &&
                                              ce.EvaluationPeriod == monthYear);

                // Get department total work volume
                var departmentTotal = await _context.DepartmentWorkVolumeTotals
                    .FirstOrDefaultAsync(dt => dt.DepartmentId == record.DepartmentId &&
                                              dt.EvaluationPeriod == monthYear);

                // Get work volume data
                var workVolumeData = await _context.WorkVolumeData
                    .FirstOrDefaultAsync(wv => wv.EmployeeId == record.EmployeeId &&
                                              wv.EvaluationPeriod == monthYear);

                // Get supervisor evaluation
                var supervisorEval = await _context.SupervisorEvaluations
                    .FirstOrDefaultAsync(se => se.EmployeeId == record.EmployeeId &&
                                              se.EvaluationPeriod == monthYear);

                var viewModel = MapToViewModel(record);

                // Add comprehensive evaluation details
                if (comprehensiveEval != null)
                {
                    viewModel.AttendanceScore = comprehensiveEval.AttendanceScore;
                    // Use the pre-calculated work volume score from comprehensive evaluation
                    viewModel.WorkVolumeScore = comprehensiveEval.WorkVolumeScore;
                }

                if (departmentTotal != null)
                {
                    viewModel.DepartmentWorkVolume = departmentTotal.TotalDepartmentWork;
                    _logger.LogInformation($"Department {record.DepartmentId} work volume: Quality={departmentTotal.TotalQualityProgramWork}, Oracle={departmentTotal.TotalOracleWork}, Documented={departmentTotal.TotalDocumentedWork}, Total={departmentTotal.TotalDepartmentWork}");
                }
                else
                {
                    _logger.LogWarning($"No department work volume total found for department {record.DepartmentId}, period {monthYear}");
                }

                if (workVolumeData != null)
                {
                    // Use the actual employee work volume from work volume data
                    viewModel.EmployeeWorkVolume = workVolumeData.TotalEmployeeWork;
                }

                if (supervisorEval != null)
                {
                    viewModel.SupervisorEvaluationScore = supervisorEval.TotalScore;
                    viewModel.SupervisorEvaluationPercentage = supervisorEval.EvaluationPercentage;
                }

                // Calculate the correct percentage: (Employee Work Volume / Department Work Volume) × 100
                if (viewModel.DepartmentWorkVolume > 0 && viewModel.EmployeeWorkVolume > 0)
                {
                    viewModel.WorkVolumePercentage = (viewModel.EmployeeWorkVolume / viewModel.DepartmentWorkVolume) * 100;
                }
                else
                {
                    viewModel.WorkVolumePercentage = 0;
                }

                viewModels.Add(viewModel);
            }

            return viewModels;
        }

        /// <summary>
        /// Get all Employee of the Month records
        /// </summary>
        public async Task<List<EmployeeOfTheMonthViewModel>> GetAllEmployeeOfTheMonthRecordsAsync()
        {
            var records = await _context.EmployeeOfTheMonth
                .Include(e => e.Employee)
                .Include(e => e.Department)
                .Include(e => e.Selector)
                .Where(e => e.IsActive && !e.IsDeleted)
                .OrderByDescending(e => e.MonthYear)
                .ThenBy(e => e.Department.NameEn)
                .ToListAsync();

            return records.Select(MapToViewModel).ToList();
        }

        /// <summary>
        /// Get all evaluated employees for a specific period with comprehensive evaluation data
        /// This shows ALL employees with evaluations, not just Employee of the Month winners
        /// </summary>
        public async Task<List<EmployeeOfTheMonthViewModel>> GetAllEvaluatedEmployeesForPeriodAsync(string monthYear)
        {
            // Get all comprehensive evaluations for the period
            // Note: SQLite doesn't support decimal in ORDER BY, so we'll sort on client side
            var comprehensiveEvals = await _context.ComprehensiveEvaluations
                .Include(ce => ce.Employee)
                .Include(ce => ce.Department)
                .Where(ce => ce.EvaluationPeriod == monthYear)
                .ToListAsync();

            // Sort on client side to avoid SQLite decimal ordering issues
            comprehensiveEvals = comprehensiveEvals
                .OrderBy(ce => ce.Department.NameEn)
                .ThenByDescending(ce => ce.TotalScore)
                .ToList();

            var viewModels = new List<EmployeeOfTheMonthViewModel>();

            foreach (var comprehensiveEval in comprehensiveEvals)
            {
                // Get department total work volume
                var departmentTotal = await _context.DepartmentWorkVolumeTotals
                    .FirstOrDefaultAsync(dt => dt.DepartmentId == comprehensiveEval.DepartmentId &&
                                              dt.EvaluationPeriod == monthYear);

                // Get work volume data
                var workVolumeData = await _context.WorkVolumeData
                    .FirstOrDefaultAsync(wv => wv.EmployeeId == comprehensiveEval.EmployeeId &&
                                              wv.EvaluationPeriod == monthYear);

                // Get supervisor evaluation
                var supervisorEval = await _context.SupervisorEvaluations
                    .FirstOrDefaultAsync(se => se.EmployeeId == comprehensiveEval.EmployeeId &&
                                              se.EvaluationPeriod == monthYear);

                // Check if this employee was selected as Employee of the Month
                var employeeOfTheMonth = await _context.EmployeeOfTheMonth
                    .FirstOrDefaultAsync(eom => eom.EmployeeId == comprehensiveEval.EmployeeId &&
                                               eom.MonthYear == monthYear &&
                                               eom.IsActive && !eom.IsDeleted);

                // Create view model from comprehensive evaluation data
                var viewModel = new EmployeeOfTheMonthViewModel
                {
                    Id = employeeOfTheMonth?.Id ?? 0,
                    EmployeeId = comprehensiveEval.Employee.EmployeeId,
                    EmployeeName = comprehensiveEval.Employee.EnglishName,
                    EmployeeNameAr = comprehensiveEval.Employee.ArabicName,
                    DepartmentName = comprehensiveEval.Department.NameEn,
                    DepartmentNameAr = comprehensiveEval.Department.NameAr,
                    MonthYear = monthYear,
                    AttendancePercentage = comprehensiveEval.AttendancePercentage,
                    TotalScore = comprehensiveEval.TotalScore,
                    SelectedBy = employeeOfTheMonth?.Selector?.EnglishName ?? "",
                    SelectedAt = employeeOfTheMonth?.SelectedAt ?? DateTime.MinValue,

                    // Use comprehensive evaluation data
                    AttendanceScore = comprehensiveEval.AttendanceScore,
                    WorkVolumeScore = comprehensiveEval.WorkVolumeScore,

                    // Set department and employee work volumes
                    DepartmentWorkVolume = departmentTotal?.TotalDepartmentWork ?? 0,
                    EmployeeWorkVolume = workVolumeData?.TotalEmployeeWork ?? 0,

                    // Calculate work volume percentage correctly
                    WorkVolumePercentage = (departmentTotal?.TotalDepartmentWork ?? 0) > 0 && workVolumeData != null
                        ? (workVolumeData.TotalEmployeeWork / departmentTotal.TotalDepartmentWork) * 100
                        : 0,

                    // Set supervisor evaluation data
                    SupervisorEvaluationScore = supervisorEval?.TotalScore ?? 0,
                    SupervisorEvaluationPercentage = supervisorEval?.EvaluationPercentage ?? 0,

                    // Legacy fields for compatibility
                    WorkVolume = comprehensiveEval.WorkVolumeScore,
                    SupervisorScore = comprehensiveEval.SupervisorScore
                };

                // Calculate work volume percentage if not already set
                if (viewModel.WorkVolumePercentage == 0 && viewModel.DepartmentWorkVolume > 0 && viewModel.EmployeeWorkVolume > 0)
                {
                    viewModel.WorkVolumePercentage = (viewModel.EmployeeWorkVolume / viewModel.DepartmentWorkVolume) * 100;
                }

                viewModels.Add(viewModel);
            }

            return viewModels;
        }

        /// <summary>
        /// Get the top employee for a specific department and period
        /// </summary>
        public async Task<EmployeeOfTheMonthViewModel?> GetTopEmployeeByDepartmentAsync(int departmentId, string monthYear)
        {
            var record = await _context.EmployeeOfTheMonth
                .Include(e => e.Employee)
                .Include(e => e.Department)
                .Include(e => e.Selector)
                .FirstOrDefaultAsync(e => e.DepartmentId == departmentId && 
                                         e.MonthYear == monthYear && 
                                         e.IsActive && !e.IsDeleted);

            return record != null ? MapToViewModel(record) : null;
        }

        /// <summary>
        /// Calculate and automatically select Employee of the Month for each department
        /// </summary>
        public async Task<bool> CalculateAndSelectEmployeeOfTheMonthAsync(string monthYear, string selectedBy)
        {
            try
            {
                _logger.LogInformation($"Starting Employee of the Month calculation for period: {monthYear}");

                // Get all active departments
                var departments = await _context.Departments
                    .Where(d => d.IsActive)
                    .ToListAsync();

                _logger.LogInformation($"Found {departments.Count} active departments");
        {
            try
            {
                _logger.LogInformation($"Starting SINGLE Employee of the Month calculation for period: {monthYear}");

                // Clear any existing Employee of the Month records for this period
                var existingRecords = await _context.EmployeeOfTheMonth
                    .Where(e => e.MonthYear == monthYear && e.IsActive && !e.IsDeleted)
                    .ToListAsync();

                foreach (var record in existingRecords)
                {
                    record.IsActive = false;
                    record.IsDeleted = true;
                }

                // Get ALL comprehensive evaluations across ALL departments
                var allEvaluations = await _context.ComprehensiveEvaluations
                    .Include(e => e.Employee)
                    .Include(e => e.Department)
                    .Where(e => e.EvaluationPeriod == monthYear)
                    .ToListAsync();

                _logger.LogInformation($"Found {allEvaluations.Count} total evaluations across all departments");

                if (!allEvaluations.Any())
                {
                    _logger.LogWarning($"No comprehensive evaluations found for period {monthYear}");
                    return false;
                }

                // Filter employees with minimum 70% attendance
                var eligibleEmployees = allEvaluations
                    .Where(e => e.AttendancePercentage >= 0.70m)
                    .ToList();

                _logger.LogInformation($"Found {eligibleEmployees.Count} eligible employees (>=70% attendance) organization-wide");

                if (!eligibleEmployees.Any())
                {
                    _logger.LogWarning($"No eligible employees (>=70% attendance) found organization-wide for period {monthYear}");
                    return false;
                }

                // Find the SINGLE top performer across ALL departments
                var topEmployee = eligibleEmployees
                    .OrderByDescending(e => e.TotalScore)
                    .ThenByDescending(e => e.AttendancePercentage)
                    .ThenByDescending(e => e.WorkVolumeScore)
                    .First();

                _logger.LogInformation($"Selected organization-wide Employee of the Month: {topEmployee.Employee.EnglishName} " +
                                     $"from {topEmployee.Department.NameEn} with score {topEmployee.TotalScore}");

                // Create the single Employee of the Month record
                var employeeOfTheMonth = new EmployeeOfTheMonth
                {
                    EmployeeId = topEmployee.EmployeeId,
                    DepartmentId = topEmployee.DepartmentId,
                    MonthYear = monthYear,
                    AttendancePercentage = topEmployee.AttendancePercentage,
                    WorkVolume = topEmployee.WorkVolumeScore,
                    SupervisorScore = topEmployee.SupervisorScore,
                    TotalScore = topEmployee.TotalScore,
                    JustificationEn = $"Selected as organization-wide Employee of the Month with the highest total score of {topEmployee.TotalScore:F2} " +
                                    $"(Attendance: {topEmployee.AttendancePercentage:P1}, Work Volume: {topEmployee.WorkVolumeScore:F2}, " +
                                    $"Supervisor Score: {topEmployee.SupervisorScore:F2})",
                    JustificationAr = $"تم اختياره كموظف الشهر على مستوى المؤسسة بأعلى نتيجة إجمالية {topEmployee.TotalScore:F2} " +
                                    $"(الحضور: {topEmployee.AttendancePercentage:P1}، حجم العمل: {topEmployee.WorkVolumeScore:F2}، " +
                                    $"تقييم المشرف: {topEmployee.SupervisorScore:F2})",
                    SelectedBy = selectedBy,
                    SelectedAt = DateTime.UtcNow,
                    IsActive = true,
                    IsDeleted = false
                };

                _context.EmployeeOfTheMonth.Add(employeeOfTheMonth);
                await _context.SaveChangesAsync();

                _logger.LogInformation($"Successfully selected single Employee of the Month: {topEmployee.Employee.EnglishName}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error calculating single Employee of the Month for period {monthYear}");
                return false;
            }
        }

                foreach (var department in departments)
                {
                    _logger.LogInformation($"Processing department: {department.NameEn} (ID: {department.Id})");

                    // Get comprehensive evaluations for this department and period
                    var evaluations = await _context.ComprehensiveEvaluations
                        .Include(e => e.Employee)
                        .Where(e => e.DepartmentId == department.Id &&
                                   e.EvaluationPeriod == monthYear)
                        .ToListAsync();

                    _logger.LogInformation($"Found {evaluations.Count} comprehensive evaluations for department {department.NameEn}");

                    if (!evaluations.Any())
                    {
                        _logger.LogWarning($"No comprehensive evaluations found for department {department.NameEn} in period {monthYear}");
                        continue;
                    }

                    // Filter employees with minimum 70% attendance
                    var eligibleEmployees = evaluations
                        .Where(e => e.AttendancePercentage >= 0.70m)
                        .ToList();

                    _logger.LogInformation($"Found {eligibleEmployees.Count} eligible employees (>=70% attendance) in department {department.NameEn}");

                    if (!eligibleEmployees.Any())
                    {
                        _logger.LogWarning($"No eligible employees (>=70% attendance) found for department {department.NameEn}");
                        continue;
                    }

                    // Find the top performer
                    var topEmployee = eligibleEmployees
                        .OrderByDescending(e => e.TotalScore)
                        .First();

                    _logger.LogInformation($"Top employee in {department.NameEn}: {topEmployee.Employee.EnglishName} with score {topEmployee.TotalScore:F4}");

                    // Check if already exists
                    var existing = await _context.EmployeeOfTheMonth
                        .FirstOrDefaultAsync(e => e.EmployeeId == topEmployee.EmployeeId &&
                                                 e.DepartmentId == department.Id &&
                                                 e.MonthYear == monthYear);

                    if (existing == null)
                    {
                        // Create new Employee of the Month record
                        var employeeOfTheMonth = new EmployeeOfTheMonth
                        {
                            EmployeeId = topEmployee.EmployeeId,
                            DepartmentId = department.Id,
                            MonthYear = monthYear,
                            AttendancePercentage = topEmployee.AttendancePercentage,
                            WorkVolume = topEmployee.WorkVolumeScore,
                            SupervisorScore = topEmployee.SupervisorScore,
                            TotalScore = topEmployee.TotalScore,
                            SelectedBy = selectedBy,
                            SelectedAt = DateTime.UtcNow,
                            IsActive = true
                        };

                        _context.EmployeeOfTheMonth.Add(employeeOfTheMonth);
                    }
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating Employee of the Month for period {MonthYear}", monthYear);
                return false;
            }
        }

        /// <summary>
        /// Get department rankings for a specific period
        /// </summary>
        public async Task<List<DepartmentEmployeeRanking>> GetDepartmentRankingsAsync(string monthYear)
        {
            var rankings = await _context.ComprehensiveEvaluations
                .Include(e => e.Employee)
                .Include(e => e.Department)
                .Where(e => e.EvaluationPeriod == monthYear && e.AttendancePercentage >= 0.70m)
                .GroupBy(e => e.DepartmentId)
                .Select(g => new DepartmentEmployeeRanking
                {
                    DepartmentId = g.Key,
                    DepartmentName = g.First().Department.NameEn,
                    DepartmentNameAr = g.First().Department.NameAr,
                    TopEmployees = g.OrderByDescending(e => e.TotalScore)
                                   .Take(5)
                                   .Select(e => new EmployeeRankingInfo
                                   {
                                       EmployeeId = e.EmployeeId,
                                       EmployeeName = e.Employee.EnglishName,
                                       EmployeeNameAr = e.Employee.ArabicName,
                                       TotalScore = e.TotalScore,
                                       AttendancePercentage = e.AttendancePercentage,
                                       WorkVolumeScore = e.WorkVolumeScore,
                                       SupervisorScore = e.SupervisorScore
                                   }).ToList()
                })
                .ToListAsync();

            return rankings;
        }

        /// <summary>
        /// Debug method to check why an employee might not appear in Employee of the Month calculations
        /// </summary>
        public async Task<EmployeeCalculationDebugInfo> DebugEmployeeCalculationAsync(string employeeId, string monthYear)
        {
            var debugInfo = new EmployeeCalculationDebugInfo
            {
                EmployeeId = employeeId,
                EvaluationPeriod = monthYear
            };

            // Check if employee exists and is active
            var employee = await _context.Users.FirstOrDefaultAsync(u => u.Id == employeeId);
            debugInfo.EmployeeExists = employee != null;
            debugInfo.EmployeeIsActive = employee?.IsActive ?? false;
            debugInfo.EmployeeName = employee?.EnglishName ?? "Not Found";
            debugInfo.DepartmentId = employee?.PrimaryDepartmentId;

            if (employee == null) return debugInfo;

            // Check comprehensive evaluation
            var comprehensiveEval = await _context.ComprehensiveEvaluations
                .FirstOrDefaultAsync(ce => ce.EmployeeId == employeeId && ce.EvaluationPeriod == monthYear);
            debugInfo.HasComprehensiveEvaluation = comprehensiveEval != null;
            debugInfo.AttendancePercentage = comprehensiveEval?.AttendancePercentage;
            debugInfo.MeetsAttendanceRequirement = (comprehensiveEval?.AttendancePercentage ?? 0) >= 0.70m;
            debugInfo.TotalScore = comprehensiveEval?.TotalScore;

            // Check work volume data
            var workVolumeData = await _context.WorkVolumeData
                .FirstOrDefaultAsync(wv => wv.EmployeeId == employeeId && wv.EvaluationPeriod == monthYear);
            debugInfo.HasWorkVolumeData = workVolumeData != null;
            debugInfo.EmployeeWorkVolume = workVolumeData?.TotalEmployeeWork;

            // Check supervisor evaluation
            var supervisorEval = await _context.SupervisorEvaluations
                .FirstOrDefaultAsync(se => se.EmployeeId == employeeId && se.EvaluationPeriod == monthYear);
            debugInfo.HasSupervisorEvaluation = supervisorEval != null;
            debugInfo.SupervisorScore = supervisorEval?.TotalScore;

            // Check department work volume
            if (debugInfo.DepartmentId.HasValue)
            {
                var departmentTotal = await _context.DepartmentWorkVolumeTotals
                    .FirstOrDefaultAsync(dt => dt.DepartmentId == debugInfo.DepartmentId.Value && dt.EvaluationPeriod == monthYear);
                debugInfo.HasDepartmentWorkVolume = departmentTotal != null;
                debugInfo.DepartmentWorkVolume = departmentTotal?.TotalDepartmentWork;
            }

            // Check if already selected as Employee of the Month
            var existingRecord = await _context.EmployeeOfTheMonth
                .FirstOrDefaultAsync(e => e.EmployeeId == employeeId && e.MonthYear == monthYear);
            debugInfo.AlreadySelectedAsEmployeeOfMonth = existingRecord != null;

            return debugInfo;
        }

        /// <summary>
        /// Verify data integrity between employee work volumes and department totals
        /// </summary>
        public async Task<DataIntegrityReport> VerifyDataIntegrityAsync(string monthYear)
        {
            var report = new DataIntegrityReport
            {
                EvaluationPeriod = monthYear,
                DepartmentReports = new List<DepartmentIntegrityReport>()
            };

            // Get all departments
            var departments = await _context.Departments
                .Where(d => d.IsActive && !d.IsDeleted)
                .ToListAsync();

            foreach (var department in departments)
            {
                var departmentReport = new DepartmentIntegrityReport
                {
                    DepartmentId = department.Id,
                    DepartmentName = department.NameEn
                };

                // Get department total from database
                var departmentTotal = await _context.DepartmentWorkVolumeTotals
                    .FirstOrDefaultAsync(dt => dt.DepartmentId == department.Id && dt.EvaluationPeriod == monthYear);

                departmentReport.StoredDepartmentTotal = departmentTotal?.TotalDepartmentWork ?? 0;

                // Get all employee work volumes for this department
                var employeeWorkVolumes = await _context.WorkVolumeData
                    .Where(w => w.DepartmentId == department.Id && w.EvaluationPeriod == monthYear)
                    .ToListAsync();

                departmentReport.EmployeeCount = employeeWorkVolumes.Count;
                departmentReport.CalculatedDepartmentTotal = employeeWorkVolumes.Sum(w => w.TotalEmployeeWork);
                departmentReport.IsConsistent = Math.Abs(departmentReport.StoredDepartmentTotal - departmentReport.CalculatedDepartmentTotal) < 0.01m;

                // Get individual employee details
                departmentReport.EmployeeDetails = employeeWorkVolumes.Select(w => new EmployeeWorkVolumeDetail
                {
                    EmployeeId = w.EmployeeId,
                    EmployeeName = w.Employee?.EnglishName ?? "Unknown",
                    QualityWork = w.QualityProgramWork,
                    OracleWork = w.OracleWork,
                    DocumentedWork = w.DocumentedWork,
                    TotalWork = w.TotalEmployeeWork
                }).ToList();

                report.DepartmentReports.Add(departmentReport);
            }

            report.OverallConsistency = report.DepartmentReports.All(d => d.IsConsistent);
            return report;
        }

        /// <summary>
        /// Fix data integrity issues by recalculating department totals
        /// </summary>
        public async Task<bool> FixDataIntegrityIssuesAsync(string monthYear)
        {
            try
            {
                _logger.LogInformation($"Starting data integrity fix for period: {monthYear}");

                // Get all departments with inconsistent data
                var integrityReport = await VerifyDataIntegrityAsync(monthYear);
                var inconsistentDepartments = integrityReport.DepartmentReports.Where(d => !d.IsConsistent).ToList();

                if (!inconsistentDepartments.Any())
                {
                    _logger.LogInformation("No data integrity issues found");
                    return true;
                }

                foreach (var deptReport in inconsistentDepartments)
                {
                    _logger.LogInformation($"Fixing department {deptReport.DepartmentName} (ID: {deptReport.DepartmentId})");

                    // Remove existing department total
                    var existingTotal = await _context.DepartmentWorkVolumeTotals
                        .FirstOrDefaultAsync(dt => dt.DepartmentId == deptReport.DepartmentId && dt.EvaluationPeriod == monthYear);

                    if (existingTotal != null)
                    {
                        _context.DepartmentWorkVolumeTotals.Remove(existingTotal);
                    }

                    // Get all employee work volumes for this department
                    var employeeWorkVolumes = await _context.WorkVolumeData
                        .Where(w => w.DepartmentId == deptReport.DepartmentId && w.EvaluationPeriod == monthYear)
                        .ToListAsync();

                    if (employeeWorkVolumes.Any())
                    {
                        // Calculate correct totals
                        var totalQuality = employeeWorkVolumes.Sum(w => w.QualityProgramWork);
                        var totalOracle = employeeWorkVolumes.Sum(w => w.OracleWork);
                        var totalDocumented = employeeWorkVolumes.Sum(w => w.DocumentedWork);

                        // Create new department total with correct values
                        var newDepartmentTotal = new DepartmentWorkVolumeTotal
                        {
                            DepartmentId = deptReport.DepartmentId,
                            EvaluationPeriod = monthYear,
                            TotalQualityProgramWork = totalQuality,
                            TotalOracleWork = totalOracle,
                            TotalDocumentedWork = totalDocumented,
                            RecordedBy = "System-AutoFix"
                        };

                        _context.DepartmentWorkVolumeTotals.Add(newDepartmentTotal);

                        _logger.LogInformation($"Fixed department {deptReport.DepartmentName}: " +
                            $"Quality={totalQuality}, Oracle={totalOracle}, Documented={totalDocumented}, " +
                            $"Total={newDepartmentTotal.TotalDepartmentWork}");
                    }
                }

                await _context.SaveChangesAsync();
                _logger.LogInformation($"Data integrity fix completed for {inconsistentDepartments.Count} departments");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error fixing data integrity issues for period: {monthYear}");
                return false;
            }
        }

        /// <summary>
        /// Calculate and select SINGLE organization-wide Employee of the Month winner
        /// </summary>
        public async Task<bool> CalculateAndSelectSingleEmployeeOfTheMonthAsync(string monthYear, string selectedBy)
        {
            try
            {
                _logger.LogInformation($"Starting SINGLE Employee of the Month calculation for period: {monthYear}");

                // Clear any existing Employee of the Month records for this period
                var existingRecords = await _context.EmployeeOfTheMonth
                    .Where(e => e.MonthYear == monthYear && e.IsActive && !e.IsDeleted)
                    .ToListAsync();

                foreach (var record in existingRecords)
                {
                    record.IsActive = false;
                    record.IsDeleted = true;
                }

                // Get ALL comprehensive evaluations across ALL departments
                var allEvaluations = await _context.ComprehensiveEvaluations
                    .Include(e => e.Employee)
                    .Include(e => e.Department)
                    .Where(e => e.EvaluationPeriod == monthYear)
                    .ToListAsync();

                _logger.LogInformation($"Found {allEvaluations.Count} total evaluations across all departments");

                if (!allEvaluations.Any())
                {
                    _logger.LogWarning($"No comprehensive evaluations found for period {monthYear}");
                    return false;
                }

                // Filter employees with minimum 70% attendance
                var eligibleEmployees = allEvaluations
                    .Where(e => e.AttendancePercentage >= 0.70m)
                    .ToList();

                _logger.LogInformation($"Found {eligibleEmployees.Count} eligible employees (>=70% attendance) organization-wide");

                if (!eligibleEmployees.Any())
                {
                    _logger.LogWarning($"No eligible employees (>=70% attendance) found organization-wide for period {monthYear}");
                    return false;
                }

                // Find the SINGLE top performer across ALL departments
                var topEmployee = eligibleEmployees
                    .OrderByDescending(e => e.TotalScore)
                    .ThenByDescending(e => e.AttendancePercentage)
                    .ThenByDescending(e => e.WorkVolumeScore)
                    .First();

                _logger.LogInformation($"Selected organization-wide Employee of the Month: {topEmployee.Employee.EnglishName} " +
                                     $"from {topEmployee.Department.NameEn} with score {topEmployee.TotalScore}");

                // Create the single Employee of the Month record
                var employeeOfTheMonth = new EmployeeOfTheMonth
                {
                    EmployeeId = topEmployee.EmployeeId,
                    DepartmentId = topEmployee.DepartmentId,
                    MonthYear = monthYear,
                    AttendancePercentage = topEmployee.AttendancePercentage,
                    WorkVolume = topEmployee.WorkVolumeScore,
                    SupervisorScore = topEmployee.SupervisorScore,
                    TotalScore = topEmployee.TotalScore,
                    JustificationEn = $"Selected as organization-wide Employee of the Month with the highest total score of {topEmployee.TotalScore:F2} " +
                                    $"(Attendance: {topEmployee.AttendancePercentage:P1}, Work Volume: {topEmployee.WorkVolumeScore:F2}, " +
                                    $"Supervisor Score: {topEmployee.SupervisorScore:F2})",
                    JustificationAr = $"تم اختياره كموظف الشهر على مستوى المؤسسة بأعلى نتيجة إجمالية {topEmployee.TotalScore:F2} " +
                                    $"(الحضور: {topEmployee.AttendancePercentage:P1}، حجم العمل: {topEmployee.WorkVolumeScore:F2}، " +
                                    $"تقييم المشرف: {topEmployee.SupervisorScore:F2})",
                    SelectedBy = selectedBy,
                    SelectedAt = DateTime.UtcNow,
                    IsActive = true,
                    IsDeleted = false
                };

                _context.EmployeeOfTheMonth.Add(employeeOfTheMonth);
                await _context.SaveChangesAsync();

                _logger.LogInformation($"Successfully selected single Employee of the Month: {topEmployee.Employee.EnglishName}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error calculating single Employee of the Month for period {monthYear}");
                return false;
            }
        }

        /// <summary>
        /// Map EmployeeOfTheMonth entity to ViewModel
        /// </summary>
        private static EmployeeOfTheMonthViewModel MapToViewModel(EmployeeOfTheMonth entity)
        {
            return new EmployeeOfTheMonthViewModel
            {
                Id = entity.Id,
                EmployeeId = entity.Employee.EmployeeId,
                EmployeeName = entity.Employee.EnglishName,
                EmployeeNameAr = entity.Employee.ArabicName,
                DepartmentName = entity.Department.NameEn,
                DepartmentNameAr = entity.Department.NameAr,
                MonthYear = entity.MonthYear,
                AttendancePercentage = entity.AttendancePercentage,
                WorkVolume = entity.WorkVolume,
                SupervisorScore = entity.SupervisorScore,
                TotalScore = entity.TotalScore,
                JustificationEn = entity.JustificationEn,
                JustificationAr = entity.JustificationAr,
                SelectedBy = entity.Selector.EnglishName,
                SelectedAt = entity.SelectedAt
            };
        }
    }

    /// <summary>
    /// ViewModel for Employee of the Month display
    /// </summary>
    public class EmployeeOfTheMonthViewModel
    {
        public int Id { get; set; }
        public string EmployeeId { get; set; } = string.Empty;
        public string EmployeeName { get; set; } = string.Empty;
        public string EmployeeNameAr { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public string DepartmentNameAr { get; set; } = string.Empty;
        public string MonthYear { get; set; } = string.Empty;
        public decimal AttendancePercentage { get; set; }
        public decimal WorkVolume { get; set; }
        public decimal SupervisorScore { get; set; }
        public decimal TotalScore { get; set; }
        public string? JustificationEn { get; set; }
        public string? JustificationAr { get; set; }
        public string SelectedBy { get; set; } = string.Empty;
        public DateTime SelectedAt { get; set; }

        // Additional comprehensive evaluation details
        public decimal DepartmentWorkVolume { get; set; }
        public decimal EmployeeWorkVolume { get; set; }
        public decimal WorkVolumePercentage { get; set; }
        public decimal WorkVolumeScore { get; set; } // Pre-calculated work volume score from comprehensive evaluation
        public decimal AttendanceScore { get; set; }
        public decimal SupervisorEvaluationScore { get; set; }
        public decimal SupervisorEvaluationPercentage { get; set; }

        // Calculated properties for display based on Excel reference model
        public decimal WorkloadCalculation => WorkVolumeScore; // Use pre-calculated work volume score (60% weight already applied)
        public decimal ExceptionalWorkScore => 5.0m; // Placeholder for exceptional work - should be configurable
        public decimal ExcellenceScore => WorkloadCalculation + ExceptionalWorkScore; // Excellence = Workload Calculation + Exceptional Work
        public decimal WorkloadEightyPercent => 0.8m * ExcellenceScore; // 80% for Workload = 0.8 × Excellence Score
        public decimal BehaviorScore => SupervisorEvaluationScore * 0.2m; // Behavior component (20% of supervisor evaluation)
    }

    /// <summary>
    /// Department ranking information
    /// </summary>
    public class DepartmentEmployeeRanking
    {
        public int DepartmentId { get; set; }
        public string DepartmentName { get; set; } = string.Empty;
        public string DepartmentNameAr { get; set; } = string.Empty;
        public List<EmployeeRankingInfo> TopEmployees { get; set; } = new();
    }

    /// <summary>
    /// Employee ranking information
    /// </summary>
    public class EmployeeRankingInfo
    {
        public string EmployeeId { get; set; } = string.Empty;
        public string EmployeeName { get; set; } = string.Empty;
        public string EmployeeNameAr { get; set; } = string.Empty;
        public decimal TotalScore { get; set; }
        public decimal AttendancePercentage { get; set; }
        public decimal WorkVolumeScore { get; set; }
        public decimal SupervisorScore { get; set; }
    }

    /// <summary>
    /// Debug information for Employee of the Month calculation troubleshooting
    /// </summary>
    public class EmployeeCalculationDebugInfo
    {
        public string EmployeeId { get; set; } = string.Empty;
        public string EvaluationPeriod { get; set; } = string.Empty;
        public string EmployeeName { get; set; } = string.Empty;
        public bool EmployeeExists { get; set; }
        public bool EmployeeIsActive { get; set; }
        public int? DepartmentId { get; set; }

        // Data availability checks
        public bool HasComprehensiveEvaluation { get; set; }
        public bool HasWorkVolumeData { get; set; }
        public bool HasSupervisorEvaluation { get; set; }
        public bool HasDepartmentWorkVolume { get; set; }

        // Calculation values
        public decimal? AttendancePercentage { get; set; }
        public bool MeetsAttendanceRequirement { get; set; }
        public decimal? TotalScore { get; set; }
        public decimal? EmployeeWorkVolume { get; set; }
        public decimal? DepartmentWorkVolume { get; set; }
        public decimal? SupervisorScore { get; set; }

        // Status checks
        public bool AlreadySelectedAsEmployeeOfMonth { get; set; }

        /// <summary>
        /// Summary of issues preventing Employee of the Month calculation
        /// </summary>
        public List<string> Issues
        {
            get
            {
                var issues = new List<string>();

                if (!EmployeeExists) issues.Add("Employee does not exist");
                if (!EmployeeIsActive) issues.Add("Employee is not active");
                if (!HasComprehensiveEvaluation) issues.Add("Missing comprehensive evaluation");
                if (!HasWorkVolumeData) issues.Add("Missing work volume data");
                if (!HasSupervisorEvaluation) issues.Add("Missing supervisor evaluation");
                if (!HasDepartmentWorkVolume) issues.Add("Missing department work volume data");
                if (!MeetsAttendanceRequirement) issues.Add($"Attendance below 70% requirement (Current: {AttendancePercentage:P1})");
                if (!DepartmentId.HasValue) issues.Add("Employee has no department assignment");

                return issues;
            }
        }

        /// <summary>
        /// Whether the employee is eligible for Employee of the Month
        /// </summary>
        public bool IsEligible => EmployeeExists && EmployeeIsActive && HasComprehensiveEvaluation &&
                                 HasWorkVolumeData && HasSupervisorEvaluation && HasDepartmentWorkVolume &&
                                 MeetsAttendanceRequirement && DepartmentId.HasValue;
    }

    /// <summary>
    /// Data integrity report for verification
    /// </summary>
    public class DataIntegrityReport
    {
        public string EvaluationPeriod { get; set; } = string.Empty;
        public List<DepartmentIntegrityReport> DepartmentReports { get; set; } = new();
        public bool OverallConsistency { get; set; }
    }

    /// <summary>
    /// Department-specific integrity report
    /// </summary>
    public class DepartmentIntegrityReport
    {
        public int DepartmentId { get; set; }
        public string DepartmentName { get; set; } = string.Empty;
        public decimal StoredDepartmentTotal { get; set; }
        public decimal CalculatedDepartmentTotal { get; set; }
        public int EmployeeCount { get; set; }
        public bool IsConsistent { get; set; }
        public List<EmployeeWorkVolumeDetail> EmployeeDetails { get; set; } = new();
    }

    /// <summary>
    /// Employee work volume detail for verification
    /// </summary>
    public class EmployeeWorkVolumeDetail
    {
        public string EmployeeId { get; set; } = string.Empty;
        public string EmployeeName { get; set; } = string.Empty;
        public decimal QualityWork { get; set; }
        public decimal OracleWork { get; set; }
        public decimal DocumentedWork { get; set; }
        public decimal TotalWork { get; set; }
    }
}
