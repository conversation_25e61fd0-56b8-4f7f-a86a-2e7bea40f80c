@page "/users"
@rendermode InteractiveServer
@using System.ComponentModel.DataAnnotations
@using EmployeeRatingSystem.Blazor.Components.Shared
@using EmployeeRatingSystem.Blazor.Services
@using EmployeeRatingSystem.Blazor.Models
@using EmployeeRatingSystem.Blazor.Data
@using Microsoft.EntityFrameworkCore
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.Components.Authorization

@inherits LocalizedComponentBase
@inject ApplicationDbContext DbContext
@inject UserManager<ApplicationUser> UserManager
@inject IBreadcrumbService BreadcrumbService
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider

<PageTitle>@GetPageTitle("Users", "المستخدمون")</PageTitle>

<AuthenticationGuard>
<!-- Page Header -->
<div class="page-header @GetLayoutClass()">
    <!-- Breadcrumb -->
    <nav aria-label="@L("Breadcrumb", "مسار التنقل")">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="/" class="text-decoration-none">@L("Home", "الرئيسية")</a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                @L("Users", "المستخدمون")
            </li>
        </ol>
    </nav>

    <div class="d-flex justify-content-between align-items-start flex-wrap">
        <div class="flex-grow-1">
            <h1 class="h1 mb-2">
                <i class="fas fa-users @GetMarginEnd() text-primary"></i>
                @L("Users", "المستخدمون")
            </h1>
            <p class="lead mb-0">@L("Manage system users, roles, and department assignments", "إدارة مستخدمي النظام والأدوار وتعيينات الأقسام")</p>
        </div>

        <div class="@GetMarginStart() mt-2 mt-md-0">
            <div class="d-flex gap-2 @GetLayoutClass()">
                @if (CanCreateUsers())
                {
                    <button class="btn btn-primary" @onclick="ShowCreateUserModal">
                        <i class="fas fa-plus @GetMarginEnd(1)"></i>
                        @L("Create", "إنشاء")
                    </button>
                }
                <button class="btn btn-outline-secondary" @onclick="ExportUsers">
                    <i class="fas fa-download @GetMarginEnd(1)"></i>
                    @L("Export", "تصدير")
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="d-flex align-items-center">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="@GetMarginStart()">
                        <div class="stat-value">@totalUsers</div>
                        <div class="stat-label">@L("Total Users", "إجمالي المستخدمين")</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="d-flex align-items-center">
                    <div class="stat-icon">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <div class="@GetMarginStart()">
                        <div class="stat-value">@activeUsers</div>
                        <div class="stat-label">@L("Active Users", "المستخدمون النشطون")</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="d-flex align-items-center">
                    <div class="stat-icon">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <div class="@GetMarginStart()">
                        <div class="stat-value">@managersCount</div>
                        <div class="stat-label">@L("Managers", "المديرون")</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="d-flex align-items-center">
                    <div class="stat-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="@GetMarginStart()">
                        <div class="stat-value">@departmentsCount</div>
                        <div class="stat-label">@L("Departments", "الأقسام")</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Search -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-4">
                <label class="form-label">@L("Search", "البحث")</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" @bind="searchTerm" @bind:event="oninput"
                           placeholder="@L("Search by name, email, or employee ID", "البحث بالاسم أو البريد الإلكتروني أو رقم الموظف")" />
                </div>
            </div>
            <div class="col-md-3">
                <label class="form-label">@L("Role", "الدور")</label>
                <select class="form-select" @bind="selectedRole">
                    <option value="">@L("All Roles", "جميع الأدوار")</option>
                    @foreach (var role in Enum.GetValues<UserRole>())
                    {
                        <option value="@role">@GetRoleDisplayName(role)</option>
                    }
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">@L("Department", "القسم")</label>
                <select class="form-select" @bind="selectedDepartmentId">
                    <option value="">@L("All Departments", "جميع الأقسام")</option>
                    @foreach (var dept in departments)
                    {
                        <option value="@dept.Id">@dept.GetDisplayName(CurrentLanguage)</option>
                    }
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">@L("Status", "الحالة")</label>
                <select class="form-select" @bind="selectedStatus">
                    <option value="">@L("All", "الكل")</option>
                    <option value="active">@L("Active", "نشط")</option>
                    <option value="inactive">@L("Inactive", "غير نشط")</option>
                </select>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <button class="btn btn-outline-secondary btn-sm" @onclick="ClearFilters">
                    <i class="fas fa-times @GetMarginEnd(1)"></i>
                    @L("Clear Filters", "مسح المرشحات")
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-users @GetMarginEnd(1)"></i>
                @L("Users List", "قائمة المستخدمين")
            </h5>
            <div class="d-flex align-items-center gap-2">
                <span class="badge bg-primary">
                    @filteredUsers.Count() @L("users", "مستخدم")
                </span>
                <div class="btn-group btn-group-sm" role="group">
                    <button class="btn @(viewMode == "table" ? "btn-primary" : "btn-outline-primary")"
                            @onclick="@(() => SetViewMode("table"))" title="@L("Table View", "عرض جدولي")">
                        <i class="fas fa-table"></i>
                    </button>
                    <button class="btn @(viewMode == "cards" ? "btn-primary" : "btn-outline-primary")"
                            @onclick="@(() => SetViewMode("cards"))" title="@L("Card View", "عرض البطاقات")">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        @if (isLoading)
        {
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">@L("Loading", "جاري التحميل")</span>
                </div>
                <p class="mt-2 text-muted">@L("Loading", "جاري التحميل")</p>
            </div>
        }
        else if (!filteredUsers.Any())
        {
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">@L("No Data", "لا توجد بيانات")</h5>
                <p class="text-muted">@L("No users found matching your criteria", "لم يتم العثور على مستخدمين يطابقون معاييرك")</p>
                @if (CanCreateUsers())
                {
                    <button class="btn btn-primary" @onclick="ShowCreateUserModal">
                        <i class="fas fa-plus @GetMarginEnd(1)"></i>
                        @L("Create First User", "إنشاء أول مستخدم")
                    </button>
                }
            </div>
        }
        else if (viewMode == "table")
        {
            <div class="table-responsive">
                <table class="table table-hover mb-0 @GetLayoutClass()">
                    <thead class="table-light">
                        <tr>
                            <th scope="col" class="@GetTextStart()">
                                <button class="btn btn-link p-0 text-decoration-none fw-semibold"
                                        @onclick="() => SortBy(nameof(ApplicationUser.EmployeeId))">
                                    @L("Employee ID", "رقم الموظف")
                                    @if (sortField == nameof(ApplicationUser.EmployeeId))
                                    {
                                        <i class="fas fa-sort-@(sortAscending ? "up" : "down") @GetMarginStart(1)"></i>
                                    }
                                </button>
                            </th>
                            <th scope="col" class="@GetTextStart()">
                                <button class="btn btn-link p-0 text-decoration-none fw-semibold"
                                        @onclick="() => SortByName()"
                                        title="@GetNameSortTooltip()">
                                    @L("Name", "الاسم")
                                    @if (sortField == "Name")
                                    {
                                        <i class="fas fa-sort-@(sortAscending ? "up" : "down") @GetMarginStart(1)"></i>
                                    }
                                </button>
                            </th>
                            <th scope="col" class="@GetTextStart()">
                                <button class="btn btn-link p-0 text-decoration-none fw-semibold"
                                        @onclick="() => SortBy(nameof(ApplicationUser.Email))">
                                    @L("Email", "البريد الإلكتروني")
                                    @if (sortField == nameof(ApplicationUser.Email))
                                    {
                                        <i class="fas fa-sort-@(sortAscending ? "up" : "down") @GetMarginStart(1)"></i>
                                    }
                                </button>
                            </th>
                            <th scope="col" class="@GetTextStart()">
                                <button class="btn btn-link p-0 text-decoration-none fw-semibold"
                                        @onclick="() => SortBy(nameof(ApplicationUser.Role))">
                                    @L("Role", "الدور")
                                    @if (sortField == nameof(ApplicationUser.Role))
                                    {
                                        <i class="fas fa-sort-@(sortAscending ? "up" : "down") @GetMarginStart(1)"></i>
                                    }
                                </button>
                            </th>
                            <th scope="col" class="@GetTextStart()">@L("Department", "القسم")</th>
                            <th scope="col" class="text-center">@L("Status", "الحالة")</th>
                            <th scope="col" class="text-center">@L("Actions", "الإجراءات")</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var user in GetPagedUsers())
                        {
                            <tr>
                                <td class="@GetTextStart()">
                                    <span class="fw-medium">@user.EmployeeId</span>
                                </td>
                                <td class="user-name-column">
                                    @if (IsRTL)
                                    {
                                        <div class="d-flex align-items-center justify-content-end">
                                            <div class="text-end">
                                                <div class="fw-medium">@GetUserDisplayName(user)</div>
                                                @if (!string.IsNullOrEmpty(user.EnglishName) && IsArabic)
                                                {
                                                    <small class="text-muted d-block">@user.EnglishName</small>
                                                }
                                            </div>
                                            <div class="profile-avatar profile-avatar-sm ms-2">
                                                @GetUserInitials(user)
                                            </div>
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="d-flex align-items-center justify-content-start">
                                            <div class="profile-avatar profile-avatar-sm me-2">
                                                @GetUserInitials(user)
                                            </div>
                                            <div class="text-start">
                                                <div class="fw-medium">@GetUserDisplayName(user)</div>
                                                @if (!string.IsNullOrEmpty(user.ArabicName) && !IsArabic)
                                                {
                                                    <small class="text-muted d-block">@user.ArabicName</small>
                                                }
                                            </div>
                                        </div>
                                    }
                                </td>
                                <td class="@GetTextStart()">
                                    <a href="mailto:@user.Email" class="text-decoration-none">@user.Email</a>
                                </td>
                                <td class="@GetTextStart()">
                                    <span class="badge bg-@GetRoleBadgeColor(user.Role)">
                                        @GetRoleDisplayName(user.Role)
                                    </span>
                                </td>
                                <td class="@GetTextStart()">
                                    @if (user.PrimaryDepartment != null)
                                    {
                                        <span class="text-muted">@user.PrimaryDepartment.GetDisplayName(CurrentLanguage)</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">@L("No Department", "بدون قسم")</span>
                                    }
                                </td>
                                <td class="text-center">
                                    @if (user.IsActive)
                                    {
                                        <span class="badge bg-success">@L("Active", "نشط")</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">@L("Inactive", "غير نشط")</span>
                                    }
                                </td>
                                <td class="text-center">
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button class="btn btn-outline-primary" @onclick="() => ViewUser(user)"
                                                title="@L("View Details", "عرض التفاصيل")">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary" @onclick="() => EditUser(user)"
                                                title="@L("Edit", "تعديل")">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-@(user.IsActive ? "warning" : "success")"
                                                @onclick="() => ToggleUserStatus(user)"
                                                title="@(user.IsActive ? L("Deactivate", "إلغاء التفعيل") : L("Activate", "تفعيل"))">
                                            <i class="fas fa-@(user.IsActive ? "user-slash" : "user-check")"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" @onclick="() => DeleteUser(user)"
                                                title="@L("Delete", "حذف")">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else if (viewMode == "cards")
        {
            <div class="p-3">
                <div class="row g-3">
                    @foreach (var user in GetPagedUsers())
                    {
                        <div class="col-md-6 col-lg-4">
                            <div class="card h-100 user-card">
                                <div class="card-body">
                                    <div class="d-flex align-items-start justify-content-between mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="profile-avatar @GetMarginEnd()">
                                                @GetUserInitials(user)
                                            </div>
                                            <div>
                                                <h6 class="mb-1">@GetUserDisplayName(user)</h6>
                                                <small class="text-muted">@user.EmployeeId</small>
                                            </div>
                                        </div>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button"
                                                    data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><button class="dropdown-item" @onclick="() => ViewUser(user)">
                                                    <i class="fas fa-eye @GetMarginEnd(1)"></i>
                                                    @L("View Details", "عرض التفاصيل")
                                                </button></li>
                                                <li><button class="dropdown-item" @onclick="() => EditUser(user)">
                                                    <i class="fas fa-edit @GetMarginEnd(1)"></i>
                                                    @L("Edit", "تعديل")
                                                </button></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><button class="dropdown-item" @onclick="() => ToggleUserStatus(user)">
                                                    <i class="fas fa-@(user.IsActive ? "user-slash" : "user-check") @GetMarginEnd(1)"></i>
                                                    @(user.IsActive ? L("Deactivate", "إلغاء التفعيل") : L("Activate", "تفعيل"))
                                                </button></li>
                                                <li><button class="dropdown-item text-danger" @onclick="() => DeleteUser(user)">
                                                    <i class="fas fa-trash @GetMarginEnd(1)"></i>
                                                    @L("Delete", "حذف")
                                                </button></li>
                                            </ul>
                                        </div>
                                    </div>

                                    <div class="mb-2">
                                        <small class="text-muted">@L("Email", "البريد الإلكتروني"):</small>
                                        <div>@user.Email</div>
                                    </div>

                                    <div class="mb-2">
                                        <small class="text-muted">@L("Role", "الدور"):</small>
                                        <div>
                                            <span class="badge bg-@GetRoleBadgeColor(user.Role)">
                                                @GetRoleDisplayName(user.Role)
                                            </span>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <small class="text-muted">@L("Department", "القسم"):</small>
                                        <div>
                                            @if (user.PrimaryDepartment != null)
                                            {
                                                @user.PrimaryDepartment.GetDisplayName(CurrentLanguage)
                                            }
                                            else
                                            {
                                                <span class="text-muted fst-italic">@L("No Department", "بدون قسم")</span>
                                            }
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            @if (user.IsActive)
                                            {
                                                <span class="badge bg-success">@L("Active", "نشط")</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">@L("Inactive", "غير نشط")</span>
                                            }
                                        </div>
                                        <small class="text-muted">
                                            @L("Created", "تاريخ الإنشاء"): @FormatDate(user.CreatedAt)
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        }
    </div>

    <!-- Pagination -->
    @if (filteredUsers.Any() && totalPages > 1)
    {
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted">
                    @L("Showing", "عرض") @((currentPage - 1) * pageSize + 1) - @Math.Min(currentPage * pageSize, filteredUsers.Count())
                    @L("of", "من") @filteredUsers.Count() @L("users", "مستخدم")
                </div>
                <nav aria-label="@L("Users pagination", "ترقيم المستخدمين")">
                    <ul class="pagination pagination-sm mb-0 @GetLayoutClass()">
                        <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                            <button class="page-link" @onclick="() => GoToPage(currentPage - 1)" disabled="@(currentPage == 1)">
                                <i class="fas @GetChevronPrevious()"></i>
                            </button>
                        </li>

                        @for (int i = Math.Max(1, currentPage - 2); i <= Math.Min(totalPages, currentPage + 2); i++)
                        {
                            <li class="page-item @(i == currentPage ? "active" : "")">
                                <button class="page-link" @onclick="() => GoToPage(i)">@i</button>
                            </li>
                        }

                        <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                            <button class="page-link" @onclick="() => GoToPage(currentPage + 1)" disabled="@(currentPage == totalPages)">
                                <i class="fas @GetChevronNext()"></i>
                            </button>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    }
</div>

<!-- Create/Edit User Modal -->
@if (showUserModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-@(isEditMode ? "edit" : "plus") @GetMarginEnd(1)"></i>
                        @(isEditMode ? L("Edit User", "تعديل مستخدم") : L("Create User", "إنشاء مستخدم"))
                    </h5>
                    <button type="button" class="btn-close" @onclick="CloseUserModal"></button>
                </div>
                <div class="modal-body">
                    <EditForm Model="userModel" OnValidSubmit="SaveUser" OnInvalidSubmit="OnInvalidSubmit" FormName="userForm">
                        <DataAnnotationsValidator />
                        <ValidationSummary class="alert alert-danger" />

                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">@L("Employee ID", "رقم الموظف") <span class="text-danger">*</span></label>
                                <InputText @bind-Value="userModel.EmployeeId" class="form-control" />
                                <ValidationMessage For="() => userModel.EmployeeId" />
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">@L("Email", "البريد الإلكتروني") <span class="text-danger">*</span></label>
                                <InputText @bind-Value="userModel.Email" class="form-control" type="email" />
                                <ValidationMessage For="() => userModel.Email" />
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">@L("English Name", "الاسم بالإنجليزية") <span class="text-danger">*</span></label>
                                <InputText @bind-Value="userModel.EnglishName" class="form-control" />
                                <ValidationMessage For="() => userModel.EnglishName" />
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">@L("Arabic Name", "الاسم بالعربية") <span class="text-danger">*</span></label>
                                <InputText @bind-Value="userModel.ArabicName" class="form-control" />
                                <ValidationMessage For="() => userModel.ArabicName" />
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">@L("Role", "الدور") <span class="text-danger">*</span></label>
                                <InputSelect @bind-Value="userModel.Role" class="form-select">
                                    @foreach (var role in Enum.GetValues<UserRole>())
                                    {
                                        <option value="@role">@GetRoleDisplayName(role)</option>
                                    }
                                </InputSelect>
                                <ValidationMessage For="() => userModel.Role" />
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">@L("Primary Department", "القسم الأساسي")</label>
                                <InputSelect @bind-Value="userModel.PrimaryDepartmentId" class="form-select">
                                    <option value="">@L("Select Department", "اختر القسم")</option>
                                    @foreach (var dept in departments)
                                    {
                                        <option value="@dept.Id">@dept.GetDisplayName(CurrentLanguage)</option>
                                    }
                                </InputSelect>
                                <ValidationMessage For="() => userModel.PrimaryDepartmentId" />
                            </div>
                            @if (!isEditMode)
                            {
                                <div class="col-md-6">
                                    <label class="form-label">@L("Password", "كلمة المرور")</label>
                                    <InputText @bind-Value="userModel.Password" class="form-control" type="password" placeholder="Password123!" />
                                    <ValidationMessage For="() => userModel.Password" />
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">@L("Confirm Password", "تأكيد كلمة المرور")</label>
                                    <InputText @bind-Value="userModel.ConfirmPassword" class="form-control" type="password" />
                                    <ValidationMessage For="() => userModel.ConfirmPassword" />
                                </div>
                            }
                            <div class="col-12">
                                <div class="form-check">
                                    <InputCheckbox @bind-Value="userModel.IsActive" class="form-check-input" id="isActive" />
                                    <label class="form-check-label" for="isActive">
                                        @L("Active User", "مستخدم نشط")
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" @onclick="CloseUserModal">
                                @L("Cancel", "إلغاء")
                            </button>
                            <button type="submit" class="btn btn-primary" disabled="@isSaving">
                                @if (isSaving)
                                {
                                    <span class="spinner-border spinner-border-sm @GetMarginEnd(1)" role="status"></span>
                                }
                                <i class="fas fa-save @GetMarginEnd(1)"></i>
                                @L("Save", "حفظ")
                            </button>
                        </div>
                    </EditForm>
                </div>
            </div>
        </div>
    </div>
}

<!-- User Details Modal -->
@if (showDetailsModal && selectedUser != null)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user @GetMarginEnd(1)"></i>
                        @L("User Details", "تفاصيل المستخدم")
                    </h5>
                    <button type="button" class="btn-close" @onclick="CloseDetailsModal"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-12 text-center mb-3">
                            <div class="profile-avatar profile-avatar-xl mx-auto mb-2">
                                @GetUserInitials(selectedUser)
                            </div>
                            <h4>@GetUserDisplayName(selectedUser)</h4>
                            <span class="badge bg-@GetRoleBadgeColor(selectedUser.Role) fs-6">
                                @GetRoleDisplayName(selectedUser.Role)
                            </span>
                        </div>

                        <div class="col-md-6">
                            <strong>@L("Employee ID", "رقم الموظف"):</strong>
                            <p>@selectedUser.EmployeeId</p>
                        </div>
                        <div class="col-md-6">
                            <strong>@L("Email", "البريد الإلكتروني"):</strong>
                            <p><a href="mailto:@selectedUser.Email">@selectedUser.Email</a></p>
                        </div>
                        <div class="col-md-6">
                            <strong>@L("English Name", "الاسم بالإنجليزية"):</strong>
                            <p>@selectedUser.EnglishName</p>
                        </div>
                        <div class="col-md-6">
                            <strong>@L("Arabic Name", "الاسم بالعربية"):</strong>
                            <p>@selectedUser.ArabicName</p>
                        </div>
                        <div class="col-md-6">
                            <strong>@L("Primary Department", "القسم الأساسي"):</strong>
                            <p>
                                @if (selectedUser.PrimaryDepartment != null)
                                {
                                    @selectedUser.PrimaryDepartment.GetDisplayName(CurrentLanguage)
                                }
                                else
                                {
                                    <span class="text-muted fst-italic">@L("No Department", "بدون قسم")</span>
                                }
                            </p>
                        </div>
                        <div class="col-md-6">
                            <strong>@L("Status", "الحالة"):</strong>
                            <p>
                                @if (selectedUser.IsActive)
                                {
                                    <span class="badge bg-success">@L("Active", "نشط")</span>
                                }
                                else
                                {
                                    <span class="badge bg-secondary">@L("Inactive", "غير نشط")</span>
                                }
                            </p>
                        </div>
                        <div class="col-md-6">
                            <strong>@L("Created At", "تاريخ الإنشاء"):</strong>
                            <p>@FormatDate(selectedUser.CreatedAt)</p>
                        </div>
                        <div class="col-md-6">
                            <strong>@L("Last Updated", "آخر تحديث"):</strong>
                            <p>@FormatDate(selectedUser.UpdatedAt)</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="CloseDetailsModal">
                        @L("Close", "إغلاق")
                    </button>
                    <button type="button" class="btn btn-primary" @onclick="() => EditUser(selectedUser)">
                        <i class="fas fa-edit @GetMarginEnd(1)"></i>
                        @L("Edit", "تعديل")
                    </button>
                </div>
            </div>
        </div>
    </div>
}

<!-- Confirmation Modal -->
@if (showConfirmModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle text-warning @GetMarginEnd(1)"></i>
                        @L("Confirm Action", "تأكيد الإجراء")
                    </h5>
                    <button type="button" class="btn-close" @onclick="CloseConfirmModal"></button>
                </div>
                <div class="modal-body">
                    <p>@confirmMessage</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="CloseConfirmModal">
                        @L("Cancel", "إلغاء")
                    </button>
                    <button type="button" class="btn btn-danger" @onclick="ConfirmAction" disabled="@isSaving">
                        @if (isSaving)
                        {
                            <span class="spinner-border spinner-border-sm @GetMarginEnd(1)" role="status"></span>
                        }
                        @L("Confirm", "تأكيد")
                    </button>
                </div>
            </div>
        </div>
    </div>
}

@code {
    // Data properties
    private List<ApplicationUser> users = new();
    private List<Department> departments = new();
    private ApplicationUser? currentUser = null;

    // Statistics
    private int totalUsers = 0;
    private int activeUsers = 0;
    private int managersCount = 0;
    private int departmentsCount = 0;

    // Filtering and search
    private string searchTerm = "";
    private UserRole? selectedRole = null;
    private int? selectedDepartmentId = null;
    private string selectedStatus = "";

    // Sorting
    private string sortField = "Name";
    private bool sortAscending = true;

    // Pagination
    private int currentPage = 1;
    private int pageSize = 10;
    private int totalPages => (int)Math.Ceiling((double)filteredUsers.Count() / pageSize);

    // View mode
    private string viewMode = "table";

    // Loading states
    private bool isLoading = true;
    private bool isSaving = false;

    // Modal states
    private bool showUserModal = false;
    private bool showDetailsModal = false;
    private bool showConfirmModal = false;
    private bool isEditMode = false;

    // Selected items
    private ApplicationUser? selectedUser = null;
    private UserModel userModel = new();

    // Confirmation
    private string confirmMessage = "";
    private Func<Task>? confirmAction = null;

    // Computed properties
    private IEnumerable<ApplicationUser> filteredUsers
    {
        get
        {
            var query = users.AsEnumerable();

            // Apply search filter with language priority
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var search = searchTerm.ToLower();
                query = query.Where(u =>
                {
                    // Prioritize current language name in search
                    var primaryName = IsArabic ? u.ArabicName : u.EnglishName;
                    var secondaryName = IsArabic ? u.EnglishName : u.ArabicName;

                    return (primaryName?.ToLower().Contains(search) ?? false) ||
                           (secondaryName?.ToLower().Contains(search) ?? false) ||
                           (u.Email?.ToLower().Contains(search) ?? false) ||
                           (u.EmployeeId?.ToLower().Contains(search) ?? false);
                });
            }

            // Apply role filter
            if (selectedRole.HasValue)
            {
                query = query.Where(u => u.Role == selectedRole.Value);
            }

            // Apply department filter
            if (selectedDepartmentId.HasValue)
            {
                query = query.Where(u => u.PrimaryDepartmentId == selectedDepartmentId.Value);
            }

            // Apply status filter
            if (!string.IsNullOrEmpty(selectedStatus))
            {
                var isActive = selectedStatus == "active";
                query = query.Where(u => u.IsActive == isActive);
            }

            // Apply sorting
            query = sortField switch
            {
                nameof(ApplicationUser.EmployeeId) => sortAscending ?
                    query.OrderBy(u => u.EmployeeId) : query.OrderByDescending(u => u.EmployeeId),
                "Name" => ApplyNameSorting(query, sortAscending),
                nameof(ApplicationUser.Email) => sortAscending ?
                    query.OrderBy(u => u.Email) : query.OrderByDescending(u => u.Email),
                nameof(ApplicationUser.Role) => sortAscending ?
                    query.OrderBy(u => u.Role) : query.OrderByDescending(u => u.Role),
                _ => ApplyNameSorting(query, true)
            };

            return query;
        }
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadCurrentUser();
        await LoadData();
    }

    private async Task LoadCurrentUser()
    {
        try
        {
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                var userEmail = authState.User.Identity.Name;
                if (!string.IsNullOrEmpty(userEmail))
                {
                    currentUser = await UserManager.FindByEmailAsync(userEmail);
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading current user: {ex.Message}");
        }
    }

    private bool CanCreateUsers()
    {
        return currentUser?.Role == UserRole.SUPER_ADMIN ||
               currentUser?.Role == UserRole.EXCELLENCE_TEAM;
    }

    private async Task LoadData()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            // Load users with their departments
            users = await DbContext.Users
                .Include(u => u.PrimaryDepartment)
                .Where(u => !u.IsDeleted)
                .ToListAsync();

            // Load departments
            departments = await DbContext.Departments
                .Where(d => d.IsActive && !d.IsDeleted)
                .OrderBy(d => d.NameEn)
                .ToListAsync();

            // Calculate statistics
            totalUsers = users.Count;
            activeUsers = users.Count(u => u.IsActive);
            managersCount = users.Count(u => u.Role == UserRole.MANAGER || u.Role == UserRole.SUPER_ADMIN);
            departmentsCount = departments.Count;
        }
        catch (Exception ex)
        {
            // Handle error - could show toast notification
            Console.WriteLine($"Error loading data: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    // Pagination methods
    private IEnumerable<ApplicationUser> GetPagedUsers()
    {
        return filteredUsers.Skip((currentPage - 1) * pageSize).Take(pageSize);
    }

    private void GoToPage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            StateHasChanged();
        }
    }

    // Sorting methods
    private void SortBy(string field)
    {
        if (sortField == field)
        {
            sortAscending = !sortAscending;
        }
        else
        {
            sortField = field;
            sortAscending = true;
        }
        currentPage = 1; // Reset to first page when sorting
        StateHasChanged();
    }

    // Filter methods
    private void ClearFilters()
    {
        searchTerm = "";
        selectedRole = null;
        selectedDepartmentId = null;
        selectedStatus = "";
        currentPage = 1;
        StateHasChanged();
    }

    // Sorting methods
    private void SortByName()
    {
        if (sortField == "Name")
        {
            sortAscending = !sortAscending;
        }
        else
        {
            sortField = "Name";
            sortAscending = true;
        }
        currentPage = 1;
        StateHasChanged();
    }

    private IEnumerable<ApplicationUser> ApplyNameSorting(IEnumerable<ApplicationUser> query, bool ascending)
    {
        if (IsArabic)
        {
            // Sort by Arabic name when in Arabic locale using Arabic culture comparison
            var arabicComparer = StringComparer.Create(new System.Globalization.CultureInfo("ar-SA"), false);
            return ascending ?
                query.OrderBy(u => u.ArabicName ?? u.EnglishName ?? "", arabicComparer) :
                query.OrderByDescending(u => u.ArabicName ?? u.EnglishName ?? "", arabicComparer);
        }
        else
        {
            // Sort by English name when in English locale using English culture comparison
            var englishComparer = StringComparer.Create(new System.Globalization.CultureInfo("en-US"), false);
            return ascending ?
                query.OrderBy(u => u.EnglishName ?? u.ArabicName ?? "", englishComparer) :
                query.OrderByDescending(u => u.EnglishName ?? u.ArabicName ?? "", englishComparer);
        }
    }

    private string GetNameSortTooltip()
    {
        var direction = sortAscending ? L("ascending", "تصاعدي") : L("descending", "تنازلي");
        var nameType = IsArabic ? L("Arabic names", "الأسماء العربية") : L("English names", "الأسماء الإنجليزية");
        return L($"Sort by {nameType} in {direction} order", $"ترتيب حسب {nameType} بشكل {direction}");
    }

    // View mode methods
    private void SetViewMode(string mode)
    {
        viewMode = mode;
        StateHasChanged();
    }

    // User management methods
    private async Task ShowCreateUserModal()
    {
        if (!CanCreateUsers())
        {
            await JSRuntime.InvokeVoidAsync("alert", L("Access Denied", "تم رفض الوصول") + ": " + L("Only Super Admin and Excellence Team can create users", "يمكن فقط للسوبر أدمين وفريق التميز إنشاء المستخدمين"));
            return;
        }

        userModel = new UserModel();
        isEditMode = false;
        showUserModal = true;
        StateHasChanged();
    }

    private void EditUser(ApplicationUser user)
    {
        userModel = new UserModel
        {
            Id = user.Id,
            EmployeeId = user.EmployeeId ?? string.Empty,
            Email = user.Email ?? string.Empty,
            EnglishName = user.EnglishName ?? string.Empty,
            ArabicName = user.ArabicName ?? string.Empty,
            Role = user.Role,
            PrimaryDepartmentId = user.PrimaryDepartmentId,
            IsActive = user.IsActive
        };
        isEditMode = true;
        showUserModal = true;
        showDetailsModal = false;
        StateHasChanged();
    }

    private void ViewUser(ApplicationUser user)
    {
        selectedUser = user;
        showDetailsModal = true;
        StateHasChanged();
    }

    private async Task SaveUser()
    {
        Console.WriteLine("SaveUser method called");
        await JSRuntime.InvokeVoidAsync("console.log", "SaveUser method called from JavaScript");

        // Prevent multiple simultaneous save operations
        if (isSaving)
        {
            Console.WriteLine("SaveUser already in progress, ignoring duplicate call");
            await JSRuntime.InvokeVoidAsync("console.log", "SaveUser already in progress, ignoring duplicate call");
            return;
        }

        // Log current userModel data
        Console.WriteLine($"SaveUser - userModel data:");
        Console.WriteLine($"  EmployeeId: '{userModel.EmployeeId}'");
        Console.WriteLine($"  Email: '{userModel.Email}'");
        Console.WriteLine($"  EnglishName: '{userModel.EnglishName}'");
        Console.WriteLine($"  ArabicName: '{userModel.ArabicName}'");
        Console.WriteLine($"  Role: {userModel.Role}");
        Console.WriteLine($"  IsEditMode: {isEditMode}");

        // Check authorization for user creation
        if (!isEditMode && !CanCreateUsers())
        {
            await JSRuntime.InvokeVoidAsync("alert", L("Access Denied", "تم رفض الوصول") + ": " + L("Only Super Admin and Excellence Team can create users", "يمكن فقط للسوبر أدمين وفريق التميز إنشاء المستخدمين"));
            return;
        }

        isSaving = true;
        StateHasChanged();

        try
        {
            if (isEditMode)
            {
                // Update existing user
                var existingUser = await DbContext.Users.FindAsync(userModel.Id);
                if (existingUser != null)
                {
                    existingUser.EmployeeId = userModel.EmployeeId;
                    existingUser.Email = userModel.Email;
                    existingUser.EnglishName = userModel.EnglishName;
                    existingUser.ArabicName = userModel.ArabicName;
                    existingUser.Role = userModel.Role;
                    existingUser.PrimaryDepartmentId = userModel.PrimaryDepartmentId;
                    existingUser.IsActive = userModel.IsActive;
                    existingUser.UpdatedAt = DateTime.UtcNow;

                    await DbContext.SaveChangesAsync();
                }
            }
            else
            {
                // Add detailed logging for debugging
                Console.WriteLine($"Creating new user - Employee ID: '{userModel.EmployeeId}', Email: '{userModel.Email}'");
                await JSRuntime.InvokeVoidAsync("console.log", $"Creating new user - Employee ID: '{userModel.EmployeeId}', Email: '{userModel.Email}'");

                // First, let's see what Employee IDs currently exist in the database
                var allEmployeeIds = await DbContext.Users
                    .Where(u => !u.IsDeleted)
                    .Select(u => u.EmployeeId)
                    .ToListAsync();
                Console.WriteLine($"Current Employee IDs in database: [{string.Join(", ", allEmployeeIds)}]");
                await JSRuntime.InvokeVoidAsync("console.log", $"Current Employee IDs in database: [{string.Join(", ", allEmployeeIds)}]");

                // Check for duplicate email/username before creating
                Console.WriteLine($"Checking for existing user by email: '{userModel.Email}'");
                var existingUserByEmail = await UserManager.FindByEmailAsync(userModel.Email);
                Console.WriteLine($"Existing user by email result: {(existingUserByEmail != null ? existingUserByEmail.EmployeeId : "None")}");

                if (existingUserByEmail != null)
                {
                    var errorMsg = $"Error: A user with email '{userModel.Email}' already exists (Employee ID: {existingUserByEmail.EmployeeId}).";
                    Console.WriteLine(errorMsg);
                    await JSRuntime.InvokeVoidAsync("alert", errorMsg);
                    isSaving = false;
                    StateHasChanged();
                    return;
                }

                Console.WriteLine($"Checking for existing user by Employee ID: '{userModel.EmployeeId}'");

                // Check for duplicate Employee ID (case-insensitive, excluding soft-deleted users)
                var existingUserByEmployeeId = await DbContext.Users
                    .FirstOrDefaultAsync(u => u.EmployeeId.ToLower() == userModel.EmployeeId.ToLower() && !u.IsDeleted);
                Console.WriteLine($"Existing user by Employee ID result: {(existingUserByEmployeeId != null ? existingUserByEmployeeId.Email : "None")}");

                // Also check if there are any soft-deleted users with the same Employee ID
                var deletedUserWithSameId = await DbContext.Users
                    .FirstOrDefaultAsync(u => u.EmployeeId.ToLower() == userModel.EmployeeId.ToLower() && u.IsDeleted);
                if (deletedUserWithSameId != null)
                {
                    Console.WriteLine($"WARNING: Found soft-deleted user with same Employee ID: {deletedUserWithSameId.Email}");
                }

                if (existingUserByEmployeeId != null)
                {
                    var errorMsg = $"Error: A user with Employee ID '{userModel.EmployeeId}' already exists.\n\n" +
                                  $"Existing User Details:\n" +
                                  $"• Email: {existingUserByEmployeeId.Email}\n" +
                                  $"• Name: {existingUserByEmployeeId.EnglishName}\n" +
                                  $"• Role: {existingUserByEmployeeId.Role}\n\n" +
                                  $"Please use a different Employee ID or check if this user already exists in the system.";
                    Console.WriteLine(errorMsg);
                    await JSRuntime.InvokeVoidAsync("alert", errorMsg);
                    isSaving = false;
                    StateHasChanged();
                    return;
                }

                // Create new user using proper ASP.NET Core Identity registration
                // Note: UserManager handles its own transactions, so we don't need manual transaction management
                Console.WriteLine("Creating ApplicationUser object...");
                var newUser = new ApplicationUser
                {
                    EmployeeId = userModel.EmployeeId,
                    Email = userModel.Email,
                    UserName = userModel.Email,
                    EnglishName = userModel.EnglishName,
                    ArabicName = userModel.ArabicName,
                    Role = userModel.Role,
                    PrimaryDepartmentId = userModel.PrimaryDepartmentId,
                    IsActive = userModel.IsActive,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                Console.WriteLine($"New user object created - ID: {newUser.Id}, EmployeeId: {newUser.EmployeeId}, Email: {newUser.Email}");

                // Use UserManager to properly register the user with Identity
                var password = !string.IsNullOrEmpty(userModel.Password) ? userModel.Password : "Password123!";
                Console.WriteLine($"Calling UserManager.CreateAsync with password length: {password.Length}");
                var result = await UserManager.CreateAsync(newUser, password);

                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    Console.WriteLine($"UserManager.CreateAsync failed: {errors}");
                    await JSRuntime.InvokeVoidAsync("alert", $"Error creating user: {errors}");
                    isSaving = false;
                    StateHasChanged();
                    return;
                }

                Console.WriteLine("User creation completed successfully");

                // Show success message with password
                await JSRuntime.InvokeVoidAsync("alert",
                    $"User created successfully!\n\nEmployee ID: {userModel.EmployeeId}\nPassword: {password}\n\nPlease inform the user to change their password after first login.");
            }

            await LoadData();
            CloseUserModal();
        }
        catch (Exception ex)
        {
            // Handle error - show user-friendly message
            Console.WriteLine($"Error saving user: {ex.Message}");
            Console.WriteLine($"Exception type: {ex.GetType().Name}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");

            // Check for inner exception details
            if (ex.InnerException != null)
            {
                Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                Console.WriteLine($"Inner exception type: {ex.InnerException.GetType().Name}");
            }

            // Check if it's a duplicate constraint error
            var errorMessage = ex.InnerException?.Message ?? ex.Message;
            if (errorMessage.Contains("UNIQUE constraint failed: AspNetUsers.EmployeeId"))
            {
                await JSRuntime.InvokeVoidAsync("alert",
                    L("Error", "خطأ") + ": " +
                    L("A user with this Employee ID already exists", "يوجد مستخدم بهذا الرقم الوظيفي بالفعل"));
            }
            else if (errorMessage.Contains("UNIQUE constraint failed: AspNetUsers.NormalizedEmail"))
            {
                await JSRuntime.InvokeVoidAsync("alert",
                    L("Error", "خطأ") + ": " +
                    L("A user with this email already exists", "يوجد مستخدم بهذا البريد الإلكتروني بالفعل"));
            }
            else if (errorMessage.Contains("UNIQUE constraint failed: AspNetUsers.NormalizedUserName"))
            {
                await JSRuntime.InvokeVoidAsync("alert",
                    L("Error", "خطأ") + ": " +
                    L("A user with this username already exists", "يوجد مستخدم بهذا اسم المستخدم بالفعل"));
            }
            else if (errorMessage.Contains("An error occurred while saving the entity changes"))
            {
                await JSRuntime.InvokeVoidAsync("alert",
                    L("Error", "خطأ") + ": " +
                    L("Database error occurred. Please check if the user data is valid and try again.", "حدث خطأ في قاعدة البيانات. يرجى التحقق من صحة بيانات المستخدم والمحاولة مرة أخرى."));
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert",
                    L("Error saving user", "خطأ في حفظ المستخدم") + ": " + ex.Message);
            }
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    private async Task OnInvalidSubmit()
    {
        Console.WriteLine("Form validation failed");
        await JSRuntime.InvokeVoidAsync("console.log", "Form validation failed");
        await JSRuntime.InvokeVoidAsync("alert", "Please fill in all required fields correctly.");
    }

    private void ToggleUserStatus(ApplicationUser user)
    {
        var action = user.IsActive ? L("deactivate", "إلغاء تفعيل") : L("activate", "تفعيل");
        var userName = GetUserDisplayName(user);

        confirmMessage = L($"Are you sure you want to {action} user '{userName}'?",
                          $"هل أنت متأكد من أنك تريد {action} المستخدم '{userName}'؟");

        confirmAction = async () =>
        {
            try
            {
                user.IsActive = !user.IsActive;
                user.UpdatedAt = DateTime.UtcNow;
                await DbContext.SaveChangesAsync();
                await LoadData();
                CloseConfirmModal();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error toggling user status: {ex.Message}");
            }
        };

        showConfirmModal = true;
        StateHasChanged();
    }

    private void DeleteUser(ApplicationUser user)
    {
        var userName = GetUserDisplayName(user);
        confirmMessage = L($"Are you sure you want to delete user '{userName}'? This action cannot be undone.",
                          $"هل أنت متأكد من أنك تريد حذف المستخدم '{userName}'؟ لا يمكن التراجع عن هذا الإجراء.");

        confirmAction = async () =>
        {
            try
            {
                user.IsDeleted = true;
                user.UpdatedAt = DateTime.UtcNow;
                await DbContext.SaveChangesAsync();
                await LoadData();
                CloseConfirmModal();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting user: {ex.Message}");
            }
        };

        showConfirmModal = true;
        StateHasChanged();
    }

    private async Task ExportUsers()
    {
        // TODO: Implement export functionality
        await JSRuntime.InvokeVoidAsync("alert", L("Export functionality will be implemented soon", "سيتم تنفيذ وظيفة التصدير قريباً"));
    }

    // Modal management methods
    private void CloseUserModal()
    {
        showUserModal = false;
        userModel = new UserModel();
        StateHasChanged();
    }

    private void CloseDetailsModal()
    {
        showDetailsModal = false;
        selectedUser = null;
        StateHasChanged();
    }

    private void CloseConfirmModal()
    {
        showConfirmModal = false;
        confirmMessage = "";
        confirmAction = null;
        StateHasChanged();
    }

    private async Task ConfirmAction()
    {
        if (confirmAction != null)
        {
            isSaving = true;
            StateHasChanged();

            await confirmAction();

            isSaving = false;
            StateHasChanged();
        }
    }

    // Utility methods
    private string GetUserDisplayName(ApplicationUser user)
    {
        return IsArabic ? user.ArabicName : user.EnglishName;
    }

    private string GetUserInitials(ApplicationUser user)
    {
        var name = GetUserDisplayName(user);
        var parts = name.Split(' ', StringSplitOptions.RemoveEmptyEntries);

        if (parts.Length >= 2)
        {
            return $"{parts[0][0]}{parts[1][0]}".ToUpper();
        }
        else if (parts.Length == 1)
        {
            return parts[0].Length >= 2 ? parts[0].Substring(0, 2).ToUpper() : parts[0].ToUpper();
        }

        return "??";
    }

    private string GetRoleDisplayName(UserRole role)
    {
        return role switch
        {
            UserRole.SUPER_ADMIN => L("Super Admin", "سوبر أدمين"),
            UserRole.MANAGER => L("Manager", "المدير"),
            UserRole.SUPERVISOR => L("Direct Supervisor", "المسؤول المباشر"),
            UserRole.EXCELLENCE_TEAM => L("Excellence Team", "فريق التميز"),
            UserRole.EMPLOYEE => L("Employee", "موظف"),
            _ => role.ToString()
        };
    }

    private string GetRoleBadgeColor(UserRole role)
    {
        return role switch
        {
            UserRole.SUPER_ADMIN => "danger",
            UserRole.MANAGER => "primary",
            UserRole.SUPERVISOR => "info",
            UserRole.EXCELLENCE_TEAM => "warning",
            UserRole.EMPLOYEE => "success",
            _ => "secondary"
        };
    }

    // User model for forms
    public class UserModel
    {
        public string Id { get; set; } = "";

        [Required(ErrorMessage = "Employee ID is required")]
        [StringLength(50, ErrorMessage = "Employee ID cannot exceed 50 characters")]
        public string EmployeeId { get; set; } = "";

        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Invalid email format")]
        public string Email { get; set; } = "";

        [Required(ErrorMessage = "English name is required")]
        [StringLength(255, ErrorMessage = "English name cannot exceed 255 characters")]
        public string EnglishName { get; set; } = "";

        [Required(ErrorMessage = "Arabic name is required")]
        [StringLength(255, ErrorMessage = "Arabic name cannot exceed 255 characters")]
        public string ArabicName { get; set; } = "";

        [Required(ErrorMessage = "Role is required")]
        public UserRole Role { get; set; } = UserRole.EMPLOYEE;

        public int? PrimaryDepartmentId { get; set; }

        public bool IsActive { get; set; } = true;

        public string Password { get; set; } = "";

        public string ConfirmPassword { get; set; } = "";
    }


}
</AuthenticationGuard>