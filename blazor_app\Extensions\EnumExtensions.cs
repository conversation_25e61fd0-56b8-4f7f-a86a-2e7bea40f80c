using EmployeeRatingSystem.Blazor.Models;
using System.ComponentModel.DataAnnotations;
using System.Reflection;

namespace EmployeeRatingSystem.Blazor.Extensions
{
    /// <summary>
    /// Extension methods for enums used in the Employee Rating System
    /// </summary>
    public static class EnumExtensions
    {
        /// <summary>
        /// Get display name for UserRole enum based on language
        /// </summary>
        public static string GetDisplayName(this UserRole role, string language = "en")
        {
            return role switch
            {
                UserRole.SUPER_ADMIN => language == "ar" ? "سوبر أدمين" : "Super Admin",
                UserRole.MANAGER => language == "ar" ? "المدير" : "Manager",
                UserRole.SUPERVISOR => language == "ar" ? "المسؤول المباشر" : "Direct Supervisor",
                UserRole.EXCELLENCE_TEAM => language == "ar" ? "فريق التميز" : "Excellence Team",
                UserRole.EMPLOYEE => language == "ar" ? "موظف" : "Employee",
                _ => role.ToString()
            };
        }

        /// <summary>
        /// Get display name for DepartmentRole enum based on language
        /// </summary>
        public static string GetDisplayName(this DepartmentRole role, string language = "en")
        {
            return role switch
            {
                DepartmentRole.EMPLOYEE => language == "ar" ? "موظف" : "Employee",
                DepartmentRole.TEAM_LEAD => language == "ar" ? "قائد فريق" : "Team Lead",
                DepartmentRole.SUPERVISOR => language == "ar" ? "مشرف" : "Supervisor",
                DepartmentRole.MANAGER => language == "ar" ? "مدير" : "Manager",
                DepartmentRole.DEPARTMENT_HEAD => language == "ar" ? "رئيس القسم" : "Department Head",
                _ => role.ToString()
            };
        }

        /// <summary>
        /// Get display name for EvaluationStatus enum based on language
        /// </summary>
        public static string GetDisplayName(this EvaluationStatus status, string language = "en")
        {
            return status switch
            {
                EvaluationStatus.DRAFT => language == "ar" ? "مسودة" : "Draft",
                EvaluationStatus.SUBMITTED => language == "ar" ? "مُرسل" : "Submitted",
                EvaluationStatus.APPROVED => language == "ar" ? "موافق عليه" : "Approved",
                EvaluationStatus.REJECTED => language == "ar" ? "مرفوض" : "Rejected",
                _ => status.ToString()
            };
        }

        /// <summary>
        /// Get all values of an enum with their display names
        /// </summary>
        public static Dictionary<T, string> GetDisplayNameDictionary<T>(string language = "en") where T : struct, Enum
        {
            var result = new Dictionary<T, string>();

            foreach (T value in Enum.GetValues<T>())
            {
                string displayName = value switch
                {
                    UserRole role => role.GetDisplayName(language),
                    DepartmentRole deptRole => deptRole.GetDisplayName(language),
                    EvaluationStatus status => status.GetDisplayName(language),
                    _ => GetDisplayNameFromAttribute(value) ?? value.ToString()
                };

                result[value] = displayName;
            }

            return result;
        }

        /// <summary>
        /// Get display name from DisplayAttribute if available
        /// </summary>
        private static string? GetDisplayNameFromAttribute<T>(T enumValue) where T : Enum
        {
            var field = enumValue.GetType().GetField(enumValue.ToString());
            var attribute = field?.GetCustomAttribute<DisplayAttribute>();
            return attribute?.Name;
        }

        /// <summary>
        /// Get role hierarchy level (higher number = higher authority)
        /// </summary>
        public static int GetHierarchyLevel(this UserRole role)
        {
            return role switch
            {
                UserRole.SUPER_ADMIN => 5,
                UserRole.EXCELLENCE_TEAM => 5, // Same level as SUPER_ADMIN for full access
                UserRole.MANAGER => 4,
                UserRole.SUPERVISOR => 3,
                UserRole.EMPLOYEE => 1,
                _ => 0
            };
        }

        /// <summary>
        /// Check if a role can manage another role
        /// </summary>
        public static bool CanManage(this UserRole managerRole, UserRole targetRole)
        {
            return managerRole.GetHierarchyLevel() > targetRole.GetHierarchyLevel();
        }

        /// <summary>
        /// Get all roles that this role can manage
        /// </summary>
        public static IEnumerable<UserRole> GetManageableRoles(this UserRole role)
        {
            var currentLevel = role.GetHierarchyLevel();
            return Enum.GetValues<UserRole>()
                .Where(r => r.GetHierarchyLevel() < currentLevel);
        }

        /// <summary>
        /// Check if role has management privileges
        /// </summary>
        public static bool IsManagementRole(this UserRole role)
        {
            return role == UserRole.SUPER_ADMIN ||
                   role == UserRole.EXCELLENCE_TEAM ||
                   role == UserRole.MANAGER ||
                   role == UserRole.SUPERVISOR;
        }

        /// <summary>
        /// Check if department role has management privileges
        /// </summary>
        public static bool IsManagementRole(this DepartmentRole role)
        {
            return role == DepartmentRole.MANAGER || 
                   role == DepartmentRole.DEPARTMENT_HEAD || 
                   role == DepartmentRole.SUPERVISOR;
        }

        /// <summary>
        /// Get CSS class for role badge
        /// </summary>
        public static string GetBadgeClass(this UserRole role)
        {
            return role switch
            {
                UserRole.SUPER_ADMIN => "badge bg-danger",
                UserRole.MANAGER => "badge bg-primary",
                UserRole.SUPERVISOR => "badge bg-info",
                UserRole.EXCELLENCE_TEAM => "badge bg-warning",
                UserRole.EMPLOYEE => "badge bg-secondary",
                _ => "badge bg-light text-dark"
            };
        }

        /// <summary>
        /// Get CSS class for evaluation status badge
        /// </summary>
        public static string GetBadgeClass(this EvaluationStatus status)
        {
            return status switch
            {
                EvaluationStatus.DRAFT => "badge bg-secondary",
                EvaluationStatus.SUBMITTED => "badge bg-primary",
                EvaluationStatus.APPROVED => "badge bg-success",
                EvaluationStatus.REJECTED => "badge bg-danger",
                _ => "badge bg-light text-dark"
            };
        }

        /// <summary>
        /// Get icon class for role
        /// </summary>
        public static string GetIconClass(this UserRole role)
        {
            return role switch
            {
                UserRole.SUPER_ADMIN => "fas fa-crown",
                UserRole.MANAGER => "fas fa-user-tie",
                UserRole.SUPERVISOR => "fas fa-user-check",
                UserRole.EXCELLENCE_TEAM => "fas fa-star",
                UserRole.EMPLOYEE => "fas fa-user",
                _ => "fas fa-question"
            };
        }
    }
}
