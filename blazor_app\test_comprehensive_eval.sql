-- Insert a test comprehensive evaluation record
-- First, let's check what users exist
SELECT Id, EnglishName, Role FROM AspNetUsers WHERE IsActive = 1 LIMIT 5;

-- Insert a comprehensive evaluation record manually
INSERT INTO ComprehensiveEvaluations (
    EmployeeId,
    DepartmentId,
    EvaluationPeriod,
    WorkVolumeScore,
    WorkVolumePercentage,
    AttendanceScore,
    AttendancePercentage,
    SupervisorScore,
    TotalScore,
    Status,
    CalculatedBy,
    CalculatedAt,
    CreatedAt,
    UpdatedAt,
    DepartmentRank,
    OverallRank
) VALUES (
    (SELECT Id FROM AspNetUsers WHERE IsActive = 1 LIMIT 1),  -- First active user as employee
    1,  -- Department ID
    '2025-01',  -- Evaluation period
    0.85,  -- Work volume score (85%)
    85.0,  -- Work volume percentage
    0.90,  -- Attendance score (90%)
    90.0,  -- Attendance percentage
    0.80,  -- Supervisor score (80%)
    0.85,  -- Total score (85%)
    'COMPLETED',  -- Status
    (SELECT Id FROM AspNetUsers WHERE Role = 'MANAGER' AND IsActive = 1 LIMIT 1),  -- Manager as calculator
    datetime('now'),  -- Calculated at
    datetime('now'),  -- Created at
    datetime('now'),  -- Updated at
    1,  -- Department rank
    1   -- Overall rank
);

-- Check if the record was inserted
SELECT COUNT(*) as comprehensive_evaluations_count FROM ComprehensiveEvaluations;
