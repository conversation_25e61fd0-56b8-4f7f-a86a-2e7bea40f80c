@inherits BaseChart
@inject IJSRuntime JSRuntime

@if (IsLoading)
{
    <div class="text-center py-4">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">@LoadingMessage</span>
        </div>
        <p class="mt-2 text-muted">@LoadingMessage</p>
    </div>
}
else if (PrimaryData?.Any() != true)
{
    <div class="text-center py-4 text-muted">
        <i class="fas fa-chart-line fa-2x mb-2"></i>
        <p>@L("No data available", "لا توجد بيانات متاحة")</p>
    </div>
}
else
{
    <div class="chart-container @CssClass" style="@Style">
        <canvas id="@CanvasId" width="@Width" height="@Height"></canvas>
    </div>
}

@code {
    /// <summary>
    /// Chart data labels (X-axis)
    /// </summary>
    [Parameter] public string[]? Labels { get; set; }

    /// <summary>
    /// Primary dataset data
    /// </summary>
    [Parameter] public double[]? PrimaryData { get; set; }

    /// <summary>
    /// Secondary dataset data (optional)
    /// </summary>
    [Parameter] public double[]? SecondaryData { get; set; }

    /// <summary>
    /// Primary dataset label
    /// </summary>
    [Parameter] public string PrimaryLabel { get; set; } = "Primary";

    /// <summary>
    /// Secondary dataset label
    /// </summary>
    [Parameter] public string SecondaryLabel { get; set; } = "Secondary";

    /// <summary>
    /// Primary dataset color
    /// </summary>
    [Parameter] public string PrimaryColor { get; set; } = "#1e3a8a";

    /// <summary>
    /// Secondary dataset color
    /// </summary>
    [Parameter] public string SecondaryColor { get; set; } = "#065f46";

    /// <summary>
    /// Whether to fill the area under the line
    /// </summary>
    [Parameter] public bool Fill { get; set; } = false;

    /// <summary>
    /// Line tension (0 = straight lines, 0.4 = curved)
    /// </summary>
    [Parameter] public double Tension { get; set; } = 0.4;

    protected override async Task InitializeChart()
    {
        if (PrimaryData?.Any() == true && Labels?.Any() == true)
        {
            await CreateChart();
        }
    }

    public override async Task UpdateChart()
    {
        if (PrimaryData?.Any() == true && Labels?.Any() == true)
        {
            await CreateChart();
        }
    }

    private async Task CreateChart()
    {
        try
        {
            if (SecondaryData?.Any() == true)
            {
                // Dual-axis line chart
                await JSRuntime.InvokeVoidAsync("createLineChart", CanvasId, Labels, PrimaryData, SecondaryData, Title);
            }
            else
            {
                // Single line chart
                await JSRuntime.InvokeVoidAsync("createSingleLineChart", CanvasId, Labels, PrimaryData, Title, PrimaryColor, Fill, Tension);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error creating line chart: {ex.Message}");
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        if (PrimaryData?.Any() == true && Labels?.Any() == true)
        {
            await UpdateChart();
        }
    }
}
