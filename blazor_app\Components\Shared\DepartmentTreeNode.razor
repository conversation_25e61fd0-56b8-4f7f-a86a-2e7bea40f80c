@using EmployeeRatingSystem.Blazor.Models
@using EmployeeRatingSystem.Blazor.Services

@inherits LocalizedComponentBase

<div class="department-tree-node">
    <div class="department-item @(IsExpanded ? "expanded" : "collapsed")" style="@GetIndentStyle()">
        <div class="d-flex align-items-center justify-content-between p-2 rounded @GetHoverClass()">
            <div class="d-flex align-items-center flex-grow-1">
                <!-- Expand/Collapse Button -->
                @if (HasChildren())
                {
                    <button class="btn btn-sm btn-link p-0 @GetMarginEnd(1)" @onclick="ToggleExpansion">
                        <i class="fas fa-@(IsExpanded ? "chevron-down" : "chevron-right") text-muted"></i>
                    </button>
                }
                else
                {
                    <span class="@GetMarginEnd(1)" style="width: 16px;"></span>
                }

                <!-- Department Icon -->
                <div class="department-icon @GetMarginEnd(2)">
                    <i class="fas fa-building text-primary"></i>
                </div>

                <!-- Department Info -->
                <div class="flex-grow-1">
                    <div class="d-flex align-items-center">
                        <h6 class="mb-0 @GetMarginEnd(2)">@GetDepartmentName()</h6>
                        <span class="badge bg-light text-dark @GetMarginEnd(1)">@Department.Code</span>
                        @if (!Department.IsActive)
                        {
                            <span class="badge bg-danger">@L("Inactive", "غير نشط")</span>
                        }
                    </div>
                    @if (!string.IsNullOrEmpty(GetDepartmentDescription()))
                    {
                        <small class="text-muted">@GetDepartmentDescription()</small>
                    }
                    <div class="mt-1">
                        <small class="text-muted">
                            <i class="fas fa-users @GetMarginEnd(1)"></i>
                            @Department.GetDirectEmployeeCount() @L("employees", "موظف")
                            @if (Department.Children.Any(c => !c.IsDeleted))
                            {
                                <span class="@GetMarginStart(2)">
                                    <i class="fas fa-sitemap @GetMarginEnd(1)"></i>
                                    @Department.Children.Count(c => !c.IsDeleted) @L("sub-departments", "قسم فرعي")
                                </span>
                            }
                        </small>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="department-actions">
                <div class="btn-group" role="group">
                    <button class="btn btn-sm btn-outline-primary" @onclick="() => OnEdit.InvokeAsync(Department)" 
                            title="@L("Edit", "تعديل")">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success" @onclick="() => OnAddChild.InvokeAsync(Department)"
                            title="@L("Add Sub-department", "إضافة قسم فرعي")">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" @onclick="() => OnDelete.InvokeAsync(Department)"
                            title="@L("Delete", "حذف")">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Child Departments -->
    @if (IsExpanded && HasChildren())
    {
        <div class="department-children">
            @foreach (var child in GetActiveChildren())
            {
                <DepartmentTreeNode Department="child"
                                  IsExpanded="IsChildExpanded(child.Id)"
                                  OnToggleExpand="OnToggleExpand"
                                  OnEdit="OnEdit"
                                  OnDelete="OnDelete"
                                  OnAddChild="OnAddChild"
                                  IsArabic="IsArabic" />
            }
        </div>
    }
</div>

@code {
    [Parameter] public Department Department { get; set; } = default!;
    [Parameter] public bool IsExpanded { get; set; }
    [Parameter] public EventCallback<int> OnToggleExpand { get; set; }
    [Parameter] public EventCallback<Department> OnEdit { get; set; }
    [Parameter] public EventCallback<Department> OnDelete { get; set; }
    [Parameter] public EventCallback<Department> OnAddChild { get; set; }
    [Parameter] public bool IsArabic { get; set; }

    private bool HasChildren()
    {
        return Department.Children.Any(c => !c.IsDeleted);
    }

    private IEnumerable<Department> GetActiveChildren()
    {
        return Department.Children
            .Where(c => !c.IsDeleted)
            .OrderBy(c => c.Order)
            .ThenBy(c => IsArabic ? c.NameAr : c.NameEn);
    }

    private bool IsChildExpanded(int childId)
    {
        // This would need to be passed down from parent or managed differently
        return false; // For now, children start collapsed
    }

    private string GetDepartmentName()
    {
        return IsArabic ? Department.NameAr : Department.NameEn;
    }

    private string GetDepartmentDescription()
    {
        return IsArabic ? Department.DescriptionAr : Department.DescriptionEn;
    }

    private string GetIndentStyle()
    {
        return $"margin-{(IsArabic ? "right" : "left")}: {Department.Level * 20}px;";
    }

    private string GetHoverClass()
    {
        return "department-item-hover";
    }

    private async Task ToggleExpansion()
    {
        await OnToggleExpand.InvokeAsync(Department.Id);
    }
}

<style>
    .department-tree-node {
        margin-bottom: 2px;
    }

    .department-item-hover:hover {
        background-color: var(--bs-light) !important;
    }

    .department-actions {
        opacity: 0;
        transition: opacity 0.2s ease;
    }

    .department-item:hover .department-actions {
        opacity: 1;
    }

    .department-icon {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .department-children {
        border-left: 2px solid var(--bs-border-color);
        margin-left: 12px;
        padding-left: 8px;
    }

    .rtl-layout .department-children {
        border-left: none;
        border-right: 2px solid var(--bs-border-color);
        margin-left: 0;
        margin-right: 12px;
        padding-left: 0;
        padding-right: 8px;
    }
</style>
