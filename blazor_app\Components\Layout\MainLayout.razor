@using System.Globalization
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Identity
@using EmployeeRatingSystem.Blazor.Components.Shared
@using EmployeeRatingSystem.Blazor.Services
@using EmployeeRatingSystem.Blazor.Models
@inherits LocalizedLayoutComponentBase
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserManager<ApplicationUser> UserManager

<div class="page @GetBodyClass()" dir="@Direction" lang="@CurrentLanguage">
    <header class="header-section">
        <!-- Enhanced Navigation -->
        <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm fixed-top">
            <div class="container">
                <a class="navbar-brand d-flex align-items-center" href="/">
                    <i class="fas fa-chart-line @GetMarginEnd() text-primary"></i>
                    <span class="fw-bold">@L("Employee Rating System", "نظام تقييم الموظفين")</span>
                </a>

                <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                        aria-controls="navbarNav" aria-expanded="false" aria-label="@L("Toggle navigation", "تبديل التنقل")">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    @if (isAuthenticated)
                    {
                        <ul class="navbar-nav @(IsRTL ? "ms-auto" : "me-auto")">
                            <li class="nav-item">
                                <NavLink class="nav-link" href="/dashboard" Match="NavLinkMatch.Prefix">
                                    <i class="fas fa-tachometer-alt @GetMarginEnd(1)"></i>
                                    @L("Dashboard", "لوحة التحكم")
                                </NavLink>
                            </li>

                            @if (CanAccessDepartments())
                            {
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" id="departmentsDropdown" role="button"
                                       data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fas fa-sitemap @GetMarginEnd(1)"></i>
                                        @L("Departments", "الأقسام")
                                    </a>
                                    <ul class="dropdown-menu" aria-labelledby="departmentsDropdown">
                                        <li><NavLink class="dropdown-item" href="/departments">
                                            <i class="fas fa-list @GetMarginEnd(1)"></i>
                                            @L("View All", "عرض الكل")
                                        </NavLink></li>
                                        <li><NavLink class="dropdown-item" href="/departments/create">
                                            <i class="fas fa-plus @GetMarginEnd(1)"></i>
                                            @L("Create New", "إنشاء جديد")
                                        </NavLink></li>
                                    </ul>
                                </li>
                            }

                            @if (CanAccessEvaluations())
                            {
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" id="evaluationsDropdown" role="button"
                                       data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fas fa-clipboard-list @GetMarginEnd(1)"></i>
                                        @L("Evaluations", "التقييمات")
                                    </a>
                                    <ul class="dropdown-menu" aria-labelledby="evaluationsDropdown">
                                        <li><NavLink class="dropdown-item" href="/evaluations">
                                            <i class="fas fa-list @GetMarginEnd(1)"></i>
                                            @L("View All", "عرض الكل")
                                        </NavLink></li>
                                        <li><NavLink class="dropdown-item" href="/evaluations/comprehensive">
                                            <i class="fas fa-calculator @GetMarginEnd(1)"></i>
                                            @L("Comprehensive Evaluation", "التقييم الشهري")
                                        </NavLink></li>
                                        <li><NavLink class="dropdown-item" href="/evaluations/quarterly">
                                            <i class="fas fa-calendar-alt @GetMarginEnd(1)"></i>
                                            @L("Quarterly Evaluation", "التقييم الربع السنوي")
                                        </NavLink></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><NavLink class="dropdown-item" href="/evaluations/reports">
                                            <i class="fas fa-chart-bar @GetMarginEnd(1)"></i>
                                            @L("Reports", "التقارير")
                                        </NavLink></li>
                                    </ul>
                                </li>
                            }

                            @if (CanAccessUsers())
                            {
                                <li class="nav-item">
                                    <NavLink class="nav-link" href="/users" Match="NavLinkMatch.Prefix">
                                        <i class="fas fa-users @GetMarginEnd(1)"></i>
                                        @L("Users", "المستخدمون")
                                    </NavLink>
                                </li>
                            }

                            @if (CanAccessEmployeeOfTheMonth())
                            {
                                <li class="nav-item">
                                    <NavLink class="nav-link" href="/employee-of-the-month" Match="NavLinkMatch.Prefix">
                                        <i class="fas fa-trophy @GetMarginEnd(1) text-warning"></i>
                                        @L("Employee of the Month", "موظف الشهر")
                                    </NavLink>
                                </li>
                            }
                        </ul>

                        <ul class="navbar-nav">
                            @if (isAuthenticated && currentUser != null)
                            {
                                <!-- User Menu Dropdown (when authenticated) -->
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button"
                                       data-bs-toggle="dropdown" aria-expanded="false">
                                        <div class="profile-avatar profile-avatar-sm @GetMarginEnd(1)">
                                            @GetUserInitials(currentUser)
                                        </div>
                                        <span class="d-none d-md-inline">@GetUserDisplayName(currentUser)</span>
                                    </a>
                                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                        <li><h6 class="dropdown-header">
                                            @GetUserDisplayName(currentUser)
                                            <br><small class="text-muted">@GetRoleDisplayName(currentUser.Role)</small>
                                        </h6></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="/profile">
                                            <i class="fas fa-user-circle @GetMarginEnd(1)"></i>
                                            @L("Profile", "الملف الشخصي")
                                        </a></li>
                                        <li><a class="dropdown-item" href="/settings">
                                            <i class="fas fa-cog @GetMarginEnd(1)"></i>
                                            @L("Settings", "الإعدادات")
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="/logout">
                                            <i class="fas fa-sign-out-alt @GetMarginEnd(1)"></i>
                                            @L("Logout", "تسجيل الخروج")
                                        </a></li>
                                    </ul>
                                </li>
                            }
                            else
                            {
                                <!-- Login Link (when not authenticated) -->
                                <li class="nav-item">
                                    <NavLink class="nav-link" href="/login">
                                        <i class="fas fa-sign-in-alt @GetMarginEnd(1)"></i>
                                        @L("Login", "تسجيل الدخول")
                                    </NavLink>
                                </li>
                            }

                            <!-- Enhanced Language Switcher -->
                            <li class="nav-item d-flex align-items-center @GetMarginStart(2)">
                                <LanguageSwitcher ShowDropdown="false" Size="sm" />
                            </li>
                        </ul>
                    }
                    else
                    {
                        <!-- Navigation for non-authenticated users -->
                        <ul class="navbar-nav ms-auto">
                            <li class="nav-item">
                                <NavLink class="nav-link" href="/login">
                                    <i class="fas fa-sign-in-alt @GetMarginEnd(1)"></i>
                                    @L("Login", "تسجيل الدخول")
                                </NavLink>
                            </li>
                            <li class="nav-item">
                                <NavLink class="nav-link" href="/register">
                                    <i class="fas fa-user-plus @GetMarginEnd(1)"></i>
                                    @L("Register", "التسجيل")
                                </NavLink>
                            </li>
                            <!-- Language Switcher for non-authenticated users -->
                            <li class="nav-item d-flex align-items-center @GetMarginStart(2)">
                                <LanguageSwitcher ShowDropdown="false" Size="sm" />
                            </li>
                        </ul>
                    }
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <div class="content-wrapper fade-in">
                @Body
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">
                        @L("© 2025 Employee Rating System. All rights reserved.", "© 2025 نظام تقييم الموظفين. جميع الحقوق محفوظة.")
                    </p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0">
                        @L("Version 2.0 - Enhanced Enterprise Edition", "الإصدار 2.0 - النسخة المحسنة للمؤسسات")
                    </p>
                </div>
            </div>
        </div>
    </footer>
</div>

<div id="blazor-error-ui">
    An unhandled error has occurred.
    <a href="" class="reload">Reload</a>
    <a class="dismiss">🗙</a>
</div>

@code {
    private bool isAuthenticated = false;
    private ApplicationUser? currentUser = null;

    protected override async Task OnInitializedAsync()
    {
        await LoadAuthenticationState();
    }

    private async Task LoadAuthenticationState()
    {
        try
        {
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            isAuthenticated = authState.User.Identity?.IsAuthenticated ?? false;

            if (isAuthenticated)
            {
                var userEmail = authState.User.Identity?.Name;
                if (!string.IsNullOrEmpty(userEmail))
                {
                    currentUser = await UserManager.FindByEmailAsync(userEmail);
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading authentication state: {ex.Message}");
            isAuthenticated = false;
            currentUser = null;
        }
    }

    private new string GetBodyClass()
    {
        return GetLayoutClass();
    }

    private string Direction => IsRTL ? "rtl" : "ltr";
    private string CurrentLanguage => IsArabic ? "ar" : "en";

    // Role-based access control methods
    private bool CanAccessDepartments()
    {
        if (!isAuthenticated || currentUser == null) return false;
        return currentUser.Role == UserRole.SUPER_ADMIN ||
               currentUser.Role == UserRole.EXCELLENCE_TEAM ||
               currentUser.Role == UserRole.MANAGER;
    }

    private bool CanCreateDepartments()
    {
        if (!isAuthenticated || currentUser == null) return false;
        return currentUser.Role == UserRole.SUPER_ADMIN ||
               currentUser.Role == UserRole.EXCELLENCE_TEAM;
    }

    private bool CanAccessEvaluations()
    {
        if (!isAuthenticated || currentUser == null) return false;
        return currentUser.Role != UserRole.EMPLOYEE; // All except employees
    }

    private bool CanAccessUsers()
    {
        if (!isAuthenticated || currentUser == null) return false;
        return currentUser.Role == UserRole.SUPER_ADMIN ||
               currentUser.Role == UserRole.EXCELLENCE_TEAM ||
               currentUser.Role == UserRole.MANAGER;
    }

    private bool CanAccessEmployeeOfTheMonth()
    {
        if (!isAuthenticated || currentUser == null) return false;
        return currentUser.Role == UserRole.SUPER_ADMIN ||
               currentUser.Role == UserRole.EXCELLENCE_TEAM;
    }

    // Helper methods for user display
    private string GetUserDisplayName(ApplicationUser? user)
    {
        if (user == null) return "";
        return IsArabic ? user.ArabicName : user.EnglishName;
    }

    private string GetUserInitials(ApplicationUser? user)
    {
        if (user == null) return "U";
        var name = GetUserDisplayName(user);
        if (string.IsNullOrEmpty(name)) return "U";

        var parts = name.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        if (parts.Length >= 2)
        {
            return $"{parts[0][0]}{parts[1][0]}".ToUpper();
        }
        return parts[0][0].ToString().ToUpper();
    }

    private string GetRoleDisplayName(UserRole role)
    {
        return role switch
        {
            UserRole.SUPER_ADMIN => L("Super Admin", "سوبر أدمين"),
            UserRole.MANAGER => L("Manager", "المدير"),
            UserRole.SUPERVISOR => L("Direct Supervisor", "المسؤول المباشر"),
            UserRole.EXCELLENCE_TEAM => L("Excellence Team", "فريق التميز"),
            UserRole.EMPLOYEE => L("Employee", "موظف"),
            _ => L("Unknown", "غير معروف")
        };
    }
}
o