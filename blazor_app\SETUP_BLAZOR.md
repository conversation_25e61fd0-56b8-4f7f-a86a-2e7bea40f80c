# Setup Guide - Employee Rating System (Blazor)

## 🎯 Overview
This guide provides step-by-step instructions for setting up and running the Employee Rating System Blazor Server application.

## 📋 Prerequisites

### Required Software
- **.NET 8.0 SDK** or later
- **Visual Studio 2022** (recommended) or **Visual Studio Code**
- **Git** for version control
- **SQL Server** (production) or **SQLite** (development)

### Optional Software
- **PostgreSQL** (alternative production database)
- **Docker** (for containerized deployment)
- **IIS** (for Windows production deployment)

### System Requirements
- **OS**: Windows 10/11, macOS, or Linux
- **RAM**: Minimum 4GB, recommended 8GB+
- **Storage**: 2GB free space
- **Browser**: Chrome, Firefox, Safari, or Edge (latest versions)

## 🚀 Quick Start (Development)

### 1. Clone Repository
```bash
git clone <repository-url>
cd employee_rating_blazor
```

### 2. Restore Dependencies
```bash
cd blazor_app
dotnet restore
```

### 3. Setup Database
```bash
# Create and apply migrations
dotnet ef database update
```

### 4. Run Application
```bash
dotnet run
```

### 5. Access Application
- Open browser to: `http://localhost:5222`
- Login with demo credentials (see DEMO_CREDENTIALS.md)

## 🔧 Detailed Setup Instructions

### Step 1: Environment Setup

#### Install .NET 8.0 SDK
```bash
# Windows (using winget)
winget install Microsoft.DotNet.SDK.8

# macOS (using Homebrew)
brew install dotnet

# Linux (Ubuntu/Debian)
sudo apt-get update
sudo apt-get install -y dotnet-sdk-8.0
```

#### Verify Installation
```bash
dotnet --version
# Should show 8.0.x or later
```

### Step 2: Database Configuration

#### Option A: SQLite (Development - Default)
No additional setup required. Database file will be created automatically.

```json
// appsettings.json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=employee_rating.db"
  },
  "DATABASE_ENGINE": "sqlite"
}
```

#### Option B: PostgreSQL (Production)
1. Install PostgreSQL server
2. Create database and user
3. Update configuration

```bash
# Create database
createdb employee_rating
createuser -P employee_rating_user
```

```json
// appsettings.json
{
  "ConnectionStrings": {
    "PostgreSQL": "Host=localhost;Database=employee_rating;Username=employee_rating_user;Password=your_password"
  },
  "DATABASE_ENGINE": "postgresql"
}
```

#### Option C: SQL Server (Production)
1. Install SQL Server
2. Create database
3. Update configuration

```json
// appsettings.json
{
  "ConnectionStrings": {
    "SqlServer": "Server=localhost;Database=EmployeeRating;Trusted_Connection=true;TrustServerCertificate=true"
  },
  "DATABASE_ENGINE": "sqlserver"
}
```

### Step 3: Application Configuration

#### Environment Variables
Create `.env` file or set environment variables:

```env
# Database Configuration
DATABASE_ENGINE=sqlite
POSTGRES_HOST=localhost
POSTGRES_DB=employee_rating
POSTGRES_USER=your_user
POSTGRES_PASSWORD=your_password

# Application Settings
ASPNETCORE_ENVIRONMENT=Development
ASPNETCORE_URLS=http://localhost:5222
```

#### appsettings Configuration
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=employee_rating.db",
    "PostgreSQL": "",
    "SqlServer": ""
  },
  "DATABASE_ENGINE": "sqlite",
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "Application": {
    "Name": "Employee Rating System",
    "NameAr": "نظام تقييم الموظفين",
    "Version": "2.0.0",
    "DefaultLanguage": "en",
    "SupportedLanguages": ["en", "ar"]
  }
}
```

### Step 4: Database Migration

#### Create Initial Migration (if needed)
```bash
dotnet ef migrations add InitialCreate
```

#### Apply Migrations
```bash
dotnet ef database update
```

#### Verify Database Setup
```bash
# Check migration status
dotnet ef migrations list

# Check database connection
dotnet ef dbcontext info
```

### Step 5: Run Application

#### Development Mode
```bash
dotnet run
```

#### Production Mode
```bash
dotnet run --environment Production
```

#### With Specific URL
```bash
dotnet run --urls "http://localhost:5222"
```

## 🌐 Production Deployment

### Option 1: IIS Deployment (Windows)

#### 1. Publish Application
```bash
dotnet publish -c Release -o ./publish
```

#### 2. Configure IIS
- Install ASP.NET Core Hosting Bundle
- Create new website in IIS Manager
- Point to published folder
- Configure application pool for "No Managed Code"

#### 3. Update Configuration
```json
// appsettings.Production.json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=prod-server;Database=EmployeeRating;..."
  },
  "Logging": {
    "LogLevel": {
      "Default": "Warning"
    }
  }
}
```

### Option 2: Linux Deployment

#### 1. Install .NET Runtime
```bash
sudo apt-get install -y aspnetcore-runtime-8.0
```

#### 2. Publish and Deploy
```bash
dotnet publish -c Release -o /var/www/employee-rating
```

#### 3. Configure Systemd Service
```ini
# /etc/systemd/system/employee-rating.service
[Unit]
Description=Employee Rating System
After=network.target

[Service]
Type=notify
ExecStart=/usr/bin/dotnet /var/www/employee-rating/EmployeeRatingSystem.Blazor.dll
Restart=always
RestartSec=10
User=www-data
Environment=ASPNETCORE_ENVIRONMENT=Production

[Install]
WantedBy=multi-user.target
```

#### 4. Start Service
```bash
sudo systemctl enable employee-rating
sudo systemctl start employee-rating
```

### Option 3: Docker Deployment

#### 1. Create Dockerfile
```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["EmployeeRatingSystem.Blazor.csproj", "."]
RUN dotnet restore
COPY . .
RUN dotnet build -c Release -o /app/build

FROM build AS publish
RUN dotnet publish -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "EmployeeRatingSystem.Blazor.dll"]
```

#### 2. Build and Run
```bash
docker build -t employee-rating .
docker run -p 8080:80 employee-rating
```

## 🔒 Security Configuration

### HTTPS Configuration
```json
// appsettings.Production.json
{
  "Kestrel": {
    "Endpoints": {
      "Https": {
        "Url": "https://localhost:5001",
        "Certificate": {
          "Path": "certificate.pfx",
          "Password": "certificate_password"
        }
      }
    }
  }
}
```

### Security Headers
```csharp
// Program.cs additions
app.UseHsts();
app.UseHttpsRedirection();
app.Use(async (context, next) =>
{
    context.Response.Headers.Add("X-Frame-Options", "DENY");
    context.Response.Headers.Add("X-Content-Type-Options", "nosniff");
    await next();
});
```

## 📊 Performance Optimization

### Database Optimization
```bash
# Enable connection pooling
dotnet add package Microsoft.Extensions.ObjectPool
```

### Caching Configuration
```csharp
// Program.cs
builder.Services.AddMemoryCache();
builder.Services.AddResponseCaching();
```

### Compression
```csharp
// Program.cs
builder.Services.AddResponseCompression();
```

## 🧪 Testing Setup

### Unit Testing
```bash
# Create test project
dotnet new xunit -n EmployeeRatingSystem.Tests
cd EmployeeRatingSystem.Tests
dotnet add reference ../blazor_app/EmployeeRatingSystem.Blazor.csproj
```

### Integration Testing
```bash
# Add test packages
dotnet add package Microsoft.AspNetCore.Mvc.Testing
dotnet add package Microsoft.EntityFrameworkCore.InMemory
```

### Run Tests
```bash
dotnet test
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Database Connection Errors
```bash
# Check connection string
dotnet ef dbcontext info

# Reset database
dotnet ef database drop
dotnet ef database update
```

#### 2. Migration Issues
```bash
# Remove last migration
dotnet ef migrations remove

# Reset migrations
rm -rf Migrations/
dotnet ef migrations add InitialCreate
dotnet ef database update
```

#### 3. Port Conflicts
```bash
# Use different port
dotnet run --urls "http://localhost:5223"
```

#### 4. Permission Issues (Linux)
```bash
# Fix file permissions
sudo chown -R www-data:www-data /var/www/employee-rating
sudo chmod -R 755 /var/www/employee-rating
```

### Logging and Diagnostics
```json
// appsettings.Development.json
{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Microsoft.EntityFrameworkCore": "Information"
    }
  }
}
```

## 📞 Support and Resources

### Documentation
- [ASP.NET Core Documentation](https://docs.microsoft.com/aspnet/core)
- [Blazor Documentation](https://docs.microsoft.com/aspnet/core/blazor)
- [Entity Framework Core](https://docs.microsoft.com/ef/core)

### Community Support
- [ASP.NET Core GitHub](https://github.com/dotnet/aspnetcore)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/blazor)
- [.NET Community](https://dotnet.microsoft.com/community)

### Project-Specific Help
- Check `DEMO_CREDENTIALS.md` for test accounts
- Review `TESTING_CHECKLIST.md` for functionality verification
- See `BLAZOR_IMPLEMENTATION_SUMMARY.md` for architecture details

---

**🎉 Setup Complete!**

Your Employee Rating System Blazor application should now be running successfully. Access it at the configured URL and login with the demo credentials to start testing all features.
