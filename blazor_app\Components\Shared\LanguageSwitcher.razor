@rendermode InteractiveServer
@using EmployeeRatingSystem.Blazor.Components.Shared
@using EmployeeRatingSystem.Blazor.Services
@inherits LocalizedComponentBase
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation

<div class="language-switcher @GetLayoutClass()">
    @if (ShowDropdown)
    {
        <!-- Dropdown Language Switcher -->
        <div class="dropdown">
            <button class="btn btn-outline-primary btn-sm dropdown-toggle" type="button" 
                    id="languageDropdown" data-bs-toggle="dropdown" aria-expanded="false"
                    title="@GetLanguageSwitchTitle()">
                <i class="fas fa-globe @GetMarginEnd(1)"></i>
                @GetCurrentLanguageDisplay()
            </button>
            <ul class="dropdown-menu" aria-labelledby="languageDropdown">
                <li>
                    <button class="dropdown-item @(IsArabic ? "" : "active")" 
                            @onclick="@(() => SwitchLanguage("en"))"
                            disabled="@(!IsArabic)">
                        <i class="fas fa-check @GetMarginEnd(1) @(IsArabic ? "invisible" : "")"></i>
                        English
                    </button>
                </li>
                <li>
                    <button class="dropdown-item @(IsArabic ? "active" : "")" 
                            @onclick="@(() => SwitchLanguage("ar"))"
                            disabled="@IsArabic">
                        <i class="fas fa-check @GetMarginEnd(1) @(IsArabic ? "" : "invisible")"></i>
                        العربية
                    </button>
                </li>
            </ul>
        </div>
    }
    else
    {
        <!-- Toggle Button Language Switcher -->
        <div class="btn-group" role="group" aria-label="@L("Language Selection", "اختيار اللغة")">
            <button type="button" 
                    class="btn @(IsArabic ? "btn-outline-primary" : "btn-primary") btn-sm"
                    @onclick="@(() => SwitchLanguage("en"))"
                    title="@L("Switch to English", "التبديل إلى الإنجليزية")"
                    disabled="@(!IsArabic)">
                EN
            </button>
            <button type="button" 
                    class="btn @(IsArabic ? "btn-primary" : "btn-outline-primary") btn-sm"
                    @onclick="@(() => SwitchLanguage("ar"))"
                    title="@L("Switch to Arabic", "التبديل إلى العربية")"
                    disabled="@IsArabic">
                ع
            </button>
        </div>
    }
</div>

@code {
    /// <summary>
    /// Whether to show as dropdown or toggle buttons
    /// </summary>
    [Parameter] public bool ShowDropdown { get; set; } = false;

    /// <summary>
    /// Size of the switcher (sm, md, lg)
    /// </summary>
    [Parameter] public string Size { get; set; } = "sm";

    /// <summary>
    /// Whether to show icons
    /// </summary>
    [Parameter] public bool ShowIcons { get; set; } = true;

    /// <summary>
    /// Custom CSS class
    /// </summary>
    [Parameter] public string CssClass { get; set; } = "";

    /// <summary>
    /// Callback when language is changed
    /// </summary>
    [Parameter] public EventCallback<string> OnLanguageChanged { get; set; }

    private string GetCurrentLanguageDisplay()
    {
        return IsArabic ? "العربية" : "English";
    }

    private string GetLanguageSwitchTitle()
    {
        return L("Change Language", "تغيير اللغة");
    }

    private async Task SwitchLanguage(string culture)
    {
        try
        {
            // Set the culture cookie using the correct ASP.NET Core localization cookie name
            await JSRuntime.InvokeVoidAsync("eval",
                $"document.cookie = \".AspNetCore.Culture=c={culture}|uic={culture}; path=/; expires=\" + new Date(Date.now() + 365*24*60*60*1000).toUTCString();");
            
            // Notify parent component
            if (OnLanguageChanged.HasDelegate)
            {
                await OnLanguageChanged.InvokeAsync(culture);
            }

            // Reload the page to apply the new culture
            await Task.Delay(100); // Small delay to ensure cookie is set
            Navigation.NavigateTo(Navigation.Uri, forceLoad: true);
        }
        catch (Exception ex)
        {
            // Log error or show notification
            Console.WriteLine($"Error switching language: {ex.Message}");
        }
    }

    private string GetButtonSizeClass()
    {
        return Size switch
        {
            "lg" => "btn-lg",
            "sm" => "btn-sm",
            _ => ""
        };
    }
}

<style>
    .language-switcher {
        display: flex;
        align-items: center;
    }

    .language-switcher .dropdown-item.active {
        background-color: var(--primary-color);
        color: white;
    }

    .language-switcher .dropdown-item:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    .language-switcher .btn-group .btn {
        min-width: 40px;
        font-weight: 600;
    }

    .language-switcher .btn-group .btn:disabled {
        opacity: 1;
    }

    /* RTL specific adjustments */
    .rtl-layout .language-switcher .dropdown-menu {
        right: 0;
        left: auto;
    }

    .rtl-layout .language-switcher .btn-group {
        flex-direction: row-reverse;
    }

    /* Animation for smooth transitions */
    .language-switcher .btn {
        transition: all 0.2s ease-in-out;
    }

    .language-switcher .dropdown-item {
        transition: all 0.2s ease-in-out;
    }

    /* Hover effects */
    .language-switcher .dropdown-item:hover:not(:disabled) {
        background-color: var(--bg-secondary);
        color: var(--primary-color);
    }

    .language-switcher .btn:hover:not(:disabled) {
        transform: translateY(-1px);
    }
</style>
