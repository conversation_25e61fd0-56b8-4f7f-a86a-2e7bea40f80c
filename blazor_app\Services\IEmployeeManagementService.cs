using EmployeeRatingSystem.Blazor.Models;

namespace EmployeeRatingSystem.Blazor.Services
{
    /// <summary>
    /// Service interface for managing employee authentication, role assignment, and department relationships.
    /// Handles automatic role assignment based on Employee ID and department management.
    /// </summary>
    public interface IEmployeeManagementService
    {
        /// <summary>
        /// Get user by Employee ID for authentication
        /// </summary>
        Task<ApplicationUser?> GetUserByEmployeeIdAsync(string employeeId);

        /// <summary>
        /// Determine role and department assignment for an Employee ID
        /// </summary>
        Task<(UserRole role, int? departmentId)> GetRoleAndDepartmentAssignmentAsync(string employeeId);

        /// <summary>
        /// Check if Employee ID is pre-configured for specific role/department
        /// </summary>
        Task<bool> IsEmployeeIdPreConfiguredAsync(string employeeId);

        /// <summary>
        /// Assign role and department to user (Super Admin function)
        /// </summary>
        Task<bool> AssignRoleAndDepartmentAsync(string userId, UserRole role, int? departmentId, string assignedByUserId);

        /// <summary>
        /// Get all departments accessible to a user based on their role and assignments
        /// </summary>
        Task<IEnumerable<Department>> GetAccessibleDepartmentsAsync(string userId);

        /// <summary>
        /// Check if user can evaluate another user based on role and department hierarchy
        /// </summary>
        Task<bool> CanUserEvaluateAsync(string evaluatorUserId, string targetUserId);

        /// <summary>
        /// Get all users that a user can evaluate
        /// </summary>
        Task<IEnumerable<ApplicationUser>> GetEvaluatableUsersAsync(string evaluatorUserId);

        /// <summary>
        /// Get department hierarchy for a user (departments they manage or supervise)
        /// </summary>
        Task<IEnumerable<Department>> GetManagedDepartmentsAsync(string userId);

        /// <summary>
        /// Check if user has management role in any department
        /// </summary>
        Task<bool> HasManagementRoleAsync(string userId);

        /// <summary>
        /// Get user's role in a specific department
        /// </summary>
        Task<DepartmentRole?> GetUserRoleInDepartmentAsync(string userId, int departmentId);

        /// <summary>
        /// Create employee pre-configuration for automatic assignment
        /// </summary>
        Task<bool> CreateEmployeePreConfigurationAsync(string employeeId, UserRole role, int? departmentId, string createdByUserId);

        /// <summary>
        /// Update employee pre-configuration
        /// </summary>
        Task<bool> UpdateEmployeePreConfigurationAsync(string employeeId, UserRole role, int? departmentId, string updatedByUserId);

        /// <summary>
        /// Delete employee pre-configuration
        /// </summary>
        Task<bool> DeleteEmployeePreConfigurationAsync(string employeeId, string deletedByUserId);

        /// <summary>
        /// Get all employee pre-configurations
        /// </summary>
        Task<IEnumerable<EmployeePreConfiguration>> GetAllEmployeePreConfigurationsAsync();
    }
}
