<!DOCTYPE html>
<html>
<head>
    <title>Authentication Fix Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .btn { padding: 10px 20px; margin: 10px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .btn.success { background: #28a745; }
        .btn.danger { background: #dc3545; }
        .log { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin: 10px 0; border-radius: 5px; max-height: 400px; overflow-y: auto; }
        .status { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .status.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .status.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #dee2e6; padding: 8px; text-align: left; }
        th { background: #f8f9fa; }
        .badge { padding: 2px 8px; border-radius: 3px; font-size: 12px; }
        .badge.success { background: #28a745; color: white; }
        .badge.danger { background: #dc3545; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Authentication Fix Tool</h1>
        <p>This tool will fix authentication issues for users EMP006 and EMP007 who lack password hashes.</p>
        
        <div>
            <button class="btn" onclick="checkStatus()">📊 Check User Status</button>
            <button class="btn success" onclick="fixAuthentication()">🔧 Fix Authentication Issues</button>
            <button class="btn" onclick="testLogins()">🧪 Test All Logins</button>
        </div>

        <div id="status"></div>
        <div id="userTable"></div>
        <div id="logs" class="log" style="display: none;">
            <h3>Debug Logs</h3>
            <div id="logContent"></div>
        </div>
    </div>

    <script>
        let logs = [];

        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            logs.push(`[${timestamp}] ${message}`);
            updateLogDisplay();
        }

        function updateLogDisplay() {
            const logDiv = document.getElementById('logs');
            const logContent = document.getElementById('logContent');
            logContent.innerHTML = logs.map(log => `<div>${log}</div>`).join('');
            logDiv.style.display = 'block';
            logContent.scrollTop = logContent.scrollHeight;
        }

        function showStatus(message, isError = false) {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${isError ? 'error' : 'success'}">${message}</div>`;
        }

        async function checkStatus() {
            addLog('Checking user authentication status...');
            try {
                const response = await fetch('/api/auth/status');
                if (response.ok) {
                    const users = await response.json();
                    displayUserTable(users);
                    addLog(`Loaded ${users.length} users`);
                    
                    const incompleteUsers = users.filter(u => !u.hasPassword);
                    if (incompleteUsers.length > 0) {
                        showStatus(`Found ${incompleteUsers.length} users with authentication issues: ${incompleteUsers.map(u => u.employeeId).join(', ')}`, true);
                    } else {
                        showStatus('All users have proper authentication setup!', false);
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                addLog(`Error checking status: ${error.message}`);
                showStatus(`Error checking status: ${error.message}`, true);
            }
        }

        async function fixAuthentication() {
            addLog('Starting authentication fix process...');
            showStatus('Fixing authentication issues...', false);
            
            try {
                const response = await fetch('/api/auth/fix', { method: 'POST' });
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addLog('✓ Authentication fix completed successfully');
                    showStatus('Authentication issues fixed successfully! All users should now be able to log in.', false);
                    
                    // Refresh status after fix
                    setTimeout(() => checkStatus(), 1000);
                } else {
                    throw new Error(result.message || 'Fix process failed');
                }
            } catch (error) {
                addLog(`✗ Error during fix: ${error.message}`);
                showStatus(`Error during fix: ${error.message}`, true);
            }
        }

        async function testLogins() {
            addLog('Testing login functionality for all users...');
            
            const testUsers = [
                { id: 'EMP001', password: 'Password123!' },
                { id: 'EMP002', password: 'Password123!' },
                { id: 'EMP003', password: 'Password123!' },
                { id: 'EMP004', password: 'Password123!' },
                { id: 'EMP005', password: 'Password123!' },
                { id: 'EMP006', password: 'Password123!' },
                { id: 'EMP007', password: 'Fa35108981' },
                { id: 'EMP008', password: 'Password123!' }
            ];

            let successCount = 0;
            let failCount = 0;

            for (const user of testUsers) {
                try {
                    const formData = new FormData();
                    formData.append('employeeId', user.id);
                    formData.append('password', user.password);
                    formData.append('rememberMe', 'false');
                    formData.append('returnUrl', '/dashboard');

                    const response = await fetch('/api/auth/login', {
                        method: 'POST',
                        body: formData
                    });

                    if (response.ok || response.status === 302) {
                        addLog(`✓ ${user.id}: Login test SUCCESS`);
                        successCount++;
                    } else {
                        addLog(`✗ ${user.id}: Login test FAILED (${response.status})`);
                        failCount++;
                    }
                } catch (error) {
                    addLog(`✗ ${user.id}: Login test ERROR - ${error.message}`);
                    failCount++;
                }
            }

            const message = `Login testing completed: ${successCount} SUCCESS, ${failCount} FAILED`;
            addLog(message);
            showStatus(message, failCount > 0);
        }

        function displayUserTable(users) {
            const tableDiv = document.getElementById('userTable');
            
            let tableHTML = `
                <h3>User Authentication Status</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Employee ID</th>
                            <th>Name</th>
                            <th>Role</th>
                            <th>Is Active</th>
                            <th>Has Password</th>
                            <th>Can Authenticate</th>
                            <th>Expected Password</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            users.forEach(user => {
                const rowClass = user.canAuthenticate ? 'success' : 'danger';
                tableHTML += `
                    <tr>
                        <td><strong>${user.employeeId}</strong></td>
                        <td>${user.name}</td>
                        <td>${user.role}</td>
                        <td><span class="badge ${user.isActive ? 'success' : 'danger'}">${user.isActive ? 'ACTIVE' : 'INACTIVE'}</span></td>
                        <td><span class="badge ${user.hasPassword ? 'success' : 'danger'}">${user.hasPassword ? 'YES' : 'NO'}</span></td>
                        <td><span class="badge ${user.canAuthenticate ? 'success' : 'danger'}">${user.canAuthenticate ? 'YES' : 'NO'}</span></td>
                        <td><code>${user.expectedPassword}</code></td>
                    </tr>
                `;
            });

            tableHTML += `
                    </tbody>
                </table>
            `;

            tableDiv.innerHTML = tableHTML;
        }

        // Auto-check status on page load
        window.onload = function() {
            addLog('Authentication Fix Tool loaded');
            checkStatus();
        };
    </script>
</body>
</html>
