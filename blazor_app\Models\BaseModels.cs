using System.ComponentModel.DataAnnotations;

namespace EmployeeRatingSystem.Blazor.Models
{
    /// <summary>
    /// Abstract base model that provides self-updating 'CreatedAt' and 'UpdatedAt' fields.
    /// Equivalent to Django's TimeStampedModel.
    /// </summary>
    public abstract class TimeStampedModel
    {
        [Display(Name = "Created At")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Display(Name = "Updated At")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Abstract base model that provides soft delete functionality.
    /// Equivalent to Django's SoftDeleteModel.
    /// </summary>
    public abstract class SoftDeleteModel
    {
        [Display(Name = "Is Deleted")]
        public bool IsDeleted { get; set; } = false;

        [Display(Name = "Deleted At")]
        public DateTime? DeletedAt { get; set; }

        /// <summary>
        /// Soft delete the object.
        /// </summary>
        public void SoftDelete()
        {
            IsDeleted = true;
            DeletedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Restore a soft deleted object.
        /// </summary>
        public void Restore()
        {
            IsDeleted = false;
            DeletedAt = null;
        }
    }

    /// <summary>
    /// Base model that combines timestamp and soft delete functionality.
    /// Equivalent to Django's BaseModel.
    /// </summary>
    public abstract class BaseModel : TimeStampedModel
    {
        [Key]
        public int Id { get; set; }

        [Display(Name = "Is Deleted")]
        public bool IsDeleted { get; set; } = false;

        [Display(Name = "Deleted At")]
        public DateTime? DeletedAt { get; set; }

        /// <summary>
        /// Soft delete the object.
        /// </summary>
        public void SoftDelete()
        {
            IsDeleted = true;
            DeletedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Restore a soft deleted object.
        /// </summary>
        public void Restore()
        {
            IsDeleted = false;
            DeletedAt = null;
        }
    }

    /// <summary>
    /// User roles enum with 5 distinct roles as specified in requirements.
    /// </summary>
    public enum UserRole
    {
        [Display(Name = "Super Admin (سوبر أدمين)")]
        SUPER_ADMIN,

        [Display(Name = "Manager (المدير)")]
        MANAGER,

        [Display(Name = "Direct Supervisor (المسؤول المباشر)")]
        SUPERVISOR,

        [Display(Name = "Excellence Team (فريق التميز)")]
        EXCELLENCE_TEAM,

        [Display(Name = "Employee (موظف)")]
        EMPLOYEE
    }

    /// <summary>
    /// Evaluation status enum matching Django's EVALUATION_STATUS.
    /// </summary>
    public enum EvaluationStatus
    {
        [Display(Name = "Draft")]
        DRAFT,

        [Display(Name = "Submitted")]
        SUBMITTED,

        [Display(Name = "Approved")]
        APPROVED,

        [Display(Name = "Rejected")]
        REJECTED
    }
}
