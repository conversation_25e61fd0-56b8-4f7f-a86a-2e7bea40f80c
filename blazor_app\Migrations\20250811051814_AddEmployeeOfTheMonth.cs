﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EmployeeRatingSystem.Blazor.Migrations
{
    /// <inheritdoc />
    public partial class AddEmployeeOfTheMonth : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "EmployeeOfTheMonth",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    EmployeeId = table.Column<string>(type: "TEXT", maxLength: 450, nullable: false),
                    DepartmentId = table.Column<int>(type: "INTEGER", nullable: false),
                    MonthYear = table.Column<string>(type: "TEXT", maxLength: 7, nullable: false),
                    AttendancePercentage = table.Column<decimal>(type: "TEXT", nullable: false),
                    WorkVolume = table.Column<decimal>(type: "TEXT", nullable: false),
                    SupervisorScore = table.Column<decimal>(type: "TEXT", nullable: false),
                    TotalScore = table.Column<decimal>(type: "TEXT", nullable: false),
                    JustificationEn = table.Column<string>(type: "TEXT", maxLength: 2000, nullable: true),
                    JustificationAr = table.Column<string>(type: "TEXT", maxLength: 2000, nullable: true),
                    SelectedBy = table.Column<string>(type: "TEXT", maxLength: 450, nullable: false),
                    SelectedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    IsDeleted = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EmployeeOfTheMonth", x => x.Id);
                    table.ForeignKey(
                        name: "FK_EmployeeOfTheMonth_AspNetUsers_EmployeeId",
                        column: x => x.EmployeeId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_EmployeeOfTheMonth_AspNetUsers_SelectedBy",
                        column: x => x.SelectedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_EmployeeOfTheMonth_Departments_DepartmentId",
                        column: x => x.DepartmentId,
                        principalTable: "Departments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "EmployeeOfTheMonthAttachments",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    EmployeeOfTheMonthId = table.Column<int>(type: "INTEGER", nullable: false),
                    FileName = table.Column<string>(type: "TEXT", maxLength: 255, nullable: false),
                    StoredFileName = table.Column<string>(type: "TEXT", maxLength: 255, nullable: false),
                    FilePath = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    FileSize = table.Column<long>(type: "INTEGER", nullable: false),
                    ContentType = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    DescriptionEn = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    DescriptionAr = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    UploadedBy = table.Column<string>(type: "TEXT", maxLength: 450, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    IsDeleted = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EmployeeOfTheMonthAttachments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_EmployeeOfTheMonthAttachments_AspNetUsers_UploadedBy",
                        column: x => x.UploadedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_EmployeeOfTheMonthAttachments_EmployeeOfTheMonth_EmployeeOfTheMonthId",
                        column: x => x.EmployeeOfTheMonthId,
                        principalTable: "EmployeeOfTheMonth",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_EmployeeOfTheMonth_DepartmentId",
                table: "EmployeeOfTheMonth",
                column: "DepartmentId");

            migrationBuilder.CreateIndex(
                name: "IX_EmployeeOfTheMonth_EmployeeId",
                table: "EmployeeOfTheMonth",
                column: "EmployeeId");

            migrationBuilder.CreateIndex(
                name: "IX_EmployeeOfTheMonth_SelectedBy",
                table: "EmployeeOfTheMonth",
                column: "SelectedBy");

            migrationBuilder.CreateIndex(
                name: "IX_EmployeeOfTheMonthAttachments_EmployeeOfTheMonthId",
                table: "EmployeeOfTheMonthAttachments",
                column: "EmployeeOfTheMonthId");

            migrationBuilder.CreateIndex(
                name: "IX_EmployeeOfTheMonthAttachments_UploadedBy",
                table: "EmployeeOfTheMonthAttachments",
                column: "UploadedBy");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "EmployeeOfTheMonthAttachments");

            migrationBuilder.DropTable(
                name: "EmployeeOfTheMonth");
        }
    }
}
