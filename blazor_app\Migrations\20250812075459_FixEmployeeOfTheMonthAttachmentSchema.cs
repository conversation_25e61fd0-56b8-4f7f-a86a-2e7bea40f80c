﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EmployeeRatingSystem.Blazor.Migrations
{
    /// <inheritdoc />
    public partial class FixEmployeeOfTheMonthAttachmentSchema : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_EmployeeOfTheMonth_EmployeeId",
                table: "EmployeeOfTheMonth");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "EmployeeOfTheMonthAttachments");

            migrationBuilder.DropColumn(
                name: "StoredFileName",
                table: "EmployeeOfTheMonthAttachments");

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedAt",
                table: "EmployeeOfTheMonthAttachments",
                type: "TEXT",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AlterColumn<decimal>(
                name: "WorkVolume",
                table: "EmployeeOfTheMonth",
                type: "decimal(10,2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "TEXT");

            migrationBuilder.AlterColumn<decimal>(
                name: "TotalScore",
                table: "EmployeeOfTheMonth",
                type: "decimal(5,4)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "TEXT");

            migrationBuilder.AlterColumn<decimal>(
                name: "SupervisorScore",
                table: "EmployeeOfTheMonth",
                type: "decimal(5,4)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "TEXT");

            migrationBuilder.AlterColumn<decimal>(
                name: "AttendancePercentage",
                table: "EmployeeOfTheMonth",
                type: "decimal(5,4)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "TEXT");

            migrationBuilder.CreateIndex(
                name: "IX_EmployeeOfTheMonth_EmployeeId_DepartmentId_MonthYear",
                table: "EmployeeOfTheMonth",
                columns: new[] { "EmployeeId", "DepartmentId", "MonthYear" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_EmployeeOfTheMonth_EmployeeId_DepartmentId_MonthYear",
                table: "EmployeeOfTheMonth");

            migrationBuilder.DropColumn(
                name: "UpdatedAt",
                table: "EmployeeOfTheMonthAttachments");

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "EmployeeOfTheMonthAttachments",
                type: "INTEGER",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "StoredFileName",
                table: "EmployeeOfTheMonthAttachments",
                type: "TEXT",
                maxLength: 255,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AlterColumn<decimal>(
                name: "WorkVolume",
                table: "EmployeeOfTheMonth",
                type: "TEXT",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(10,2)");

            migrationBuilder.AlterColumn<decimal>(
                name: "TotalScore",
                table: "EmployeeOfTheMonth",
                type: "TEXT",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(5,4)");

            migrationBuilder.AlterColumn<decimal>(
                name: "SupervisorScore",
                table: "EmployeeOfTheMonth",
                type: "TEXT",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(5,4)");

            migrationBuilder.AlterColumn<decimal>(
                name: "AttendancePercentage",
                table: "EmployeeOfTheMonth",
                type: "TEXT",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(5,4)");

            migrationBuilder.CreateIndex(
                name: "IX_EmployeeOfTheMonth_EmployeeId",
                table: "EmployeeOfTheMonth",
                column: "EmployeeId");
        }
    }
}
