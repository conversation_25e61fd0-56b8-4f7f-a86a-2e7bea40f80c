@page "/debug/fix-users"
@using Microsoft.AspNetCore.Identity
@using Microsoft.EntityFrameworkCore
@using EmployeeRatingSystem.Blazor.Models
@using EmployeeRatingSystem.Blazor.Data
@using EmployeeRatingSystem.Blazor.Components.Shared

@inherits LocalizedComponentBase
@inject ApplicationDbContext DbContext
@inject UserManager<ApplicationUser> UserManager

<PageTitle>Fix Users</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-tools me-2"></i>
                    Fix All Users
                </h1>
                <button class="btn btn-success btn-lg" @onclick="FixAllUsers" disabled="@isProcessing">
                    @if (isProcessing)
                    {
                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        <span>Processing...</span>
                    }
                    else
                    {
                        <i class="fas fa-magic me-2"></i>
                        <span>Fix All Users Now</span>
                    }
                </button>
            </div>

            @if (!string.IsNullOrEmpty(message))
            {
                <div class="alert alert-@(isError ? "danger" : "success") alert-dismissible fade show">
                    <i class="fas fa-@(isError ? "exclamation-triangle" : "check-circle") me-2"></i>
                    @message
                    <button type="button" class="btn-close" @onclick="ClearMessage"></button>
                </div>
            }

            @if (logs.Any())
            {
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>
                            Process Log
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="bg-dark text-light p-3 rounded" style="max-height: 400px; overflow-y: auto;">
                            @foreach (var log in logs)
                            {
                                <div class="mb-1">
                                    <small class="text-muted">[@DateTime.Now.ToString("HH:mm:ss")]</small>
                                    <span>@log</span>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            }

            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        What This Tool Does
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-user-check text-success me-2"></i>Activates Users</h6>
                            <p class="text-muted">Sets IsActive = true for all users</p>
                            
                            <h6><i class="fas fa-trash-restore text-info me-2"></i>Restores Deleted Users</h6>
                            <p class="text-muted">Sets IsDeleted = false for all users</p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-unlock text-warning me-2"></i>Unlocks Accounts</h6>
                            <p class="text-muted">Removes lockout restrictions</p>
                            
                            <h6><i class="fas fa-key text-primary me-2"></i>Resets Passwords</h6>
                            <p class="text-muted">Sets all passwords to "Password123!"</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private List<string> logs = new();
    private string message = string.Empty;
    private bool isError = false;
    private bool isProcessing = false;

    private async Task FixAllUsers()
    {
        isProcessing = true;
        logs.Clear();
        message = string.Empty;
        StateHasChanged();

        try
        {
            AddLog("Starting user fix process...");

            // Get all users
            var users = await UserManager.Users.ToListAsync();
            AddLog($"Found {users.Count} users");

            var fixedCount = 0;

            foreach (var user in users)
            {
                AddLog($"Processing user: {user.EmployeeId} - {user.EnglishName}");
                
                bool needsUpdate = false;

                // Check current status
                AddLog($"  Current status - IsActive: {user.IsActive}, IsDeleted: {user.IsDeleted}, Lockout: {user.LockoutEnd}, Failed: {user.AccessFailedCount}");

                // Activate user if inactive
                if (!user.IsActive)
                {
                    user.IsActive = true;
                    needsUpdate = true;
                    AddLog($"  ✓ Activating user");
                }

                // Undelete user if deleted
                if (user.IsDeleted)
                {
                    user.IsDeleted = false;
                    needsUpdate = true;
                    AddLog($"  ✓ Undeleting user");
                }

                // Reset lockout
                if (user.LockoutEnd.HasValue && user.LockoutEnd > DateTimeOffset.UtcNow)
                {
                    await UserManager.SetLockoutEndDateAsync(user, null);
                    AddLog($"  ✓ Removing lockout");
                }

                // Reset failed attempts
                if (user.AccessFailedCount > 0)
                {
                    await UserManager.ResetAccessFailedCountAsync(user);
                    AddLog($"  ✓ Resetting failed attempts");
                }

                // Reset password to default
                var token = await UserManager.GeneratePasswordResetTokenAsync(user);
                var passwordResult = await UserManager.ResetPasswordAsync(user, token, "Password123!");
                
                if (passwordResult.Succeeded)
                {
                    AddLog($"  ✓ Password reset to 'Password123!'");
                }
                else
                {
                    AddLog($"  ✗ Password reset failed: {string.Join(", ", passwordResult.Errors.Select(e => e.Description))}");
                }

                // Update user if needed
                if (needsUpdate)
                {
                    var updateResult = await UserManager.UpdateAsync(user);
                    if (updateResult.Succeeded)
                    {
                        AddLog($"  ✓ User updated successfully");
                        fixedCount++;
                    }
                    else
                    {
                        AddLog($"  ✗ User update failed: {string.Join(", ", updateResult.Errors.Select(e => e.Description))}");
                    }
                }
                else
                {
                    fixedCount++;
                    AddLog($"  ✓ User was already in good state");
                }

                AddLog($"  → User {user.EmployeeId} processing completed");
                StateHasChanged();
                await Task.Delay(100); // Small delay to show progress
            }

            AddLog("User fix process completed!");
            ShowMessage($"Successfully processed {fixedCount} users. All users should now be able to login with password 'Password123!'", false);
        }
        catch (Exception ex)
        {
            AddLog($"ERROR: {ex.Message}");
            ShowMessage($"Error during fix process: {ex.Message}", true);
        }
        finally
        {
            isProcessing = false;
            StateHasChanged();
        }
    }

    private void AddLog(string logMessage)
    {
        logs.Add(logMessage);
        StateHasChanged();
    }

    private void ShowMessage(string msg, bool error)
    {
        message = msg;
        isError = error;
        StateHasChanged();
    }

    private void ClearMessage()
    {
        message = string.Empty;
        StateHasChanged();
    }
}
