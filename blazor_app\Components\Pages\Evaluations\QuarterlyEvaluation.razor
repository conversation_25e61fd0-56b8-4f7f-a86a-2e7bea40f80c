@page "/evaluations/quarterly"
@rendermode InteractiveServer
@using EmployeeRatingSystem.Blazor.Components.Shared
@using EmployeeRatingSystem.Blazor.Services
@using EmployeeRatingSystem.Blazor.Models
@using EmployeeRatingSystem.Blazor.Data
@using Microsoft.EntityFrameworkCore
@using Microsoft.AspNetCore.Components.Authorization
@using System.Globalization
@inherits LocalizedComponentBase
@inject ApplicationDbContext DbContext
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IEmployeeManagementService EmployeeManagementService

<PageTitle>@L("Quarterly Employee Evaluation", "التقييم الربع السنوي للموظفين")</PageTitle>

<!-- Page Header -->
<div class="page-header @GetLayoutClass()">
    <nav aria-label="@L("Breadcrumb", "مسار التنقل")">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="/" class="text-decoration-none">@L("Home", "الرئيسية")</a>
            </li>
            <li class="breadcrumb-item">
                <a href="/evaluations" class="text-decoration-none">@L("Evaluations", "التقييمات")</a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                @L("Quarterly Evaluation", "التقييم الربع السنوي")
            </li>
        </ol>
    </nav>

    <div class="d-flex justify-content-between align-items-start flex-wrap">
        <div class="flex-grow-1">
            <h1 class="h1 mb-2">
                <i class="fas fa-calendar-alt @GetMarginEnd() text-primary"></i>
                @L("Quarterly Employee Evaluation", "التقييم الربع السنوي للموظفين")
            </h1>
            <p class="lead mb-0">@L("Quarterly evaluation system with aggregated monthly scores", "نظام التقييم الربع السنوي مع تجميع النتائج الشهرية")</p>
        </div>
    </div>
</div>

<!-- Period Selection -->
<div class="card shadow-sm mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0">
            <i class="fas fa-calendar @GetMarginEnd(1)"></i>
            @L("Evaluation Period Selection", "اختيار فترة التقييم")
        </h5>
    </div>
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-3">
                <label class="form-label">@L("Year", "السنة")</label>
                <select class="form-select" @bind="selectedYear">
                    <option value="">@L("Select Year", "اختر السنة")</option>
                    @for (int year = DateTime.Now.Year; year >= DateTime.Now.Year - 5; year--)
                    {
                        <option value="@year">@year</option>
                    }
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">@L("Period", "الفترة")</label>
                <select class="form-select" @bind="selectedPeriod">
                    <option value="">@L("Select Period", "اختر الفترة")</option>
                    <option value="Q1">@L("Q1 (Jan-Mar)", "الربع الأول (يناير-مارس)")</option>
                    <option value="Q2">@L("Q2 (Apr-Jun)", "الربع الثاني (أبريل-يونيو)")</option>
                    <option value="H1">@L("H1 (Jan-Jun)", "نصف السنة الأول (يناير-يونيو)")</option>
                    <option value="Q3">@L("Q3 (Jul-Sep)", "الربع الثالث (يوليو-سبتمبر)")</option>
                    <option value="Q4">@L("Q4 (Oct-Dec)", "الربع الرابع (أكتوبر-ديسمبر)")</option>
                    <option value="H2">@L("H2 (Jul-Dec)", "نصف السنة الثاني (يوليو-ديسمبر)")</option>
                    <option value="ANNUAL">@L("Annual (Jan-Dec)", "السنوي (يناير-ديسمبر)")</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">@L("Department", "القسم")</label>
                @if (currentUser?.Role == UserRole.SUPERVISOR)
                {
                    <!-- Auto-selected department for supervisors (read-only) -->
                    <div class="input-group">
                        <select class="form-select" @bind="selectedDepartmentId" disabled>
                            @foreach (var dept in departments)
                            {
                                <option value="@dept.Id" selected="@(dept.Id.ToString() == selectedDepartmentId)">
                                    @GetDepartmentName(dept)
                                </option>
                            }
                        </select>
                        <span class="input-group-text" title="@L("Auto-selected for your role", "محدد تلقائياً لدورك")">
                            <i class="fas fa-lock text-muted"></i>
                        </span>
                    </div>
                    <small class="text-muted">@L("Your department is automatically selected", "تم تحديد قسمك تلقائياً")</small>
                }
                else
                {
                    <!-- Normal department selection for other roles -->
                    <select class="form-select" @bind="selectedDepartmentId">
                        <option value="">@L("All Departments", "جميع الأقسام")</option>
                        @foreach (var dept in departments)
                        {
                            <option value="@dept.Id">@GetDepartmentName(dept)</option>
                        }
                    </select>
                }
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button class="btn btn-primary w-100" @onclick="LoadQuarterlyEvaluations" disabled="@(string.IsNullOrEmpty(selectedYear) || string.IsNullOrEmpty(selectedPeriod))">
                    <i class="fas fa-search @GetMarginEnd(1)"></i>
                    @L("Load Evaluations", "تحميل التقييمات")
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Loading State -->
@if (isLoading)
{
    <div class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">@L("Loading...", "جاري التحميل...")</span>
        </div>
        <p class="mt-2">@L("Loading quarterly evaluations...", "جاري تحميل التقييمات الربع السنوية...")</p>
    </div>
}

<!-- Results Section -->
@if (quarterlyResults.Any() && !isLoading)
{
    <!-- Summary Cards -->
    <div class="row g-4 mb-4">
        <div class="col-md-3">
            <div class="card stat-card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="stat-icon bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-users fa-lg text-primary"></i>
                    </div>
                    <h3 class="h2 mb-1 text-primary">@quarterlyResults.Count</h3>
                    <p class="text-muted mb-0">@L("Total Employees", "إجمالي الموظفين")</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="stat-icon bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-chart-line fa-lg text-success"></i>
                    </div>
                    <h3 class="h2 mb-1 text-success">@GetAverageScore().ToString("F1")%</h3>
                    <p class="text-muted mb-0">@L("Average Score", "متوسط النتيجة")</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="stat-icon bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-trophy fa-lg text-warning"></i>
                    </div>
                    <h3 class="h2 mb-1 text-warning">@GetTopScore().ToString("F1")%</h3>
                    <p class="text-muted mb-0">@L("Highest Score", "أعلى نتيجة")</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="stat-icon bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-calendar-check fa-lg text-info"></i>
                    </div>
                    <h3 class="h2 mb-1 text-info">@GetPeriodDisplayName()</h3>
                    <p class="text-muted mb-0">@L("Period", "الفترة")</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quarterly Results Table -->
    <div class="card shadow-sm">
        <div class="card-header bg-light">
            <h5 class="card-title mb-0">
                <i class="fas fa-table @GetMarginEnd(1)"></i>
                @L("Quarterly Evaluation Results", "نتائج التقييم الربع السنوي") - @GetPeriodDisplayName() @selectedYear
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>@L("Rank", "الترتيب")</th>
                            <th>@L("Employee", "الموظف")</th>
                            <th>@L("Department", "القسم")</th>
                            <th>@L("Work Volume", "حجم العمل")</th>
                            <th>@L("Attendance", "الحضور")</th>
                            <th>@L("Supervisor", "المسؤول")</th>
                            <th>@L("Total Score", "النتيجة الإجمالية")</th>
                            <th>@L("Grade", "التقدير")</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var result in quarterlyResults.OrderByDescending(r => r.TotalScore))
                        {
                            <tr>
                                <td>
                                    <span class="badge bg-primary">@(quarterlyResults.OrderByDescending(r => r.TotalScore).ToList().IndexOf(result) + 1)</span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="@GetMarginStart(2)">
                                            <div class="fw-bold">@GetEmployeeName(result.Employee)</div>
                                            <small class="text-muted">@result.Employee.EmployeeId</small>
                                        </div>
                                    </div>
                                </td>
                                <td>@GetDepartmentName(result.Employee.PrimaryDepartment)</td>
                                <td>
                                    <span class="badge bg-info">@result.WorkVolumeScore.ToString("F1")%</span>
                                </td>
                                <td>
                                    <span class="badge bg-success">@result.AttendanceScore.ToString("F1")%</span>
                                </td>
                                <td>
                                    <span class="badge bg-warning">@result.SupervisorScore.ToString("F1")%</span>
                                </td>
                                <td>
                                    <span class="badge bg-primary fs-6">@result.TotalScore.ToString("F1")%</span>
                                </td>
                                <td>
                                    <span class="badge @GetGradeBadgeClass(result.TotalScore)">@GetGradeText(result.TotalScore)</span>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
}
else if (!isLoading && !string.IsNullOrEmpty(selectedYear) && !string.IsNullOrEmpty(selectedPeriod))
{
    <div class="text-center py-5">
        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">@L("No evaluations found", "لم يتم العثور على تقييمات")</h5>
        <p class="text-muted">@L("No evaluation data available for the selected period", "لا توجد بيانات تقييم متاحة للفترة المحددة")</p>
    </div>
}

@code {
    // Properties
    private string selectedYear = DateTime.Now.Year.ToString();
    private string selectedPeriod = "";
    private string selectedDepartmentId = "";
    private bool isLoading = false;

    // Data collections
    private List<Department> departments = new();
    private List<QuarterlyEvaluationResult> quarterlyResults = new();

    // User context
    private ApplicationUser? currentUser;
    private bool isAuthenticated = false;

    protected override async Task OnInitializedAsync()
    {
        // Set default values
        selectedYear = DateTime.Now.Year.ToString();
        
        await LoadCurrentUser();
        await LoadDepartments();
    }

    private async Task LoadCurrentUser()
    {
        try
        {
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            isAuthenticated = authState.User.Identity?.IsAuthenticated ?? false;

            if (isAuthenticated)
            {
                var userEmail = authState.User.Identity?.Name;
                if (!string.IsNullOrEmpty(userEmail))
                {
                    currentUser = await DbContext.Users
                        .Include(u => u.PrimaryDepartment)
                        .FirstOrDefaultAsync(u => u.Email == userEmail);
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading current user: {ex.Message}");
        }
    }

    private async Task LoadDepartments()
    {
        try
        {
            if (currentUser != null)
            {
                // Use EmployeeManagementService to get accessible departments based on role
                var accessibleDepartments = await EmployeeManagementService.GetAccessibleDepartmentsAsync(currentUser.Id);
                departments = accessibleDepartments
                    .OrderBy(d => d.NameEn)
                    .ToList();

                Console.WriteLine($"Loaded {departments.Count} accessible departments for user role: {currentUser.Role}");

                // Auto-select department for supervisors (they only have access to one department)
                if (currentUser.Role == UserRole.SUPERVISOR && departments.Count == 1)
                {
                    selectedDepartmentId = departments.First().Id.ToString();
                    Console.WriteLine($"Auto-selected department for supervisor: {selectedDepartmentId} ({departments.First().NameEn})");
                }
            }
            else
            {
                Console.WriteLine("Current user is null, cannot load departments");
                departments = new List<Department>();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading departments: {ex.Message}");
            departments = new List<Department>();
        }
    }

    private async Task LoadQuarterlyEvaluations()
    {
        if (string.IsNullOrEmpty(selectedYear) || string.IsNullOrEmpty(selectedPeriod))
            return;

        isLoading = true;
        quarterlyResults.Clear();
        StateHasChanged();

        try
        {
            var months = GetMonthsForPeriod(selectedPeriod);
            var evaluationPeriods = months.Select(month => $"{selectedYear}-{month:D2}").ToList();

            // Load comprehensive evaluations for the specified periods
            var query = DbContext.ComprehensiveEvaluations
                .Include(e => e.Employee)
                .ThenInclude(e => e.PrimaryDepartment)
                .Where(e => evaluationPeriods.Contains(e.EvaluationPeriod));

            // Apply department filter if specified
            if (!string.IsNullOrEmpty(selectedDepartmentId) && int.TryParse(selectedDepartmentId, out int deptId))
            {
                query = query.Where(e => e.DepartmentId == deptId);
            }

            var evaluations = await query.ToListAsync();

            // Group by employee and calculate quarterly averages
            var groupedEvaluations = evaluations
                .GroupBy(e => e.EmployeeId)
                .Select(g => new QuarterlyEvaluationResult
                {
                    Employee = g.First().Employee,
                    WorkVolumeScore = g.Average(e => e.WorkVolumePercentage),
                    AttendanceScore = g.Average(e => e.AttendancePercentage),
                    SupervisorScore = g.Average(e => e.SupervisorScore * 100),
                    TotalScore = g.Average(e => e.TotalScore * 100),
                    EvaluationCount = g.Count(),
                    Period = selectedPeriod,
                    Year = int.Parse(selectedYear)
                })
                .ToList();

            quarterlyResults = groupedEvaluations;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading quarterly evaluations: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private List<int> GetMonthsForPeriod(string period)
    {
        return period switch
        {
            "Q1" => new List<int> { 1, 2, 3 }, // Jan-Mar
            "Q2" => new List<int> { 4, 5, 6 }, // Apr-Jun
            "H1" => new List<int> { 1, 2, 3, 4, 5, 6 }, // Jan-Jun
            "Q3" => new List<int> { 7, 8, 9 }, // Jul-Sep
            "Q4" => new List<int> { 10, 11, 12 }, // Oct-Dec
            "H2" => new List<int> { 7, 8, 9, 10, 11, 12 }, // Jul-Dec
            "ANNUAL" => new List<int> { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12 }, // Full year
            _ => new List<int>()
        };
    }

    private string GetPeriodDisplayName()
    {
        return selectedPeriod switch
        {
            "Q1" => L("Q1 (Jan-Mar)", "الربع الأول (يناير-مارس)"),
            "Q2" => L("Q2 (Apr-Jun)", "الربع الثاني (أبريل-يونيو)"),
            "H1" => L("H1 (Jan-Jun)", "نصف السنة الأول (يناير-يونيو)"),
            "Q3" => L("Q3 (Jul-Sep)", "الربع الثالث (يوليو-سبتمبر)"),
            "Q4" => L("Q4 (Oct-Dec)", "الربع الرابع (أكتوبر-ديسمبر)"),
            "H2" => L("H2 (Jul-Dec)", "نصف السنة الثاني (يوليو-ديسمبر)"),
            "ANNUAL" => L("Annual (Jan-Dec)", "السنوي (يناير-ديسمبر)"),
            _ => selectedPeriod
        };
    }

    private decimal GetAverageScore()
    {
        return quarterlyResults.Any() ? quarterlyResults.Average(r => r.TotalScore) : 0;
    }

    private decimal GetTopScore()
    {
        return quarterlyResults.Any() ? quarterlyResults.Max(r => r.TotalScore) : 0;
    }

    private string GetEmployeeName(ApplicationUser employee)
    {
        var isRtl = CultureInfo.CurrentCulture.Name.StartsWith("ar");
        return isRtl ?
            (!string.IsNullOrEmpty(employee.ArabicName) ? employee.ArabicName : employee.EnglishName) :
            (!string.IsNullOrEmpty(employee.EnglishName) ? employee.EnglishName : employee.ArabicName);
    }

    private string GetDepartmentName(Department? department)
    {
        if (department == null) return L("Unknown", "غير معروف");

        var isRtl = CultureInfo.CurrentCulture.Name.StartsWith("ar");
        return isRtl ?
            (!string.IsNullOrEmpty(department.NameAr) ? department.NameAr : department.NameEn) :
            (!string.IsNullOrEmpty(department.NameEn) ? department.NameEn : department.NameAr);
    }

    private string GetGradeBadgeClass(decimal score)
    {
        return score switch
        {
            >= 90 => "bg-success",
            >= 80 => "bg-primary",
            >= 70 => "bg-warning",
            >= 60 => "bg-info",
            _ => "bg-danger"
        };
    }

    private string GetGradeText(decimal score)
    {
        return score switch
        {
            >= 90 => L("Excellent", "ممتاز"),
            >= 80 => L("Very Good", "جيد جداً"),
            >= 70 => L("Good", "جيد"),
            >= 60 => L("Satisfactory", "مقبول"),
            _ => L("Needs Improvement", "يحتاج تحسين")
        };
    }

    // Helper class for quarterly evaluation results
    public class QuarterlyEvaluationResult
    {
        public ApplicationUser Employee { get; set; } = null!;
        public decimal WorkVolumeScore { get; set; }
        public decimal AttendanceScore { get; set; }
        public decimal SupervisorScore { get; set; }
        public decimal TotalScore { get; set; }
        public int EvaluationCount { get; set; }
        public string Period { get; set; } = "";
        public int Year { get; set; }
    }
}
