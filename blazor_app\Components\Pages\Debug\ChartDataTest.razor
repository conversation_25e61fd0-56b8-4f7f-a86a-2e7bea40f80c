@page "/debug/chart-data-test"
@using EmployeeRatingSystem.Blazor.Models
@using EmployeeRatingSystem.Blazor.Data
@using Microsoft.EntityFrameworkCore
@using EmployeeRatingSystem.Blazor.Components.Shared.Charts
@inject ApplicationDbContext DbContext
@inherits LocalizedComponentBase

<PageTitle>Chart Data Test</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3>Chart Data Test</h3>
                    <p>Testing chart data initialization for Reports page</p>
                </div>
                <div class="card-body">
                    
                    <!-- Test Data Display -->
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Work Volume Data</h5>
                            <p><strong>Labels Count:</strong> @(workVolumeLabels?.Length ?? 0)</p>
                            <p><strong>Scores Count:</strong> @(workVolumeScores?.Length ?? 0)</p>
                            @if (workVolumeLabels?.Length > 0)
                            {
                                <p><strong>Labels:</strong></p>
                                <ul>
                                    @foreach (var label in workVolumeLabels)
                                    {
                                        <li>@label</li>
                                    }
                                </ul>
                            }
                            @if (workVolumeScores?.Length > 0)
                            {
                                <p><strong>Scores:</strong> [@string.Join(", ", workVolumeScores.Select(s => s.ToString("F1")))]</p>
                            }
                        </div>
                        
                        <div class="col-md-6">
                            <h5>Supervisor Data</h5>
                            <p><strong>Labels Count:</strong> @(supervisorLabels?.Length ?? 0)</p>
                            <p><strong>Scores Count:</strong> @(supervisorScores?.Length ?? 0)</p>
                            @if (supervisorLabels?.Length > 0)
                            {
                                <p><strong>Labels:</strong></p>
                                <ul>
                                    @foreach (var label in supervisorLabels)
                                    {
                                        <li>@label</li>
                                    }
                                </ul>
                            }
                            @if (supervisorScores?.Length > 0)
                            {
                                <p><strong>Scores:</strong> [@string.Join(", ", supervisorScores.Select(s => s.ToString("F1")))]</p>
                            }
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h5>Attendance Data</h5>
                            <p><strong>Labels Count:</strong> @(attendanceLabels?.Length ?? 0)</p>
                            <p><strong>Days Count:</strong> @(attendanceDays?.Length ?? 0)</p>
                            @if (attendanceDays?.Length > 0)
                            {
                                <p><strong>Days:</strong> [@string.Join(", ", attendanceDays.Select(d => d.ToString("F0")))]</p>
                            }
                        </div>
                        
                        <div class="col-md-6">
                            <h5>Total Score Data</h5>
                            <p><strong>Labels Count:</strong> @(totalScoreLabels?.Length ?? 0)</p>
                            <p><strong>Percentages Count:</strong> @(totalScorePercentages?.Length ?? 0)</p>
                            @if (totalScorePercentages?.Length > 0)
                            {
                                <p><strong>Percentages:</strong> [@string.Join(", ", totalScorePercentages.Select(p => p.ToString("F1")))]</p>
                            }
                        </div>
                    </div>
                    
                    <!-- Test Chart -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>Test Chart</h5>
                            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                                <BarChart CanvasId="testChart"
                                          Title="Test Chart with Arabic Names"
                                          Labels="@supervisorLabels"
                                          Data="@supervisorScores"
                                          Color="#065f46"
                                          IsLoading="false"
                                          Width="600"
                                          Height="300" />
                            </div>
                        </div>
                    </div>
                    
                    <!-- Refresh Button -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <button class="btn btn-primary" @onclick="RefreshData">Refresh Data</button>
                            <button class="btn btn-secondary" @onclick="InitializeSampleData">Initialize Sample Data</button>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    // Chart data arrays - same as Reports page
    private string[]? workVolumeLabels = new string[] { "أحمد محمد علي", "فاطمة عبدالله حسن", "محمد أحمد السيد", "نورا خالد محمود", "عمر يوسف إبراهيم" };
    private double[]? workVolumeScores = new double[] { 92.5, 88.3, 95.1, 87.9, 91.7 };
    private string[]? attendanceLabels = new string[] { "أحمد محمد علي", "فاطمة عبدالله حسن", "محمد أحمد السيد", "نورا خالد محمود", "عمر يوسف إبراهيم" };
    private double[]? attendanceDays = new double[] { 22, 20, 23, 21, 22 };
    private string[]? supervisorLabels = new string[] { "أحمد محمد علي", "فاطمة عبدالله حسن", "محمد أحمد السيد", "نورا خالد محمود", "عمر يوسف إبراهيم" };
    private double[]? supervisorScores = new double[] { 44.5, 46.1, 42.3, 47.1, 45.4 };
    private string[]? totalScoreLabels = new string[] { "أحمد محمد علي", "فاطمة عبدالله حسن", "محمد أحمد السيد", "نورا خالد محمود", "عمر يوسف إبراهيم" };
    private double[]? totalScorePercentages = new double[] { 92.8, 89.9, 92.8, 91.1, 92.0 };

    protected override async Task OnInitializedAsync()
    {
        Console.WriteLine("ChartDataTest: OnInitializedAsync called");
        Console.WriteLine($"ChartDataTest: workVolumeLabels count: {workVolumeLabels?.Length ?? 0}");
        Console.WriteLine($"ChartDataTest: supervisorLabels count: {supervisorLabels?.Length ?? 0}");
        Console.WriteLine($"ChartDataTest: supervisorScores count: {supervisorScores?.Length ?? 0}");
        
        if (workVolumeLabels?.Length > 0)
        {
            Console.WriteLine($"ChartDataTest: Labels: [{string.Join(", ", workVolumeLabels)}]");
        }
        
        await base.OnInitializedAsync();
    }

    private async Task RefreshData()
    {
        Console.WriteLine("ChartDataTest: RefreshData called");
        StateHasChanged();
        await Task.CompletedTask;
    }

    private async Task InitializeSampleData()
    {
        Console.WriteLine("ChartDataTest: InitializeSampleData called");
        
        // Re-initialize with sample data
        workVolumeLabels = new string[] { "أحمد محمد علي", "فاطمة عبدالله حسن", "محمد أحمد السيد", "نورا خالد محمود", "عمر يوسف إبراهيم" };
        workVolumeScores = new double[] { 92.5, 88.3, 95.1, 87.9, 91.7 };
        supervisorLabels = new string[] { "أحمد محمد علي", "فاطمة عبدالله حسن", "محمد أحمد السيد", "نورا خالد محمود", "عمر يوسف إبراهيم" };
        supervisorScores = new double[] { 44.5, 46.1, 42.3, 47.1, 45.4 };
        attendanceLabels = new string[] { "أحمد محمد علي", "فاطمة عبدالله حسن", "محمد أحمد السيد", "نورا خالد محمود", "عمر يوسف إبراهيم" };
        attendanceDays = new double[] { 22, 20, 23, 21, 22 };
        totalScoreLabels = new string[] { "أحمد محمد علي", "فاطمة عبدالله حسن", "محمد أحمد السيد", "نورا خالد محمود", "عمر يوسف إبراهيم" };
        totalScorePercentages = new double[] { 92.8, 89.9, 92.8, 91.1, 92.0 };
        
        Console.WriteLine($"ChartDataTest: After initialization - supervisorLabels count: {supervisorLabels?.Length ?? 0}");
        Console.WriteLine($"ChartDataTest: After initialization - supervisorScores count: {supervisorScores?.Length ?? 0}");
        
        StateHasChanged();
        await Task.CompletedTask;
    }
}
