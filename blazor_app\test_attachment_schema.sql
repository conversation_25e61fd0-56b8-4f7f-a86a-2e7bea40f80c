-- Test script to verify EmployeeOfTheMonthAttachments table schema
-- Run this to check if the UpdatedAt column exists

-- Check table structure
PRAGMA table_info(EmployeeOfTheMonthAttachments);

-- Check if UpdatedAt column exists
SELECT sql FROM sqlite_master WHERE type='table' AND name='EmployeeOfTheMonthAttachments';

-- Test inserting a record (this will fail if schema is wrong)
-- Note: This is just for testing - don't run in production
/*
INSERT INTO EmployeeOfTheMonthAttachments (
    EmployeeOfTheMonthId,
    FileName,
    FilePath,
    FileSize,
    ContentType,
    DescriptionEn,
    DescriptionAr,
    UploadedBy,
    CreatedAt,
    UpdatedAt
) VALUES (
    1,
    'test.pdf',
    'uploads/employee-of-month/test.pdf',
    1024,
    'application/pdf',
    'Test file',
    'ملف تجريبي',
    'test-user-id',
    datetime('now'),
    datetime('now')
);
*/
