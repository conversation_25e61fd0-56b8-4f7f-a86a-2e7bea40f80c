using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using EmployeeRatingSystem.Blazor.Models;
using EmployeeRatingSystem.Blazor.Data;

namespace EmployeeRatingSystem.Blazor;

public class FixAuthenticationIssues
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ApplicationDbContext _context;
    private readonly ILogger<FixAuthenticationIssues> _logger;

    public FixAuthenticationIssues(
        UserManager<ApplicationUser> userManager,
        ApplicationDbContext context,
        ILogger<FixAuthenticationIssues> logger)
    {
        _userManager = userManager;
        _context = context;
        _logger = logger;
    }

    public async Task<bool> FixAllAuthenticationIssuesAsync()
    {
        try
        {
            _logger.LogInformation("Starting authentication fix process...");

            // Get all users
            var users = await _userManager.Users.OrderBy(u => u.EmployeeId).ToListAsync();
            _logger.LogInformation($"Found {users.Count} users in database");

            int fixedCount = 0;
            int errorCount = 0;

            foreach (var user in users)
            {
                try
                {
                    _logger.LogInformation($"Processing user {user.EmployeeId} - {user.EnglishName}");
                    
                    // Check if user has password hash
                    bool hasPassword = !string.IsNullOrEmpty(user.PasswordHash);
                    _logger.LogInformation($"  User {user.EmployeeId} has password: {hasPassword}");
                    
                    // Ensure user is active
                    if (!user.IsActive || user.IsDeleted)
                    {
                        user.IsActive = true;
                        user.IsDeleted = false;
                        await _userManager.UpdateAsync(user);
                        _logger.LogInformation($"  ✓ Activated user {user.EmployeeId}");
                    }

                    // Fix password if missing
                    if (!hasPassword)
                    {
                        var expectedPassword = GetExpectedPassword(user.EmployeeId);
                        _logger.LogInformation($"  Setting password for {user.EmployeeId} to: {expectedPassword}");
                        
                        var token = await _userManager.GeneratePasswordResetTokenAsync(user);
                        var result = await _userManager.ResetPasswordAsync(user, token, expectedPassword);
                        
                        if (result.Succeeded)
                        {
                            _logger.LogInformation($"  ✓ Successfully set password for {user.EmployeeId}");
                            fixedCount++;
                        }
                        else
                        {
                            var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                            _logger.LogError($"  ✗ Failed to set password for {user.EmployeeId}: {errors}");
                            errorCount++;
                        }
                    }

                    // Ensure user has proper role
                    var userRoles = await _userManager.GetRolesAsync(user);
                    if (!userRoles.Contains(user.Role.ToString()))
                    {
                        await _userManager.AddToRoleAsync(user, user.Role.ToString());
                        _logger.LogInformation($"  ✓ Added role {user.Role} to {user.EmployeeId}");
                    }

                    // Test password
                    var testPassword = GetExpectedPassword(user.EmployeeId);
                    var passwordCheck = await _userManager.CheckPasswordAsync(user, testPassword);
                    _logger.LogInformation($"  Password test for {user.EmployeeId}: {(passwordCheck ? "SUCCESS" : "FAILED")}");
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Error processing user {user.EmployeeId}: {ex.Message}");
                    errorCount++;
                }
            }

            _logger.LogInformation($"Authentication fix process completed: {fixedCount} users fixed, {errorCount} errors");
            
            // Final verification - test all logins
            await TestAllLoginsAsync();
            
            return errorCount == 0;
        }
        catch (Exception ex)
        {
            _logger.LogError($"Critical error during authentication fix: {ex.Message}");
            return false;
        }
    }

    private async Task TestAllLoginsAsync()
    {
        _logger.LogInformation("Testing authentication for all users...");
        
        var users = await _userManager.Users.OrderBy(u => u.EmployeeId).ToListAsync();
        int successCount = 0;
        int failCount = 0;

        foreach (var user in users)
        {
            try
            {
                var expectedPassword = GetExpectedPassword(user.EmployeeId);
                var result = await _userManager.CheckPasswordAsync(user, expectedPassword);
                
                if (result)
                {
                    _logger.LogInformation($"  ✓ {user.EmployeeId} ({user.EnglishName}): Authentication SUCCESS");
                    successCount++;
                }
                else
                {
                    _logger.LogWarning($"  ✗ {user.EmployeeId} ({user.EnglishName}): Authentication FAILED");
                    failCount++;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"  ✗ {user.EmployeeId}: Error testing authentication - {ex.Message}");
                failCount++;
            }
        }

        _logger.LogInformation($"Authentication test results: {successCount} SUCCESS, {failCount} FAILED");
        
        if (failCount == 0)
        {
            _logger.LogInformation("🎉 ALL USERS CAN NOW AUTHENTICATE SUCCESSFULLY!");
        }
        else
        {
            _logger.LogWarning($"⚠️  {failCount} users still cannot authenticate properly");
        }
    }

    private string GetExpectedPassword(string employeeId)
    {
        return employeeId switch
        {
            "EMP007" => "Fa35108981",
            _ => "Password123!"
        };
    }

    public async Task<List<UserAuthStatus>> GetUserAuthStatusAsync()
    {
        var users = await _userManager.Users.OrderBy(u => u.EmployeeId).ToListAsync();
        var statusList = new List<UserAuthStatus>();

        foreach (var user in users)
        {
            var expectedPassword = GetExpectedPassword(user.EmployeeId);
            var hasPassword = !string.IsNullOrEmpty(user.PasswordHash);
            var canAuthenticate = hasPassword && await _userManager.CheckPasswordAsync(user, expectedPassword);

            statusList.Add(new UserAuthStatus
            {
                EmployeeId = user.EmployeeId,
                Name = user.EnglishName,
                Role = user.Role.ToString(),
                IsActive = user.IsActive,
                HasPassword = hasPassword,
                ExpectedPassword = expectedPassword,
                CanAuthenticate = canAuthenticate
            });
        }

        return statusList;
    }
}

public class UserAuthStatus
{
    public string EmployeeId { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Role { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public bool HasPassword { get; set; }
    public string ExpectedPassword { get; set; } = string.Empty;
    public bool CanAuthenticate { get; set; }
}
