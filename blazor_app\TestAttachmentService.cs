using EmployeeRatingSystem.Blazor.Data;
using EmployeeRatingSystem.Blazor.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace EmployeeRatingSystem.Blazor
{
    /// <summary>
    /// Test class to verify the EmployeeOfTheMonthAttachment functionality
    /// </summary>
    public class AttachmentServiceTest
    {
        public static async Task TestAttachmentService(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<AttachmentServiceTest>>();

            try
            {
                logger.LogInformation("Testing EmployeeOfTheMonthAttachment database schema...");

                // Test 1: Check if table exists and has correct columns
                var tableExists = await context.Database.ExecuteSqlRawAsync(
                    "SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='EmployeeOfTheMonthAttachments'"
                );
                logger.LogInformation("EmployeeOfTheMonthAttachments table exists: {TableExists}", tableExists >= 0);

                // Test 2: Check if we can query the table (this will fail if UpdatedAt column is missing)
                var attachmentCount = await context.EmployeeOfTheMonthAttachments.CountAsync();
                logger.LogInformation("Current attachment count: {Count}", attachmentCount);

                // Test 3: Check if we have any Employee of the Month records to test with
                var employeeOfTheMonthCount = await context.EmployeeOfTheMonth.CountAsync();
                logger.LogInformation("Current Employee of the Month records: {Count}", employeeOfTheMonthCount);

                // Test 4: Try to create a test attachment record (without actual file)
                if (employeeOfTheMonthCount > 0)
                {
                    var firstRecord = await context.EmployeeOfTheMonth.FirstAsync();
                    logger.LogInformation("Testing with Employee of the Month ID: {Id}", firstRecord.Id);

                    // This tests if the schema is correct
                    var testAttachment = new Models.EmployeeOfTheMonthAttachment
                    {
                        EmployeeOfTheMonthId = firstRecord.Id,
                        FileName = "test-schema.txt",
                        FilePath = "uploads/employee-of-month/test-schema.txt",
                        FileSize = 100,
                        ContentType = "text/plain",
                        DescriptionEn = "Schema test file",
                        DescriptionAr = "ملف اختبار المخطط",
                        UploadedBy = firstRecord.SelectedBy,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };

                    // Don't actually save - just test if the model is valid
                    context.EmployeeOfTheMonthAttachments.Add(testAttachment);
                    
                    // Remove it without saving to database
                    context.EmployeeOfTheMonthAttachments.Remove(testAttachment);
                    
                    logger.LogInformation("Schema test passed - UpdatedAt column exists and is accessible");
                }

                logger.LogInformation("All attachment service tests passed successfully!");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Attachment service test failed: {Message}", ex.Message);
                throw;
            }
        }
    }
}
