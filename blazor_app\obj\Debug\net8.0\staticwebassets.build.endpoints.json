{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "EmployeeRatingSystem.Blazor.m7u9u9ow3j.styles.css", "AssetFile": "EmployeeRatingSystem.Blazor.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "873"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"MiMYVKzOXD4Og2txVQaud7iSs/JzNO4xm/BL2PzWX0o=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 12 Aug 2025 05:43:01 GMT"}, {"Name": "Link", "Value": "<_content/Microsoft.AspNetCore.Components.QuickGrid/Microsoft.AspNetCore.Components.QuickGrid.25o87uqmvr.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m7u9u9ow3j"}, {"Name": "integrity", "Value": "sha256-MiMYVKzOXD4Og2txVQaud7iSs/JzNO4xm/BL2PzWX0o="}, {"Name": "label", "Value": "EmployeeRatingSystem.Blazor.styles.css"}]}, {"Route": "EmployeeRatingSystem.Blazor.styles.css", "AssetFile": "EmployeeRatingSystem.Blazor.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "873"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"MiMYVKzOXD4Og2txVQaud7iSs/JzNO4xm/BL2PzWX0o=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 12 Aug 2025 05:43:01 GMT"}, {"Name": "Link", "Value": "<_content/Microsoft.AspNetCore.Components.QuickGrid/Microsoft.AspNetCore.Components.QuickGrid.25o87uqmvr.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MiMYVKzOXD4Og2txVQaud7iSs/JzNO4xm/BL2PzWX0o="}]}, {"Route": "_content/Microsoft.AspNetCore.Components.QuickGrid/Microsoft.AspNetCore.Components.QuickGrid.25o87uqmvr.bundle.scp.css", "AssetFile": "_content/Microsoft.AspNetCore.Components.QuickGrid/Microsoft.AspNetCore.Components.QuickGrid.25o87uqmvr.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8251"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Nuu0BHvGBlwN6c5FSVjV9yxX0YPfwg3ufdOucRJCOz0=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 16:22:14 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "25o87uqmvr"}, {"Name": "integrity", "Value": "sha256-Nuu0BHvGBlwN6c5FSVjV9yxX0YPfwg3ufdOucRJCOz0="}, {"Name": "label", "Value": "_content/Microsoft.AspNetCore.Components.QuickGrid/Microsoft.AspNetCore.Components.QuickGrid.bundle.scp.css"}]}, {"Route": "_content/Microsoft.AspNetCore.Components.QuickGrid/Microsoft.AspNetCore.Components.QuickGrid.bundle.scp.css", "AssetFile": "_content/Microsoft.AspNetCore.Components.QuickGrid/Microsoft.AspNetCore.Components.QuickGrid.25o87uqmvr.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8251"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Nuu0BHvGBlwN6c5FSVjV9yxX0YPfwg3ufdOucRJCOz0=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 16:22:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nuu0BHvGBlwN6c5FSVjV9yxX0YPfwg3ufdOucRJCOz0="}]}, {"Route": "_content/Microsoft.AspNetCore.Components.QuickGrid/QuickGrid.eu7w3d5g1a.razor.js", "AssetFile": "_content/Microsoft.AspNetCore.Components.QuickGrid/QuickGrid.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2563"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tnamWuQ2F5I2J8hbtBt4/9i8Mb2y2o+9Tepj5tUrSdU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 08 Jul 2025 14:22:06 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eu7w3d5g1a"}, {"Name": "integrity", "Value": "sha256-tnamWuQ2F5I2J8hbtBt4/9i8Mb2y2o+9Tepj5tUrSdU="}, {"Name": "label", "Value": "_content/Microsoft.AspNetCore.Components.QuickGrid/QuickGrid.razor.js"}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.AspNetCore.Components.QuickGrid/QuickGrid.razor.js", "AssetFile": "_content/Microsoft.AspNetCore.Components.QuickGrid/QuickGrid.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2563"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tnamWuQ2F5I2J8hbtBt4/9i8Mb2y2o+9Tepj5tUrSdU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 08 Jul 2025 14:22:06 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tnamWuQ2F5I2J8hbtBt4/9i8Mb2y2o+9Tepj5tUrSdU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "css/app.9tb3kzisiw.css", "AssetFile": "css/app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "83751"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"jX5XW7u+zfouFeAU/R8Z732rQ/IG+5rVhGtdkVVN+R4=\""}, {"Name": "Last-Modified", "Value": "Mon, 18 Aug 2025 08:43:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9tb3kzisiw"}, {"Name": "integrity", "Value": "sha256-jX5XW7u+zfouFeAU/R8Z732rQ/IG+5rVhGtdkVVN+R4="}, {"Name": "label", "Value": "css/app.css"}]}, {"Route": "css/app.css", "AssetFile": "css/app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "83751"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"jX5XW7u+zfouFeAU/R8Z732rQ/IG+5rVhGtdkVVN+R4=\""}, {"Name": "Last-Modified", "Value": "Mon, 18 Aug 2025 08:43:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jX5XW7u+zfouFeAU/R8Z732rQ/IG+5rVhGtdkVVN+R4="}]}, {"Route": "fix-auth.2dtiwq59h3.html", "AssetFile": "fix-auth.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9243"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"2kIx0eewfSC3U1XtJCDubzlUo8P13rsd71MWq4BqjOc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 13:20:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2dtiwq59h3"}, {"Name": "integrity", "Value": "sha256-2kIx0eewfSC3U1XtJCDubzlUo8P13rsd71MWq4BqjOc="}, {"Name": "label", "Value": "fix-auth.html"}]}, {"Route": "fix-auth.html", "AssetFile": "fix-auth.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9243"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"2kIx0eewfSC3U1XtJCDubzlUo8P13rsd71MWq4BqjOc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 13:20:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2kIx0eewfSC3U1XtJCDubzlUo8P13rsd71MWq4BqjOc="}]}, {"Route": "js/charts.js", "AssetFile": "js/charts.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "26640"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BADcrQvP4PEQYk4Nla49BYk143H0fn0TXL79xPgCy3E=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 08:57:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BADcrQvP4PEQYk4Nla49BYk143H0fn0TXL79xPgCy3E="}]}, {"Route": "js/charts.su7ha9zexa.js", "AssetFile": "js/charts.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "26640"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BADcrQvP4PEQYk4Nla49BYk143H0fn0TXL79xPgCy3E=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 08:57:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "su7ha9zexa"}, {"Name": "integrity", "Value": "sha256-BADcrQvP4PEQYk4Nla49BYk143H0fn0TXL79xPgCy3E="}, {"Name": "label", "Value": "js/charts.js"}]}, {"Route": "uploads/employee-of-month/1577caac-a3d8-487d-9065-637c41e26c1c.k4b9mllmbd.pdf", "AssetFile": "uploads/employee-of-month/1577caac-a3d8-487d-9065-637c41e26c1c.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "394795"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM=\""}, {"Name": "Last-Modified", "Value": "Sun, 17 Aug 2025 08:42:47 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k4b9mllmbd"}, {"Name": "integrity", "Value": "sha256-bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM="}, {"Name": "label", "Value": "uploads/employee-of-month/1577caac-a3d8-487d-9065-637c41e26c1c.pdf"}]}, {"Route": "uploads/employee-of-month/1577caac-a3d8-487d-9065-637c41e26c1c.pdf", "AssetFile": "uploads/employee-of-month/1577caac-a3d8-487d-9065-637c41e26c1c.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "394795"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM=\""}, {"Name": "Last-Modified", "Value": "Sun, 17 Aug 2025 08:42:47 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM="}]}, {"Route": "uploads/employee-of-month/48b23ee5-d08d-4953-9cbf-94308891729a.k4b9mllmbd.pdf", "AssetFile": "uploads/employee-of-month/48b23ee5-d08d-4953-9cbf-94308891729a.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "394795"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM=\""}, {"Name": "Last-Modified", "Value": "Sun, 17 Aug 2025 08:25:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k4b9mllmbd"}, {"Name": "integrity", "Value": "sha256-bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM="}, {"Name": "label", "Value": "uploads/employee-of-month/48b23ee5-d08d-4953-9cbf-94308891729a.pdf"}]}, {"Route": "uploads/employee-of-month/48b23ee5-d08d-4953-9cbf-94308891729a.pdf", "AssetFile": "uploads/employee-of-month/48b23ee5-d08d-4953-9cbf-94308891729a.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "394795"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM=\""}, {"Name": "Last-Modified", "Value": "Sun, 17 Aug 2025 08:25:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM="}]}, {"Route": "uploads/employee-of-month/61a1e616-a4a7-4d01-85fe-919706bfe6fa.k4b9mllmbd.pdf", "AssetFile": "uploads/employee-of-month/61a1e616-a4a7-4d01-85fe-919706bfe6fa.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "394795"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 12 Aug 2025 08:04:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k4b9mllmbd"}, {"Name": "integrity", "Value": "sha256-bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM="}, {"Name": "label", "Value": "uploads/employee-of-month/61a1e616-a4a7-4d01-85fe-919706bfe6fa.pdf"}]}, {"Route": "uploads/employee-of-month/61a1e616-a4a7-4d01-85fe-919706bfe6fa.pdf", "AssetFile": "uploads/employee-of-month/61a1e616-a4a7-4d01-85fe-919706bfe6fa.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "394795"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 12 Aug 2025 08:04:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM="}]}, {"Route": "uploads/employee-of-month/da84b545-8ad8-45a3-94aa-00a37032554f.k4b9mllmbd.pdf", "AssetFile": "uploads/employee-of-month/da84b545-8ad8-45a3-94aa-00a37032554f.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "394795"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM=\""}, {"Name": "Last-Modified", "Value": "Wed, 13 Aug 2025 07:43:05 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k4b9mllmbd"}, {"Name": "integrity", "Value": "sha256-bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM="}, {"Name": "label", "Value": "uploads/employee-of-month/da84b545-8ad8-45a3-94aa-00a37032554f.pdf"}]}, {"Route": "uploads/employee-of-month/da84b545-8ad8-45a3-94aa-00a37032554f.pdf", "AssetFile": "uploads/employee-of-month/da84b545-8ad8-45a3-94aa-00a37032554f.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "394795"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM=\""}, {"Name": "Last-Modified", "Value": "Wed, 13 Aug 2025 07:43:05 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM="}]}]}