# PowerShell script to check Employee IDs in the SQLite database
Write-Host "=== Employee ID Analysis ===" -ForegroundColor Green
Write-Host "Timestamp: $(Get-Date)" -ForegroundColor Gray
Write-Host ""

# Check if database file exists
$dbPath = "employee_rating.db"
if (-not (Test-Path $dbPath)) {
    Write-Host "ERROR: Database file '$dbPath' not found!" -ForegroundColor Red
    Write-Host "Current directory: $(Get-Location)" -ForegroundColor Yellow
    Write-Host "Files in current directory:" -ForegroundColor Yellow
    Get-ChildItem -Name "*.db"
    exit 1
}

Write-Host "Database file found: $dbPath" -ForegroundColor Green
Write-Host "Database size: $((Get-Item $dbPath).Length) bytes" -ForegroundColor Gray
Write-Host ""

# Try to use sqlite3 command if available
$sqlite3Available = $false
try {
    $null = Get-Command sqlite3 -ErrorAction Stop
    $sqlite3Available = $true
    Write-Host "SQLite3 command line tool is available" -ForegroundColor Green
} catch {
    Write-Host "SQLite3 command line tool not available, trying alternative methods..." -ForegroundColor Yellow
}

if ($sqlite3Available) {
    Write-Host "=== QUERYING DATABASE WITH SQLITE3 ===" -ForegroundColor Cyan
    
    # Query all users
    Write-Host "`nAll Users in Database:" -ForegroundColor Yellow
    $query = "SELECT EmployeeId, Email, EnglishName, IsActive, IsDeleted FROM AspNetUsers ORDER BY EmployeeId;"
    sqlite3 $dbPath $query | ForEach-Object {
        $fields = $_ -split '\|'
        if ($fields.Count -ge 5) {
            $empId = $fields[0]
            $email = $fields[1]
            $name = $fields[2]
            $isActive = $fields[3]
            $isDeleted = $fields[4]
            
            $status = if ($isDeleted -eq "1") { "[DELETED]" } elseif ($isActive -eq "0") { "[INACTIVE]" } else { "[ACTIVE]" }
            Write-Host "  $empId | $email | $name $status" -ForegroundColor $(if ($isDeleted -eq "1") { "Red" } elseif ($isActive -eq "0") { "Yellow" } else { "White" })
        }
    }
    
    # Count users
    Write-Host "`nUser Statistics:" -ForegroundColor Yellow
    $totalUsers = sqlite3 $dbPath "SELECT COUNT(*) FROM AspNetUsers;"
    $activeUsers = sqlite3 $dbPath "SELECT COUNT(*) FROM AspNetUsers WHERE IsDeleted = 0;"
    $deletedUsers = sqlite3 $dbPath "SELECT COUNT(*) FROM AspNetUsers WHERE IsDeleted = 1;"
    
    Write-Host "  Total Users: $totalUsers" -ForegroundColor White
    Write-Host "  Active Users: $activeUsers" -ForegroundColor Green
    Write-Host "  Deleted Users: $deletedUsers" -ForegroundColor Red
    
    # Check for duplicates
    Write-Host "`nChecking for Duplicate Employee IDs:" -ForegroundColor Yellow
    $duplicates = sqlite3 $dbPath "SELECT EmployeeId, COUNT(*) as count FROM AspNetUsers GROUP BY EmployeeId HAVING COUNT(*) > 1;"
    if ($duplicates) {
        Write-Host "  DUPLICATES FOUND:" -ForegroundColor Red
        $duplicates | ForEach-Object {
            Write-Host "    $_" -ForegroundColor Red
        }
    } else {
        Write-Host "  No duplicates found" -ForegroundColor Green
    }
    
    # Show existing Employee IDs
    Write-Host "`nExisting Employee IDs (Active Users Only):" -ForegroundColor Yellow
    $activeEmployeeIds = sqlite3 $dbPath "SELECT EmployeeId FROM AspNetUsers WHERE IsDeleted = 0 ORDER BY EmployeeId;"
    $activeEmployeeIds | ForEach-Object {
        Write-Host "  $_" -ForegroundColor Cyan
    }
    
} else {
    Write-Host "=== ALTERNATIVE DATABASE ANALYSIS ===" -ForegroundColor Cyan
    Write-Host "SQLite3 not available. Trying .NET approach..." -ForegroundColor Yellow
    
    # Try using .NET SQLite provider
    try {
        Add-Type -Path "System.Data.SQLite.dll" -ErrorAction Stop
        Write-Host "System.Data.SQLite loaded successfully" -ForegroundColor Green
        
        $connectionString = "Data Source=$dbPath;Version=3;Read Only=True;"
        $connection = New-Object System.Data.SQLite.SQLiteConnection($connectionString)
        $connection.Open()
        
        $command = $connection.CreateCommand()
        $command.CommandText = "SELECT EmployeeId, Email, EnglishName, IsActive, IsDeleted FROM AspNetUsers ORDER BY EmployeeId;"
        $reader = $command.ExecuteReader()
        
        Write-Host "`nUsers in Database:" -ForegroundColor Yellow
        while ($reader.Read()) {
            $empId = $reader["EmployeeId"]
            $email = $reader["Email"]
            $name = $reader["EnglishName"]
            $isActive = $reader["IsActive"]
            $isDeleted = $reader["IsDeleted"]
            
            $status = if ($isDeleted) { "[DELETED]" } elseif (-not $isActive) { "[INACTIVE]" } else { "[ACTIVE]" }
            Write-Host "  $empId | $email | $name $status" -ForegroundColor $(if ($isDeleted) { "Red" } elseif (-not $isActive) { "Yellow" } else { "White" })
        }
        
        $reader.Close()
        $connection.Close()
        
    } catch {
        Write-Host "Could not load .NET SQLite provider: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Manual database inspection required." -ForegroundColor Yellow
    }
}

# Generate suggested unique Employee IDs
Write-Host "`n=== SUGGESTED UNIQUE EMPLOYEE IDs ===" -ForegroundColor Green

# Get current timestamp for unique ID generation
$timestamp = Get-Date -Format "yyyyMMddHHmmss"
$timestampId = "EMP$timestamp"

Write-Host "Timestamp-based ID: $timestampId" -ForegroundColor Cyan

# Generate sequential suggestions
Write-Host "`nSequential ID suggestions:" -ForegroundColor Yellow
$suggestions = @()
for ($i = 1; $i -le 20; $i++) {
    $candidateId = "EMP{0:D3}" -f $i
    $suggestions += $candidateId
}

$suggestions | ForEach-Object {
    Write-Host "  $_" -ForegroundColor Cyan
}

# Generate alternative patterns
Write-Host "`nAlternative patterns:" -ForegroundColor Yellow
$patterns = @("USER", "TEST", "NEW", "TEMP")
foreach ($pattern in $patterns) {
    for ($i = 1; $i -le 3; $i++) {
        $candidateId = "$pattern{0:D3}" -f $i
        Write-Host "  $candidateId" -ForegroundColor Cyan
    }
}

Write-Host "`n=== RECOMMENDATIONS ===" -ForegroundColor Green
Write-Host "1. Use the timestamp-based ID: $timestampId" -ForegroundColor White
Write-Host "2. Or choose any of the sequential IDs that don't appear in the active users list above" -ForegroundColor White
Write-Host "3. Avoid using Employee IDs that are marked as [DELETED] to prevent confusion" -ForegroundColor White
Write-Host "4. Test the chosen Employee ID using the diagnostic page before creating the user" -ForegroundColor White

Write-Host "`nScript completed at $(Get-Date)" -ForegroundColor Gray
