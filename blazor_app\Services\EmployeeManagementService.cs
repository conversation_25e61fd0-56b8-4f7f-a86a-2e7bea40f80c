using EmployeeRatingSystem.Blazor.Data;
using EmployeeRatingSystem.Blazor.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace EmployeeRatingSystem.Blazor.Services
{
    /// <summary>
    /// Service for managing employee authentication, role assignment, and department relationships.
    /// Implements automatic role assignment based on Employee ID and comprehensive authorization logic.
    /// </summary>
    public class EmployeeManagementService : IEmployeeManagementService
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<EmployeeManagementService> _logger;

        public EmployeeManagementService(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            ILogger<EmployeeManagementService> logger)
        {
            _context = context;
            _userManager = userManager;
            _logger = logger;
        }

        public async Task<ApplicationUser?> GetUserByEmployeeIdAsync(string employeeId)
        {
            return await _context.Users
                .Include(u => u.PrimaryDepartment)
                .Include(u => u.UserDepartments)
                    .ThenInclude(ud => ud.Department)
                .Include(u => u.ManagedDepartments)
                .FirstOrDefaultAsync(u => u.EmployeeId == employeeId && !u.IsDeleted);
        }

        public async Task<(UserRole role, int? departmentId)> GetRoleAndDepartmentAssignmentAsync(string employeeId)
        {
            // Simplified implementation for now - check for pre-configured assignment later
            // var preConfig = await _context.Set<EmployeePreConfiguration>()
            //     .Include(pc => pc.PrimaryDepartment)
            //     .FirstOrDefaultAsync(pc => pc.EmployeeId == employeeId && pc.IsCurrentlyEffective());

            // if (preConfig != null)
            // {
            //     return (preConfig.AssignedRole, preConfig.PrimaryDepartmentId);
            // }

            // Default assignment logic based on Employee ID patterns
            return GetDefaultRoleAndDepartmentAssignment(employeeId);
        }

        public async Task<bool> IsEmployeeIdPreConfiguredAsync(string employeeId)
        {
            // Simplified for now
            return false;
            // return await _context.Set<EmployeePreConfiguration>()
            //     .AnyAsync(pc => pc.EmployeeId == employeeId && pc.IsCurrentlyEffective());
        }

        public async Task<bool> AssignRoleAndDepartmentAsync(string userId, UserRole role, int? departmentId, string assignedByUserId)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null) return false;

                // Update user role
                user.Role = role;
                user.PrimaryDepartmentId = departmentId;

                var result = await _userManager.UpdateAsync(user);
                if (!result.Succeeded) return false;

                // Update Identity roles
                var currentRoles = await _userManager.GetRolesAsync(user);
                await _userManager.RemoveFromRolesAsync(user, currentRoles);
                await _userManager.AddToRoleAsync(user, role.ToString());

                // Create or update user-department relationship
                if (departmentId.HasValue)
                {
                    await UpdateUserDepartmentRelationshipAsync(userId, departmentId.Value, role);
                }

                _logger.LogInformation($"Role and department assigned: User {user.EmployeeId} -> {role} in Department {departmentId} by {assignedByUserId}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error assigning role and department to user {userId}");
                return false;
            }
        }

        public async Task<IEnumerable<Department>> GetAccessibleDepartmentsAsync(string userId)
        {
            var user = await _context.Users
                .Include(u => u.PrimaryDepartment)
                .Include(u => u.UserDepartments)
                    .ThenInclude(ud => ud.Department)
                .Include(u => u.ManagedDepartments)
                .FirstOrDefaultAsync(u => u.Id == userId);

            if (user == null) return Enumerable.Empty<Department>();

            var accessibleDepartments = new HashSet<Department>();

            // Super Admin and Excellence Team can access all departments
            if (user.Role == UserRole.SUPER_ADMIN || user.Role == UserRole.EXCELLENCE_TEAM)
            {
                return await _context.Departments
                    .Where(d => d.IsActive && !d.IsDeleted)
                    .ToListAsync();
            }

            // SUPERVISOR role restriction: Only access their primary department
            // PRD Section 2.1: "Limited to direct report employees within assigned team/sub-department"
            if (user.Role == UserRole.SUPERVISOR)
            {
                if (user.PrimaryDepartment != null)
                {
                    return new List<Department> { user.PrimaryDepartment };
                }
                return Enumerable.Empty<Department>();
            }

            // EMPLOYEE role restriction: Only access their primary department (read-only)
            // Employees should only see evaluations within their own department for transparency
            if (user.Role == UserRole.EMPLOYEE)
            {
                if (user.PrimaryDepartment != null)
                {
                    return new List<Department> { user.PrimaryDepartment };
                }
                return Enumerable.Empty<Department>();
            }

            // Add primary department for other roles
            if (user.PrimaryDepartment != null)
            {
                accessibleDepartments.Add(user.PrimaryDepartment);
            }

            // Add managed departments
            foreach (var dept in user.ManagedDepartments)
            {
                accessibleDepartments.Add(dept);
                // Add sub-departments for managers
                if (user.Role == UserRole.MANAGER)
                {
                    var subDepartments = await GetSubDepartmentsAsync(dept.Id);
                    foreach (var subDept in subDepartments)
                    {
                        accessibleDepartments.Add(subDept);
                    }
                }
            }

            // Add departments from user-department relationships
            foreach (var userDept in user.UserDepartments.Where(ud => ud.IsActive))
            {
                accessibleDepartments.Add(userDept.Department);

                // If user has management role, add sub-departments
                if (userDept.HasManagementRole())
                {
                    var subDepartments = await GetSubDepartmentsAsync(userDept.DepartmentId);
                    foreach (var subDept in subDepartments)
                    {
                        accessibleDepartments.Add(subDept);
                    }
                }
            }

            return accessibleDepartments.Where(d => d.IsActive && !d.IsDeleted);
        }

        public async Task<bool> CanUserEvaluateAsync(string evaluatorUserId, string targetUserId)
        {
            var evaluator = await GetUserByEmployeeIdAsync(evaluatorUserId) ??
                           await _userManager.FindByIdAsync(evaluatorUserId);
            var target = await GetUserByEmployeeIdAsync(targetUserId) ??
                        await _userManager.FindByIdAsync(targetUserId);

            if (evaluator == null || target == null) return false;

            // Super Admin can evaluate anyone
            if (evaluator.Role == UserRole.SUPER_ADMIN) return true;

            // Excellence Team has full evaluation rights equivalent to SUPER_ADMIN
            if (evaluator.Role == UserRole.EXCELLENCE_TEAM)
            {
                return true; // Full evaluation rights - Override PRD Section 2.2
            }

            // Managers can evaluate users in their managed departments and sub-departments
            if (evaluator.Role == UserRole.MANAGER)
            {
                var managedDepartments = await GetManagedDepartmentsAsync(evaluator.Id);
                return managedDepartments.Any(d => d.Id == target.PrimaryDepartmentId);
            }

            // Supervisors can only evaluate employees in their own department
            // PRD Section 2.1: "Limited to direct report employees within assigned team/sub-department"
            if (evaluator.Role == UserRole.SUPERVISOR)
            {
                // Must be in the same department and target must be an employee (not another supervisor/manager)
                return evaluator.PrimaryDepartmentId == target.PrimaryDepartmentId &&
                       target.Role == UserRole.EMPLOYEE;
            }

            return false;
        }

        public async Task<IEnumerable<ApplicationUser>> GetEvaluatableUsersAsync(string evaluatorUserId)
        {
            var evaluator = await _userManager.FindByIdAsync(evaluatorUserId);
            if (evaluator == null) return Enumerable.Empty<ApplicationUser>();

            // Special handling for SUPERVISOR role - restrict to only their department
            if (evaluator.Role == UserRole.SUPERVISOR)
            {
                return await GetSupervisorDirectReportsAsync(evaluatorUserId);
            }

            var accessibleDepartments = await GetAccessibleDepartmentsAsync(evaluatorUserId);
            var departmentIds = accessibleDepartments.Select(d => d.Id).ToList();

            return await _context.Users
                .Where(u => u.Id != evaluatorUserId &&
                           !u.IsDeleted &&
                           u.IsActive &&
                           departmentIds.Contains(u.PrimaryDepartmentId ?? 0))
                .Include(u => u.PrimaryDepartment)
                .ToListAsync();
        }

        public async Task<IEnumerable<Department>> GetManagedDepartmentsAsync(string userId)
        {
            var user = await _context.Users
                .Include(u => u.ManagedDepartments)
                .Include(u => u.UserDepartments)
                    .ThenInclude(ud => ud.Department)
                .FirstOrDefaultAsync(u => u.Id == userId);

            if (user == null) return Enumerable.Empty<Department>();

            var managedDepartments = new HashSet<Department>();

            // Add explicitly managed departments
            foreach (var dept in user.ManagedDepartments)
            {
                managedDepartments.Add(dept);
                // Add sub-departments
                var subDepartments = await GetSubDepartmentsAsync(dept.Id);
                foreach (var subDept in subDepartments)
                {
                    managedDepartments.Add(subDept);
                }
            }

            // Add departments where user has management role
            foreach (var userDept in user.UserDepartments.Where(ud => ud.IsActive && ud.HasManagementRole()))
            {
                managedDepartments.Add(userDept.Department);
                var subDepartments = await GetSubDepartmentsAsync(userDept.DepartmentId);
                foreach (var subDept in subDepartments)
                {
                    managedDepartments.Add(subDept);
                }
            }

            return managedDepartments.Where(d => d.IsActive && !d.IsDeleted);
        }

        public async Task<bool> HasManagementRoleAsync(string userId)
        {
            var user = await _context.Users
                .Include(u => u.UserDepartments)
                .FirstOrDefaultAsync(u => u.Id == userId);

            if (user == null) return false;

            return user.Role == UserRole.SUPER_ADMIN ||
                   user.Role == UserRole.EXCELLENCE_TEAM ||
                   user.Role == UserRole.MANAGER ||
                   user.Role == UserRole.SUPERVISOR ||
                   user.UserDepartments.Any(ud => ud.IsActive && ud.HasManagementRole());
        }

        public async Task<DepartmentRole?> GetUserRoleInDepartmentAsync(string userId, int departmentId)
        {
            var userDepartment = await _context.Set<UserDepartment>()
                .FirstOrDefaultAsync(ud => ud.UserId == userId &&
                                          ud.DepartmentId == departmentId &&
                                          ud.IsActive);

            return userDepartment?.RoleInDepartment;
        }

        public async Task<bool> CreateEmployeePreConfigurationAsync(string employeeId, UserRole role, int? departmentId, string createdByUserId)
        {
            // Temporarily simplified - will implement later
            return false;
            /*
            try
            {
                // Check if configuration already exists
                var existing = await _context.Set<EmployeePreConfiguration>()
                    .FirstOrDefaultAsync(pc => pc.EmployeeId == employeeId && !pc.IsDeleted);

                if (existing != null)
                {
                    _logger.LogWarning($"Employee pre-configuration already exists for Employee ID: {employeeId}");
                    return false;
                }

                var preConfig = new EmployeePreConfiguration
                {
                    EmployeeId = employeeId,
                    AssignedRole = role,
                    PrimaryDepartmentId = departmentId,
                    CreatedByUserId = createdByUserId,
                    IsActive = true,
                    EffectiveDate = DateTime.UtcNow
                };

                _context.Set<EmployeePreConfiguration>().Add(preConfig);
                await _context.SaveChangesAsync();

                _logger.LogInformation($"Employee pre-configuration created: {employeeId} -> {role} by {createdByUserId}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error creating employee pre-configuration for {employeeId}");
                return false;
            }
            */
        }

        public async Task<bool> UpdateEmployeePreConfigurationAsync(string employeeId, UserRole role, int? departmentId, string updatedByUserId)
        {
            // Temporarily simplified
            return false;
        }

        public async Task<bool> DeleteEmployeePreConfigurationAsync(string employeeId, string deletedByUserId)
        {
            // Temporarily simplified
            return false;
        }

        public async Task<IEnumerable<EmployeePreConfiguration>> GetAllEmployeePreConfigurationsAsync()
        {
            // Temporarily simplified
            return Enumerable.Empty<EmployeePreConfiguration>();
        }

        // Helper methods
        private (UserRole role, int? departmentId) GetDefaultRoleAndDepartmentAssignment(string employeeId)
        {
            // Default assignment logic - can be customized based on Employee ID patterns
            // For now, all new employees default to EMPLOYEE role with no department
            return (UserRole.EMPLOYEE, null);
        }

        private async Task<IEnumerable<Department>> GetSubDepartmentsAsync(int departmentId)
        {
            return await _context.Departments
                .Where(d => d.ParentId == departmentId && d.IsActive && !d.IsDeleted)
                .ToListAsync();
        }

        private async Task<IEnumerable<Department>> GetSupervisedDepartmentsAsync(string userId)
        {
            return await _context.Set<UserDepartment>()
                .Where(ud => ud.UserId == userId &&
                            ud.IsActive &&
                            ud.RoleInDepartment == DepartmentRole.SUPERVISOR)
                .Select(ud => ud.Department)
                .Where(d => d.IsActive && !d.IsDeleted)
                .ToListAsync();
        }

        /// <summary>
        /// Get employees that a supervisor can directly evaluate (only within their assigned department)
        /// Implements PRD Section 2.1: "Limited to direct report employees within assigned team/sub-department"
        /// </summary>
        private async Task<IEnumerable<ApplicationUser>> GetSupervisorDirectReportsAsync(string supervisorUserId)
        {
            var supervisor = await _context.Users
                .Include(u => u.PrimaryDepartment)
                .FirstOrDefaultAsync(u => u.Id == supervisorUserId);

            if (supervisor?.PrimaryDepartmentId == null)
            {
                return Enumerable.Empty<ApplicationUser>();
            }

            // Supervisors can only evaluate employees in their own department
            // This enforces departmental isolation as required
            return await _context.Users
                .Where(u => u.Id != supervisorUserId &&
                           !u.IsDeleted &&
                           u.IsActive &&
                           u.PrimaryDepartmentId == supervisor.PrimaryDepartmentId &&
                           u.Role == UserRole.EMPLOYEE) // Only employees, not other supervisors/managers
                .Include(u => u.PrimaryDepartment)
                .OrderBy(u => u.EnglishName)
                .ToListAsync();
        }

        /// <summary>
        /// Check if a user can view a specific evaluation based on their role and department
        /// Implements departmental access restrictions for employees and supervisors
        /// </summary>
        public async Task<bool> CanUserViewEvaluationAsync(string userId, int evaluationId)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null) return false;

            // Super Admin and Excellence Team can view all evaluations
            if (user.Role == UserRole.SUPER_ADMIN || user.Role == UserRole.EXCELLENCE_TEAM)
            {
                return true;
            }

            // Get the evaluation with employee information
            var evaluation = await _context.Evaluations
                .Include(e => e.Employee)
                .FirstOrDefaultAsync(e => e.Id == evaluationId);

            if (evaluation?.Employee == null) return false;

            // SUPERVISOR role: Can view evaluations they created or within their department
            if (user.Role == UserRole.SUPERVISOR)
            {
                return evaluation.EvaluatorId == user.Id ||
                       (evaluation.Employee.PrimaryDepartmentId == user.PrimaryDepartmentId &&
                        evaluation.Employee.Role == UserRole.EMPLOYEE);
            }

            // EMPLOYEE role: Can only view evaluations within their department (read-only)
            if (user.Role == UserRole.EMPLOYEE)
            {
                return evaluation.Employee.PrimaryDepartmentId == user.PrimaryDepartmentId;
            }

            // MANAGER role: Can view evaluations in accessible departments
            if (user.Role == UserRole.MANAGER)
            {
                var accessibleDepartments = await GetAccessibleDepartmentsAsync(user.Id);
                var departmentIds = accessibleDepartments.Select(d => d.Id).ToList();
                return departmentIds.Contains(evaluation.Employee.PrimaryDepartmentId ?? 0);
            }

            return false;
        }

        private async Task UpdateUserDepartmentRelationshipAsync(string userId, int departmentId, UserRole role)
        {
            // Remove existing primary department relationship
            var existingPrimary = await _context.Set<UserDepartment>()
                .FirstOrDefaultAsync(ud => ud.UserId == userId && ud.IsPrimary);

            if (existingPrimary != null)
            {
                existingPrimary.IsPrimary = false;
            }

            // Create or update department relationship
            var userDepartment = await _context.Set<UserDepartment>()
                .FirstOrDefaultAsync(ud => ud.UserId == userId && ud.DepartmentId == departmentId);

            if (userDepartment == null)
            {
                userDepartment = new UserDepartment
                {
                    UserId = userId,
                    DepartmentId = departmentId,
                    IsPrimary = true,
                    RoleInDepartment = GetDepartmentRoleFromUserRole(role),
                    IsActive = true,
                    AssignedDate = DateTime.UtcNow
                };
                _context.Set<UserDepartment>().Add(userDepartment);
            }
            else
            {
                userDepartment.IsPrimary = true;
                userDepartment.RoleInDepartment = GetDepartmentRoleFromUserRole(role);
                userDepartment.IsActive = true;
            }

            await _context.SaveChangesAsync();
        }

        private static DepartmentRole GetDepartmentRoleFromUserRole(UserRole userRole)
        {
            return userRole switch
            {
                UserRole.MANAGER => DepartmentRole.MANAGER,
                UserRole.SUPERVISOR => DepartmentRole.SUPERVISOR,
                _ => DepartmentRole.EMPLOYEE
            };
        }
    }
}