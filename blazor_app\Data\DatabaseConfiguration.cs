using Microsoft.EntityFrameworkCore;

namespace EmployeeRatingSystem.Blazor.Data
{
    /// <summary>
    /// Database configuration service to handle multiple database providers.
    /// Equivalent to Django's database configuration with environment variables.
    /// </summary>
    public static class DatabaseConfiguration
    {
        /// <summary>
        /// Configure the database context for SQLite only (simplified for disk space).
        /// </summary>
        public static void ConfigureDatabase(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddDbContext<ApplicationDbContext>(options =>
            {
                ConfigureSqlite(options, configuration);
            });
        }

        /// <summary>
        /// Configure SQLite database (default for development)
        /// </summary>
        private static void ConfigureSqlite(DbContextOptionsBuilder options, IConfiguration configuration)
        {
            var connectionString = configuration.GetConnectionString("DefaultConnection") 
                ?? "Data Source=employee_rating.db";
            
            options.UseSqlite(connectionString, sqliteOptions =>
            {
                sqliteOptions.CommandTimeout(30);
            });
        }

        /// <summary>
        /// Configure PostgreSQL database (production option)
        /// Note: Requires Npgsql.EntityFrameworkCore.PostgreSQL package
        /// </summary>
        /*
        private static void ConfigurePostgreSQL(DbContextOptionsBuilder options, IConfiguration configuration)
        {
            var host = configuration.GetValue<string>("POSTGRES_HOST") ?? "localhost";
            var port = configuration.GetValue<string>("POSTGRES_PORT") ?? "5432";
            var database = configuration.GetValue<string>("POSTGRES_DB") ?? "employee_rating";
            var username = configuration.GetValue<string>("POSTGRES_USER") ?? "postgres";
            var password = configuration.GetValue<string>("POSTGRES_PASSWORD") ?? "";

            var connectionString = configuration.GetConnectionString("PostgreSQL")
                ?? $"Host={host};Port={port};Database={database};Username={username};Password={password}";

            options.UseNpgsql(connectionString, npgsqlOptions =>
            {
                npgsqlOptions.CommandTimeout(30);
                npgsqlOptions.EnableRetryOnFailure(
                    maxRetryCount: 3,
                    maxRetryDelay: TimeSpan.FromSeconds(30),
                    errorCodesToAdd: null);
            });
        }
        */

        /// <summary>
        /// Configure SQL Server database (production option)
        /// Note: Requires Microsoft.EntityFrameworkCore.SqlServer package
        /// </summary>
        /*
        private static void ConfigureSqlServer(DbContextOptionsBuilder options, IConfiguration configuration)
        {
            var server = configuration.GetValue<string>("SQLSERVER_HOST") ?? "localhost";
            var database = configuration.GetValue<string>("SQLSERVER_DB") ?? "EmployeeRating";
            var username = configuration.GetValue<string>("SQLSERVER_USER");
            var password = configuration.GetValue<string>("SQLSERVER_PASSWORD");
            var integratedSecurity = configuration.GetValue<bool>("SQLSERVER_INTEGRATED_SECURITY", true);

            string connectionString;

            if (!string.IsNullOrEmpty(username) && !string.IsNullOrEmpty(password))
            {
                connectionString = configuration.GetConnectionString("SqlServer")
                    ?? $"Server={server};Database={database};User Id={username};Password={password};TrustServerCertificate=true;";
            }
            else
            {
                connectionString = configuration.GetConnectionString("SqlServer")
                    ?? $"Server={server};Database={database};Integrated Security={integratedSecurity};TrustServerCertificate=true;";
            }

            options.UseSqlServer(connectionString, sqlServerOptions =>
            {
                sqlServerOptions.CommandTimeout(30);
                sqlServerOptions.EnableRetryOnFailure(
                    maxRetryCount: 3,
                    maxRetryDelay: TimeSpan.FromSeconds(30),
                    errorNumbersToAdd: null);
            });
        }
        */

        /// <summary>
        /// Get the current database provider name
        /// </summary>
        public static string GetDatabaseProvider(IConfiguration configuration)
        {
            return configuration.GetValue<string>("DATABASE_ENGINE")?.ToLower() ?? "sqlite";
        }

        /// <summary>
        /// Check if the current database provider supports migrations
        /// </summary>
        public static bool SupportsMigrations(IConfiguration configuration)
        {
            var provider = GetDatabaseProvider(configuration);
            return provider != "inmemory"; // All our providers support migrations
        }

        /// <summary>
        /// Get database-specific configuration for Entity Framework
        /// </summary>
        public static void ConfigureDatabaseSpecificOptions(DbContextOptionsBuilder options, string provider)
        {
            switch (provider.ToLower())
            {
                case "sqlite":
                default:
                    // SQLite specific configurations (development)
                    options.EnableSensitiveDataLogging(true);
                    options.EnableDetailedErrors(true);
                    break;

                // Note: PostgreSQL and SQL Server configurations commented out due to missing packages
                /*
                case "postgresql":
                    // PostgreSQL specific configurations
                    options.EnableSensitiveDataLogging(false);
                    options.EnableDetailedErrors(false);
                    break;
                case "sqlserver":
                    // SQL Server specific configurations
                    options.EnableSensitiveDataLogging(false);
                    options.EnableDetailedErrors(false);
                    break;
                */
            }
        }
    }
}
