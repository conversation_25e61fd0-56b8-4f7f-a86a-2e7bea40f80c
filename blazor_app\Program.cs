using EmployeeRatingSystem.Blazor;
using EmployeeRatingSystem.Blazor.Components;
using EmployeeRatingSystem.Blazor.Data;
using EmployeeRatingSystem.Blazor.Models;
using EmployeeRatingSystem.Blazor.Services;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Localization;
using Microsoft.EntityFrameworkCore;
using System.Globalization;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();

// Configure database
builder.Services.ConfigureDatabase(builder.Configuration);

// Configure Identity
builder.Services.AddIdentity<ApplicationUser, IdentityRole>(options =>
{
    // Password settings
    options.Password.RequireDigit = true;
    options.Password.RequireLowercase = true;
    options.Password.RequireNonAlphanumeric = false;
    options.Password.RequireUppercase = true;
    options.Password.RequiredLength = 6;
    options.Password.RequiredUniqueChars = 1;

    // Lockout settings
    options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(5);
    options.Lockout.MaxFailedAccessAttempts = 5;
    options.Lockout.AllowedForNewUsers = true;

    // User settings
    options.User.AllowedUserNameCharacters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+";
    options.User.RequireUniqueEmail = true;

    // Sign in settings
    options.SignIn.RequireConfirmedEmail = false;
    options.SignIn.RequireConfirmedPhoneNumber = false;
})
.AddEntityFrameworkStores<ApplicationDbContext>()
.AddDefaultTokenProviders();

// Configure localization
builder.Services.AddLocalization(options => options.ResourcesPath = "Resources");
builder.Services.Configure<RequestLocalizationOptions>(options =>
{
    var supportedCultures = new[]
    {
        new CultureInfo("en"),
        new CultureInfo("ar")
    };

    options.DefaultRequestCulture = new RequestCulture("en");
    options.SupportedCultures = supportedCultures;
    options.SupportedUICultures = supportedCultures;

    options.RequestCultureProviders.Insert(0, new QueryStringRequestCultureProvider());
    options.RequestCultureProviders.Insert(1, new CookieRequestCultureProvider());
});

// Add custom services
builder.Services.AddHttpContextAccessor();
builder.Services.AddScoped<ILocalizationService, LocalizationService>();
builder.Services.AddScoped<IBreadcrumbService, BreadcrumbService>();
builder.Services.AddScoped<IDataSeedingService, DataSeedingService>();
builder.Services.AddScoped<IEvaluationService, EvaluationService>();
builder.Services.AddScoped<IEmployeeManagementService, EmployeeManagementService>();
builder.Services.AddScoped<IEmployeeAuthenticationService, EmployeeAuthenticationService>();
builder.Services.AddScoped<IEvaluationCalculationService, EvaluationCalculationService>();
builder.Services.AddScoped<IEvaluationDataSeedingService, EvaluationDataSeedingService>();
builder.Services.AddScoped<IEvaluationReportsService, EvaluationReportsService>();
builder.Services.AddScoped<IEvaluationDuplicateService, EvaluationDuplicateService>();
builder.Services.AddScoped<IEmployeeOfTheMonthService, EmployeeOfTheMonthService>();
builder.Services.AddScoped<IEmployeeOfTheMonthAttachmentService, EmployeeOfTheMonthAttachmentService>();
builder.Services.AddScoped<IEvaluationAttachmentService, EvaluationAttachmentService>();
builder.Services.AddScoped<DashboardStatisticsService>();

// Add authorization
builder.Services.AddAuthorization();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

// Configure localization
var supportedCultures = new[] { "en", "ar" };
var localizationOptions = new RequestLocalizationOptions()
    .SetDefaultCulture(supportedCultures[0])
    .AddSupportedCultures(supportedCultures)
    .AddSupportedUICultures(supportedCultures);

app.UseRequestLocalization(localizationOptions);

app.UseStaticFiles();
app.UseAntiforgery();

// Add authentication and authorization
app.UseAuthentication();
app.UseAuthorization();

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

// Add authentication endpoints
app.MapPost("/api/auth/login", async (HttpContext context, IEmployeeAuthenticationService authService) =>
{
    var form = await context.Request.ReadFormAsync();
    var employeeId = form["employeeId"].ToString();
    var password = form["password"].ToString();
    var rememberMe = form["rememberMe"].ToString() == "true";
    var returnUrl = form["returnUrl"].ToString();

    if (string.IsNullOrEmpty(employeeId) || string.IsNullOrEmpty(password))
    {
        return Results.Redirect("/login?error=validation");
    }

    try
    {
        var result = await authService.PerformActualSignInAsync(employeeId, password, rememberMe);

        if (result.Succeeded)
        {
            var redirectUrl = string.IsNullOrEmpty(returnUrl) ? "/dashboard" : returnUrl;
            return Results.Redirect(redirectUrl);
        }
        else if (result.IsLockedOut)
        {
            return Results.Redirect("/login?error=lockedout");
        }
        else
        {
            return Results.Redirect("/login?error=invalid");
        }
    }
    catch (Exception)
    {
        return Results.Redirect("/login?error=system");
    }
});

app.MapPost("/api/auth/logout", async (HttpContext context, IEmployeeAuthenticationService authService) =>
{
    await authService.SignOutAsync();
    return Results.Redirect("/login");
});

// Add evaluation data seeding endpoint (for testing)
app.MapPost("/api/evaluation/seed-sample-data", async (IEvaluationDataSeedingService seedingService) =>
{
    try
    {
        await seedingService.SeedCurrentMonthDataAsync();
        return Results.Ok(new { message = "Sample data seeded successfully" });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { error = ex.Message });
    }
});

// Add test endpoint to check evaluation data
app.MapGet("/api/evaluation/test-data", async (ApplicationDbContext context) =>
{
    try
    {
        var traditionalEvaluations = await context.Evaluations.CountAsync();
        var comprehensiveEvaluations = await context.ComprehensiveEvaluations.CountAsync();
        var workVolumeData = await context.WorkVolumeData.CountAsync();
        var attendanceData = await context.AttendanceData.CountAsync();
        var supervisorEvaluations = await context.SupervisorEvaluations.CountAsync();

        // Get sample comprehensive evaluation data
        var sampleComprehensive = await context.ComprehensiveEvaluations
            .Include(e => e.Employee)
            .Take(3)
            .Select(e => new {
                e.Id,
                EmployeeName = e.Employee.EnglishName,
                e.TotalScore,
                e.EvaluationPeriod,
                e.Status,
                e.CalculatedAt
            })
            .ToListAsync();

        return Results.Ok(new {
            traditionalEvaluations,
            comprehensiveEvaluations,
            workVolumeData,
            attendanceData,
            supervisorEvaluations,
            sampleComprehensive,
            message = $"Found {traditionalEvaluations} traditional and {comprehensiveEvaluations} comprehensive evaluations"
        });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { error = ex.Message });
    }
});

// Add debug endpoint to check evaluation list data loading
app.MapGet("/api/evaluation/debug-list", async (ApplicationDbContext context) =>
{
    try
    {
        // Simulate the same logic as the Index.razor LoadData method
        var traditionalEvaluations = await context.Evaluations
            .Include(e => e.Employee)
            .ThenInclude(e => e.PrimaryDepartment)
            .Include(e => e.Evaluator)
            .Where(e => !e.IsDeleted)
            .OrderByDescending(e => e.CreatedAt)
            .ToListAsync();

        var comprehensiveEvaluations = await context.ComprehensiveEvaluations
            .Include(e => e.Employee)
            .ThenInclude(e => e.PrimaryDepartment)
            .OrderByDescending(e => e.CalculatedAt)
            .ToListAsync();

        return Results.Ok(new {
            traditionalCount = traditionalEvaluations.Count,
            comprehensiveCount = comprehensiveEvaluations.Count,
            traditionalSample = traditionalEvaluations.Take(2).Select(e => new {
                e.Id,
                EmployeeName = e.Employee?.EnglishName,
                e.TotalScore,
                e.Status,
                e.CreatedAt
            }),
            comprehensiveSample = comprehensiveEvaluations.Take(2).Select(e => new {
                e.Id,
                EmployeeName = e.Employee?.EnglishName,
                e.TotalScore,
                e.Status,
                e.CalculatedAt
            })
        });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { error = ex.Message, stackTrace = ex.StackTrace });
    }
});

// Add debug endpoint to check users
app.MapGet("/api/evaluation/debug-users", async (ApplicationDbContext context) =>
{
    try
    {
        var users = await context.Users
            .Where(u => u.IsActive)
            .Select(u => new {
                u.Id,
                u.EnglishName,
                u.Role,
                u.Email
            })
            .ToListAsync();

        return Results.Ok(new {
            userCount = users.Count,
            users = users
        });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { error = ex.Message, stackTrace = ex.StackTrace });
    }
});

// Add debug endpoint to check EvaluationPeriod values
app.MapGet("/api/evaluation/debug-periods", async (ApplicationDbContext context) =>
{
    try
    {
        var comprehensiveEvaluations = await context.ComprehensiveEvaluations
            .Select(e => new {
                e.Id,
                e.EvaluationPeriod,
                PeriodWithDay = e.EvaluationPeriod + "-01",
                EmployeeName = e.Employee != null ? e.Employee.EnglishName : "Unknown"
            })
            .ToListAsync();

        return Results.Ok(new {
            count = comprehensiveEvaluations.Count,
            periods = comprehensiveEvaluations
        });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { error = ex.Message, stackTrace = ex.StackTrace });
    }
});

// Add endpoint to manually create a test comprehensive evaluation
app.MapPost("/api/evaluation/create-test-comprehensive", async (ApplicationDbContext context) =>
{
    try
    {
        // Get first active user as employee
        var employee = await context.Users.FirstOrDefaultAsync(u => u.IsActive);
        if (employee == null)
        {
            return Results.BadRequest(new { error = "No active users found" });
        }

        // Get first manager as calculator
        var manager = await context.Users.FirstOrDefaultAsync(u => u.Role == UserRole.MANAGER && u.IsActive);
        if (manager == null)
        {
            return Results.BadRequest(new { error = "No active managers found" });
        }

        // Create a test comprehensive evaluation
        var comprehensiveEval = new ComprehensiveEvaluation
        {
            EmployeeId = employee.Id,
            DepartmentId = employee.PrimaryDepartmentId ?? 1,
            EvaluationPeriod = "2025-01",
            WorkVolumeScore = 0.85m,
            WorkVolumePercentage = 85.0m,
            AttendanceScore = 0.90m,
            AttendancePercentage = 90.0m,
            SupervisorScore = 0.80m,
            TotalScore = 0.85m,
            Status = EvaluationStatus.APPROVED,
            CalculatedBy = manager.Id,
            CalculatedAt = DateTime.UtcNow,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            DepartmentRank = 1,
            OverallRank = 1
        };

        context.ComprehensiveEvaluations.Add(comprehensiveEval);
        await context.SaveChangesAsync();

        return Results.Ok(new {
            message = "Test comprehensive evaluation created successfully",
            evaluationId = comprehensiveEval.Id,
            employeeName = employee.EnglishName,
            calculatedBy = manager.EnglishName
        });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { error = ex.Message });
    }
});

// Initialize database with proper error handling
try
{
    using var scope = app.Services.CreateScope();
    var services = scope.ServiceProvider;
    var logger = services.GetRequiredService<ILogger<Program>>();

    logger.LogInformation("Starting database initialization...");

    var context = services.GetRequiredService<ApplicationDbContext>();
    var userManager = services.GetRequiredService<UserManager<ApplicationUser>>();
    var roleManager = services.GetRequiredService<RoleManager<IdentityRole>>();

    // Apply migrations if database provider supports it
    if (DatabaseConfiguration.SupportsMigrations(builder.Configuration))
    {
        context.Database.Migrate();
        logger.LogInformation("Database migrations applied successfully.");
    }

    // Seed initial data
    var seedingService = services.GetRequiredService<IDataSeedingService>();
    await seedingService.SeedAsync();
    logger.LogInformation("Database seeding completed successfully.");
}
catch (Exception ex)
{
    var logger = app.Services.GetRequiredService<ILogger<Program>>();
    logger.LogError(ex, "Database initialization failed. Application will continue without database.");
}

// Add endpoint to update evaluation statuses for demonstration
app.MapPost("/api/evaluation/update-statuses", async (ApplicationDbContext context) =>
{
    try
    {
        // Update some traditional evaluations to different statuses
        var traditionalEvals = await context.Evaluations.ToListAsync();
        if (traditionalEvals.Count > 0)
        {
            traditionalEvals[0].Status = EvaluationStatus.SUBMITTED;
            traditionalEvals[0].SubmittedAt = DateTime.UtcNow;

            if (traditionalEvals.Count > 1)
            {
                traditionalEvals[1].Status = EvaluationStatus.APPROVED;
                traditionalEvals[1].ApprovedAt = DateTime.UtcNow;
            }
        }

        // Update some comprehensive evaluations to different statuses
        var comprehensiveEvals = await context.ComprehensiveEvaluations.ToListAsync();
        if (comprehensiveEvals.Count > 2)
        {
            comprehensiveEvals[2].Status = EvaluationStatus.SUBMITTED;
            if (comprehensiveEvals.Count > 3)
            {
                comprehensiveEvals[3].Status = EvaluationStatus.REJECTED;
            }
        }

        await context.SaveChangesAsync();

        return Results.Ok(new {
            message = "تم تحديث حالات التقييمات بنجاح",
            traditionalUpdated = traditionalEvals.Count,
            comprehensiveUpdated = comprehensiveEvals.Count
        });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { error = ex.Message });
    }
});

// Debug endpoint to check evaluator information in comprehensive evaluations
app.MapGet("/api/evaluation/debug-evaluators", async (ApplicationDbContext context) =>
{
    try
    {
        var comprehensiveEvals = await context.ComprehensiveEvaluations
            .Include(ce => ce.Employee)
            .ToListAsync();

        var evaluatorIds = comprehensiveEvals.Select(ce => ce.CalculatedBy).Distinct().ToList();
        var evaluators = await context.Users
            .Where(u => evaluatorIds.Contains(u.Id))
            .ToListAsync();

        var result = comprehensiveEvals.Select(ce => new
        {
            id = ce.Id,
            employeeName = ce.Employee.EnglishName,
            calculatedBy = ce.CalculatedBy,
            evaluatorName = evaluators.FirstOrDefault(e => e.Id == ce.CalculatedBy)?.EnglishName ?? "Not Found",
            evaluatorExists = evaluators.Any(e => e.Id == ce.CalculatedBy)
        }).ToList();

        return Results.Ok(new
        {
            comprehensiveEvaluations = result,
            totalEvaluators = evaluators.Count,
            evaluatorsList = evaluators.Select(e => new { id = e.Id, name = e.EnglishName }).ToList()
        });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { error = ex.Message });
    }
});

// Authentication fix endpoint
app.MapPost("/api/auth/fix", async (IServiceProvider serviceProvider) =>
{
    using var scope = serviceProvider.CreateScope();
    var userManager = scope.ServiceProvider.GetRequiredService<UserManager<ApplicationUser>>();
    var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
    var logger = scope.ServiceProvider.GetRequiredService<ILogger<FixAuthenticationIssues>>();

    var fixer = new FixAuthenticationIssues(userManager, context, logger);
    var success = await fixer.FixAllAuthenticationIssuesAsync();

    return success
        ? Results.Ok(new { success = true, message = "Authentication issues fixed successfully" })
        : Results.BadRequest(new { success = false, message = "Some issues occurred during fix process" });
});

// Authentication status endpoint
app.MapGet("/api/auth/status", async (IServiceProvider serviceProvider) =>
{
    using var scope = serviceProvider.CreateScope();
    var userManager = scope.ServiceProvider.GetRequiredService<UserManager<ApplicationUser>>();
    var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
    var logger = scope.ServiceProvider.GetRequiredService<ILogger<FixAuthenticationIssues>>();

    var fixer = new FixAuthenticationIssues(userManager, context, logger);
    var status = await fixer.GetUserAuthStatusAsync();

    return Results.Ok(status);
});

app.Run();
