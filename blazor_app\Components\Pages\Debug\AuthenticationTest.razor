@page "/debug/auth-test"
@using Microsoft.AspNetCore.Identity
@using Microsoft.EntityFrameworkCore
@using EmployeeRatingSystem.Blazor.Models
@using EmployeeRatingSystem.Blazor.Data
@using EmployeeRatingSystem.Blazor.Services
@using EmployeeRatingSystem.Blazor.Components.Shared

@inherits LocalizedComponentBase
@inject ApplicationDbContext DbContext
@inject UserManager<ApplicationUser> UserManager
@inject IEmployeeAuthenticationService AuthService

<PageTitle>Authentication Test</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-bug me-2"></i>
                    Authentication Debug & Test
                </h1>
                <div class="btn-group">
                    <button class="btn btn-primary" @onclick="LoadUserData" disabled="@isLoading">
                        <i class="fas fa-sync-alt me-2"></i>
                        Refresh Data
                    </button>
                    <button class="btn btn-success" @onclick="TestAllUsers" disabled="@isLoading">
                        <i class="fas fa-play me-2"></i>
                        Test All Logins
                    </button>
                </div>
            </div>

            @if (!string.IsNullOrEmpty(message))
            {
                <div class="alert alert-@(isError ? "danger" : "success") alert-dismissible fade show">
                    <i class="fas fa-@(isError ? "exclamation-triangle" : "check-circle") me-2"></i>
                    @message
                    <button type="button" class="btn-close" @onclick="ClearMessage"></button>
                </div>
            }

            <!-- User Database State -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-database me-2"></i>
                        User Database State
                    </h5>
                </div>
                <div class="card-body">
                    @if (users.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead>
                                    <tr>
                                        <th>Employee ID</th>
                                        <th>Name</th>
                                        <th>IsActive</th>
                                        <th>IsDeleted</th>
                                        <th>LockoutEnd</th>
                                        <th>AccessFailedCount</th>
                                        <th>Email Confirmed</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var user in users)
                                    {
                                        <tr class="@(GetUserRowClass(user))">
                                            <td><strong>@user.EmployeeId</strong></td>
                                            <td>@user.EnglishName</td>
                                            <td>
                                                <span class="badge bg-@(user.IsActive ? "success" : "danger")">
                                                    @user.IsActive
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-@(user.IsDeleted ? "danger" : "success")">
                                                    @user.IsDeleted
                                                </span>
                                            </td>
                                            <td>
                                                @if (user.LockoutEnd.HasValue && user.LockoutEnd > DateTimeOffset.UtcNow)
                                                {
                                                    <span class="badge bg-warning">Locked</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-success">OK</span>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge bg-@(user.AccessFailedCount > 0 ? "warning" : "success")">
                                                    @user.AccessFailedCount
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-@(user.EmailConfirmed ? "success" : "secondary")">
                                                    @user.EmailConfirmed
                                                </span>
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary" @onclick="() => TestUserLogin(user.EmployeeId)">
                                                    Test Login
                                                </button>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <p class="text-muted">No users found or data not loaded.</p>
                    }
                </div>
            </div>

            <!-- Authentication Test Results -->
            @if (testResults.Any())
            {
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-clipboard-check me-2"></i>
                            Authentication Test Results
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Employee ID</th>
                                        <th>Password Used</th>
                                        <th>Result</th>
                                        <th>Details</th>
                                        <th>Timestamp</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var result in testResults.OrderByDescending(r => r.Timestamp))
                                    {
                                        <tr class="@(result.Success ? "table-success" : "table-danger")">
                                            <td><strong>@result.EmployeeId</strong></td>
                                            <td><code>@result.Password</code></td>
                                            <td>
                                                <span class="badge bg-@(result.Success ? "success" : "danger")">
                                                    @(result.Success ? "SUCCESS" : "FAILED")
                                                </span>
                                            </td>
                                            <td>@result.Details</td>
                                            <td><small>@result.Timestamp.ToString("HH:mm:ss")</small></td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }

            <!-- Debug Logs -->
            @if (debugLogs.Any())
            {
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-terminal me-2"></i>
                            Debug Logs
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="bg-dark text-light p-3 rounded" style="max-height: 300px; overflow-y: auto;">
                            @foreach (var log in debugLogs.TakeLast(50))
                            {
                                <div class="mb-1">
                                    <small class="text-muted">[@log.Timestamp.ToString("HH:mm:ss")]</small>
                                    <span>@log.Message</span>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@code {
    private List<ApplicationUser> users = new();
    private List<AuthTestResult> testResults = new();
    private List<DebugLog> debugLogs = new();
    private string message = string.Empty;
    private bool isError = false;
    private bool isLoading = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadUserData();
    }

    private async Task LoadUserData()
    {
        isLoading = true;
        try
        {
            AddLog("Loading user data from database...");
            users = await UserManager.Users.OrderBy(u => u.EmployeeId).ToListAsync();
            AddLog($"Loaded {users.Count} users");
            
            foreach (var user in users)
            {
                AddLog($"User {user.EmployeeId}: IsActive={user.IsActive}, IsDeleted={user.IsDeleted}, " +
                       $"LockoutEnd={user.LockoutEnd}, AccessFailedCount={user.AccessFailedCount}, " +
                       $"EmailConfirmed={user.EmailConfirmed}");
            }
        }
        catch (Exception ex)
        {
            AddLog($"Error loading users: {ex.Message}");
            ShowMessage($"Error loading user data: {ex.Message}", true);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task TestAllUsers()
    {
        isLoading = true;
        testResults.Clear();
        
        try
        {
            AddLog("Starting authentication test for all users...");
            
            var testCases = new[]
            {
                ("EMP001", "Password123!"),
                ("EMP002", "Password123!"),
                ("EMP003", "Password123!"),
                ("EMP004", "Password123!"),
                ("EMP005", "Password123!"),
                ("EMP006", "Password123!"),
                ("EMP007", "Fa35108981"),
                ("EMP007", "Password123!") // Also test if EMP007 was reset
            };

            foreach (var (employeeId, password) in testCases)
            {
                await TestUserLogin(employeeId, password);
                await Task.Delay(100); // Small delay between tests
                StateHasChanged();
            }
            
            AddLog("Authentication test completed for all users");
            
            var successCount = testResults.Count(r => r.Success);
            var totalCount = testResults.Count;
            ShowMessage($"Authentication test completed: {successCount}/{totalCount} successful", successCount == totalCount);
        }
        catch (Exception ex)
        {
            AddLog($"Error during authentication test: {ex.Message}");
            ShowMessage($"Error during authentication test: {ex.Message}", true);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task TestUserLogin(string employeeId, string password = "Password123!")
    {
        try
        {
            AddLog($"Testing login for {employeeId} with password '{password}'...");
            
            var result = await AuthService.SignInWithEmployeeIdAsync(employeeId, password);
            
            var testResult = new AuthTestResult
            {
                EmployeeId = employeeId,
                Password = password,
                Success = result.Succeeded,
                Details = GetSignInResultDetails(result),
                Timestamp = DateTime.Now
            };
            
            testResults.Add(testResult);
            
            AddLog($"Login test for {employeeId}: {(result.Succeeded ? "SUCCESS" : "FAILED")} - {testResult.Details}");
            
            if (result.Succeeded)
            {
                // Sign out immediately after successful test
                await AuthService.SignOutAsync();
                AddLog($"Signed out {employeeId} after successful test");
            }
        }
        catch (Exception ex)
        {
            var testResult = new AuthTestResult
            {
                EmployeeId = employeeId,
                Password = password,
                Success = false,
                Details = $"Exception: {ex.Message}",
                Timestamp = DateTime.Now
            };
            
            testResults.Add(testResult);
            AddLog($"Login test for {employeeId} failed with exception: {ex.Message}");
        }
        
        StateHasChanged();
    }

    private string GetSignInResultDetails(Microsoft.AspNetCore.Identity.SignInResult result)
    {
        if (result.Succeeded) return "Login successful";
        if (result.IsLockedOut) return "Account is locked out";
        if (result.IsNotAllowed) return "Sign in not allowed";
        if (result.RequiresTwoFactor) return "Requires two-factor authentication";
        return "Invalid credentials or user not found";
    }

    private string GetUserRowClass(ApplicationUser user)
    {
        if (!user.IsActive || user.IsDeleted) return "table-danger";
        if (user.LockoutEnd.HasValue && user.LockoutEnd > DateTimeOffset.UtcNow) return "table-warning";
        return "";
    }

    private void AddLog(string logMessage)
    {
        debugLogs.Add(new DebugLog { Message = logMessage, Timestamp = DateTime.Now });
        StateHasChanged();
    }

    private void ShowMessage(string msg, bool error)
    {
        message = msg;
        isError = error;
        StateHasChanged();
    }

    private void ClearMessage()
    {
        message = string.Empty;
        StateHasChanged();
    }

    private class AuthTestResult
    {
        public string EmployeeId { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string Details { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }

    private class DebugLog
    {
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }
}
