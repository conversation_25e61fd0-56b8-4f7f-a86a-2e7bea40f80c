{"Version": 1, "Hash": "YUvimfqw55rPqMCqfAfHmdtK6gZAN1vRUuJcnaKeUmI=", "Source": "EmployeeRatingSystem.Blazor", "BasePath": "_content/EmployeeRatingSystem.Blazor", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "EmployeeRatingSystem.Blazor\\wwwroot", "Source": "EmployeeRatingSystem.Blazor", "ContentRoot": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\", "BasePath": "_content/EmployeeRatingSystem.Blazor", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.quickgrid\\10.0.0-preview.6.25358.103\\staticwebassets\\Microsoft.AspNetCore.Components.QuickGrid.25o87uqmvr.bundle.scp.css", "SourceId": "Microsoft.AspNetCore.Components.QuickGrid", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.quickgrid\\10.0.0-preview.6.25358.103\\staticwebassets\\", "BasePath": "_content/Microsoft.AspNetCore.Components.QuickGrid", "RelativePath": "Microsoft.AspNetCore.Components.QuickGrid.25o87uqmvr.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "25o87uqmvr", "Integrity": "Nuu0BHvGBlwN6c5FSVjV9yxX0YPfwg3ufdOucRJCOz0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.quickgrid\\10.0.0-preview.6.25358.103\\staticwebassets\\Microsoft.AspNetCore.Components.QuickGrid.25o87uqmvr.bundle.scp.css", "FileLength": 8251, "LastWriteTime": "2025-07-08T16:22:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.quickgrid\\10.0.0-preview.6.25358.103\\staticwebassets\\QuickGrid.razor.js", "SourceId": "Microsoft.AspNetCore.Components.QuickGrid", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.quickgrid\\10.0.0-preview.6.25358.103\\staticwebassets\\", "BasePath": "_content/Microsoft.AspNetCore.Components.QuickGrid", "RelativePath": "QuickGrid.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "eu7w3d5g1a", "Integrity": "tnamWuQ2F5I2J8hbtBt4/9i8Mb2y2o+9Tepj5tUrSdU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.quickgrid\\10.0.0-preview.6.25358.103\\staticwebassets\\QuickGrid.razor.js", "FileLength": 2563, "LastWriteTime": "2025-07-08T14:22:06+00:00"}, {"Identity": "C:\\Users\\<USER>\\rating\\blazor_app\\obj\\Debug\\net8.0\\scopedcss\\bundle\\EmployeeRatingSystem.Blazor.styles.css", "SourceId": "EmployeeRatingSystem.Blazor", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\rating\\blazor_app\\obj\\Debug\\net8.0\\scopedcss\\bundle\\", "BasePath": "_content/EmployeeRatingSystem.Blazor", "RelativePath": "EmployeeRatingSystem.Blazor#[.{fingerprint}]?.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "Fingerprint": "m7u9u9ow3j", "Integrity": "MiMYVKzOXD4Og2txVQaud7iSs/JzNO4xm/BL2PzWX0o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\rating\\blazor_app\\obj\\Debug\\net8.0\\scopedcss\\bundle\\EmployeeRatingSystem.Blazor.styles.css", "FileLength": 873, "LastWriteTime": "2025-08-12T05:43:01+00:00"}, {"Identity": "C:\\Users\\<USER>\\rating\\blazor_app\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\EmployeeRatingSystem.Blazor.bundle.scp.css", "SourceId": "EmployeeRatingSystem.Blazor", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\rating\\blazor_app\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\", "BasePath": "_content/EmployeeRatingSystem.Blazor", "RelativePath": "EmployeeRatingSystem.Blazor#[.{fingerprint}]!.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "55fgo8lr6g", "Integrity": "Vwdlfj0hZVeAPWpbprQfN58/rlNBKFut81sJO184KSs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\rating\\blazor_app\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\EmployeeRatingSystem.Blazor.bundle.scp.css", "FileLength": 740, "LastWriteTime": "2025-08-12T05:43:01+00:00"}, {"Identity": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\css\\app.css", "SourceId": "EmployeeRatingSystem.Blazor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\", "BasePath": "_content/EmployeeRatingSystem.Blazor", "RelativePath": "css/app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "auizp3iu4a", "Integrity": "3nCv17KCqkvB25yRpYQq5JRRpw7pmlxMsy7wSzV0TkE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\app.css", "FileLength": 86328, "LastWriteTime": "2025-08-19T04:44:35+00:00"}, {"Identity": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\fix-auth.html", "SourceId": "EmployeeRatingSystem.Blazor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\", "BasePath": "_content/EmployeeRatingSystem.Blazor", "RelativePath": "fix-auth#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2dtiwq59h3", "Integrity": "2kIx0eewfSC3U1XtJCDubzlUo8P13rsd71MWq4BqjOc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\fix-auth.html", "FileLength": 9243, "LastWriteTime": "2025-07-31T13:20:49+00:00"}, {"Identity": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\js\\charts.js", "SourceId": "EmployeeRatingSystem.Blazor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\", "BasePath": "_content/EmployeeRatingSystem.Blazor", "RelativePath": "js/charts#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "su7ha9zexa", "Integrity": "BADcrQvP4PEQYk4Nla49BYk143H0fn0TXL79xPgCy3E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\charts.js", "FileLength": 26640, "LastWriteTime": "2025-08-04T08:57:07+00:00"}, {"Identity": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\uploads\\employee-of-month\\1577caac-a3d8-487d-9065-637c41e26c1c.pdf", "SourceId": "EmployeeRatingSystem.Blazor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\", "BasePath": "_content/EmployeeRatingSystem.Blazor", "RelativePath": "uploads/employee-of-month/1577caac-a3d8-487d-9065-637c41e26c1c#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "k4b9mllmbd", "Integrity": "bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\employee-of-month\\1577caac-a3d8-487d-9065-637c41e26c1c.pdf", "FileLength": 394795, "LastWriteTime": "2025-08-17T08:42:47+00:00"}, {"Identity": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\uploads\\employee-of-month\\48b23ee5-d08d-4953-9cbf-94308891729a.pdf", "SourceId": "EmployeeRatingSystem.Blazor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\", "BasePath": "_content/EmployeeRatingSystem.Blazor", "RelativePath": "uploads/employee-of-month/48b23ee5-d08d-4953-9cbf-94308891729a#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "k4b9mllmbd", "Integrity": "bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\employee-of-month\\48b23ee5-d08d-4953-9cbf-94308891729a.pdf", "FileLength": 394795, "LastWriteTime": "2025-08-17T08:25:26+00:00"}, {"Identity": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\uploads\\employee-of-month\\61a1e616-a4a7-4d01-85fe-919706bfe6fa.pdf", "SourceId": "EmployeeRatingSystem.Blazor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\", "BasePath": "_content/EmployeeRatingSystem.Blazor", "RelativePath": "uploads/employee-of-month/61a1e616-a4a7-4d01-85fe-919706bfe6fa#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "k4b9mllmbd", "Integrity": "bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\employee-of-month\\61a1e616-a4a7-4d01-85fe-919706bfe6fa.pdf", "FileLength": 394795, "LastWriteTime": "2025-08-12T08:04:38+00:00"}, {"Identity": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\uploads\\employee-of-month\\da84b545-8ad8-45a3-94aa-00a37032554f.pdf", "SourceId": "EmployeeRatingSystem.Blazor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\", "BasePath": "_content/EmployeeRatingSystem.Blazor", "RelativePath": "uploads/employee-of-month/da84b545-8ad8-45a3-94aa-00a37032554f#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "k4b9mllmbd", "Integrity": "bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\employee-of-month\\da84b545-8ad8-45a3-94aa-00a37032554f.pdf", "FileLength": 394795, "LastWriteTime": "2025-08-13T07:43:05+00:00"}], "Endpoints": [{"Route": "_content/Microsoft.AspNetCore.Components.QuickGrid/Microsoft.AspNetCore.Components.QuickGrid.25o87uqmvr.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.quickgrid\\10.0.0-preview.6.25358.103\\staticwebassets\\Microsoft.AspNetCore.Components.QuickGrid.25o87uqmvr.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8251"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Nuu0BHvGBlwN6c5FSVjV9yxX0YPfwg3ufdOucRJCOz0=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 16:22:14 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "25o87uqmvr"}, {"Name": "integrity", "Value": "sha256-Nuu0BHvGBlwN6c5FSVjV9yxX0YPfwg3ufdOucRJCOz0="}, {"Name": "label", "Value": "_content/Microsoft.AspNetCore.Components.QuickGrid/Microsoft.AspNetCore.Components.QuickGrid.bundle.scp.css"}]}, {"Route": "_content/Microsoft.AspNetCore.Components.QuickGrid/Microsoft.AspNetCore.Components.QuickGrid.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.quickgrid\\10.0.0-preview.6.25358.103\\staticwebassets\\Microsoft.AspNetCore.Components.QuickGrid.25o87uqmvr.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8251"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Nuu0BHvGBlwN6c5FSVjV9yxX0YPfwg3ufdOucRJCOz0=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 16:22:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nuu0BHvGBlwN6c5FSVjV9yxX0YPfwg3ufdOucRJCOz0="}]}, {"Route": "_content/Microsoft.AspNetCore.Components.QuickGrid/QuickGrid.eu7w3d5g1a.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.quickgrid\\10.0.0-preview.6.25358.103\\staticwebassets\\QuickGrid.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2563"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tnamWuQ2F5I2J8hbtBt4/9i8Mb2y2o+9Tepj5tUrSdU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 08 Jul 2025 14:22:06 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eu7w3d5g1a"}, {"Name": "integrity", "Value": "sha256-tnamWuQ2F5I2J8hbtBt4/9i8Mb2y2o+9Tepj5tUrSdU="}, {"Name": "label", "Value": "_content/Microsoft.AspNetCore.Components.QuickGrid/QuickGrid.razor.js"}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Microsoft.AspNetCore.Components.QuickGrid/QuickGrid.razor.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.quickgrid\\10.0.0-preview.6.25358.103\\staticwebassets\\QuickGrid.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2563"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tnamWuQ2F5I2J8hbtBt4/9i8Mb2y2o+9Tepj5tUrSdU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 08 Jul 2025 14:22:06 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tnamWuQ2F5I2J8hbtBt4/9i8Mb2y2o+9Tepj5tUrSdU="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "css/app.auizp3iu4a.css", "AssetFile": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\css\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86328"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3nCv17KCqkvB25yRpYQq5JRRpw7pmlxMsy7wSzV0TkE=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 19 Aug 2025 04:44:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "auizp3iu4a"}, {"Name": "label", "Value": "css/app.css"}, {"Name": "integrity", "Value": "sha256-3nCv17KCqkvB25yRpYQq5JRRpw7pmlxMsy7wSzV0TkE="}]}, {"Route": "css/app.css", "AssetFile": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\css\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86328"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3nCv17KCqkvB25yRpYQq5JRRpw7pmlxMsy7wSzV0TkE=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 19 Aug 2025 04:44:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3nCv17KCqkvB25yRpYQq5JRRpw7pmlxMsy7wSzV0TkE="}]}, {"Route": "EmployeeRatingSystem.Blazor.55fgo8lr6g.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\rating\\blazor_app\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\EmployeeRatingSystem.Blazor.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "740"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Vwdlfj0hZVeAPWpbprQfN58/rlNBKFut81sJO184KSs=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 12 Aug 2025 05:43:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "55fgo8lr6g"}, {"Name": "label", "Value": "EmployeeRatingSystem.Blazor.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-Vwdlfj0hZVeAPWpbprQfN58/rlNBKFut81sJO184KSs="}]}, {"Route": "EmployeeRatingSystem.Blazor.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\rating\\blazor_app\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\EmployeeRatingSystem.Blazor.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "740"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Vwdlfj0hZVeAPWpbprQfN58/rlNBKFut81sJO184KSs=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 12 Aug 2025 05:43:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Vwdlfj0hZVeAPWpbprQfN58/rlNBKFut81sJO184KSs="}]}, {"Route": "EmployeeRatingSystem.Blazor.m7u9u9ow3j.styles.css", "AssetFile": "C:\\Users\\<USER>\\rating\\blazor_app\\obj\\Debug\\net8.0\\scopedcss\\bundle\\EmployeeRatingSystem.Blazor.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "873"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"MiMYVKzOXD4Og2txVQaud7iSs/JzNO4xm/BL2PzWX0o=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 12 Aug 2025 05:43:01 GMT"}, {"Name": "Link", "Value": "<_content/Microsoft.AspNetCore.Components.QuickGrid/Microsoft.AspNetCore.Components.QuickGrid.25o87uqmvr.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m7u9u9ow3j"}, {"Name": "integrity", "Value": "sha256-MiMYVKzOXD4Og2txVQaud7iSs/JzNO4xm/BL2PzWX0o="}, {"Name": "label", "Value": "EmployeeRatingSystem.Blazor.styles.css"}]}, {"Route": "EmployeeRatingSystem.Blazor.styles.css", "AssetFile": "C:\\Users\\<USER>\\rating\\blazor_app\\obj\\Debug\\net8.0\\scopedcss\\bundle\\EmployeeRatingSystem.Blazor.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "873"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"MiMYVKzOXD4Og2txVQaud7iSs/JzNO4xm/BL2PzWX0o=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 12 Aug 2025 05:43:01 GMT"}, {"Name": "Link", "Value": "<_content/Microsoft.AspNetCore.Components.QuickGrid/Microsoft.AspNetCore.Components.QuickGrid.25o87uqmvr.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MiMYVKzOXD4Og2txVQaud7iSs/JzNO4xm/BL2PzWX0o="}]}, {"Route": "fix-auth.2dtiwq59h3.html", "AssetFile": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\fix-auth.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9243"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"2kIx0eewfSC3U1XtJCDubzlUo8P13rsd71MWq4BqjOc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 13:20:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2dtiwq59h3"}, {"Name": "label", "Value": "fix-auth.html"}, {"Name": "integrity", "Value": "sha256-2kIx0eewfSC3U1XtJCDubzlUo8P13rsd71MWq4BqjOc="}]}, {"Route": "fix-auth.html", "AssetFile": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\fix-auth.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9243"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"2kIx0eewfSC3U1XtJCDubzlUo8P13rsd71MWq4BqjOc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 13:20:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2kIx0eewfSC3U1XtJCDubzlUo8P13rsd71MWq4BqjOc="}]}, {"Route": "js/charts.js", "AssetFile": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\js\\charts.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26640"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BADcrQvP4PEQYk4Nla49BYk143H0fn0TXL79xPgCy3E=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 08:57:07 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BADcrQvP4PEQYk4Nla49BYk143H0fn0TXL79xPgCy3E="}]}, {"Route": "js/charts.su7ha9zexa.js", "AssetFile": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\js\\charts.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26640"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BADcrQvP4PEQYk4Nla49BYk143H0fn0TXL79xPgCy3E=\""}, {"Name": "Last-Modified", "Value": "Mon, 04 Aug 2025 08:57:07 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "su7ha9zexa"}, {"Name": "label", "Value": "js/charts.js"}, {"Name": "integrity", "Value": "sha256-BADcrQvP4PEQYk4Nla49BYk143H0fn0TXL79xPgCy3E="}]}, {"Route": "uploads/employee-of-month/1577caac-a3d8-487d-9065-637c41e26c1c.k4b9mllmbd.pdf", "AssetFile": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\uploads\\employee-of-month\\1577caac-a3d8-487d-9065-637c41e26c1c.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "394795"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM=\""}, {"Name": "Last-Modified", "Value": "Sun, 17 Aug 2025 08:42:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k4b9mllmbd"}, {"Name": "label", "Value": "uploads/employee-of-month/1577caac-a3d8-487d-9065-637c41e26c1c.pdf"}, {"Name": "integrity", "Value": "sha256-bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM="}]}, {"Route": "uploads/employee-of-month/1577caac-a3d8-487d-9065-637c41e26c1c.pdf", "AssetFile": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\uploads\\employee-of-month\\1577caac-a3d8-487d-9065-637c41e26c1c.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "394795"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM=\""}, {"Name": "Last-Modified", "Value": "Sun, 17 Aug 2025 08:42:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM="}]}, {"Route": "uploads/employee-of-month/48b23ee5-d08d-4953-9cbf-94308891729a.k4b9mllmbd.pdf", "AssetFile": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\uploads\\employee-of-month\\48b23ee5-d08d-4953-9cbf-94308891729a.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "394795"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM=\""}, {"Name": "Last-Modified", "Value": "Sun, 17 Aug 2025 08:25:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k4b9mllmbd"}, {"Name": "label", "Value": "uploads/employee-of-month/48b23ee5-d08d-4953-9cbf-94308891729a.pdf"}, {"Name": "integrity", "Value": "sha256-bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM="}]}, {"Route": "uploads/employee-of-month/48b23ee5-d08d-4953-9cbf-94308891729a.pdf", "AssetFile": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\uploads\\employee-of-month\\48b23ee5-d08d-4953-9cbf-94308891729a.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "394795"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM=\""}, {"Name": "Last-Modified", "Value": "Sun, 17 Aug 2025 08:25:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM="}]}, {"Route": "uploads/employee-of-month/61a1e616-a4a7-4d01-85fe-919706bfe6fa.k4b9mllmbd.pdf", "AssetFile": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\uploads\\employee-of-month\\61a1e616-a4a7-4d01-85fe-919706bfe6fa.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "394795"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 12 Aug 2025 08:04:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k4b9mllmbd"}, {"Name": "label", "Value": "uploads/employee-of-month/61a1e616-a4a7-4d01-85fe-919706bfe6fa.pdf"}, {"Name": "integrity", "Value": "sha256-bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM="}]}, {"Route": "uploads/employee-of-month/61a1e616-a4a7-4d01-85fe-919706bfe6fa.pdf", "AssetFile": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\uploads\\employee-of-month\\61a1e616-a4a7-4d01-85fe-919706bfe6fa.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "394795"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 12 Aug 2025 08:04:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM="}]}, {"Route": "uploads/employee-of-month/da84b545-8ad8-45a3-94aa-00a37032554f.k4b9mllmbd.pdf", "AssetFile": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\uploads\\employee-of-month\\da84b545-8ad8-45a3-94aa-00a37032554f.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "394795"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM=\""}, {"Name": "Last-Modified", "Value": "Wed, 13 Aug 2025 07:43:05 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k4b9mllmbd"}, {"Name": "label", "Value": "uploads/employee-of-month/da84b545-8ad8-45a3-94aa-00a37032554f.pdf"}, {"Name": "integrity", "Value": "sha256-bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM="}]}, {"Route": "uploads/employee-of-month/da84b545-8ad8-45a3-94aa-00a37032554f.pdf", "AssetFile": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\uploads\\employee-of-month\\da84b545-8ad8-45a3-94aa-00a37032554f.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "394795"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM=\""}, {"Name": "Last-Modified", "Value": "Wed, 13 Aug 2025 07:43:05 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bEVBswavisqwH+J5XimkMDHn2NG0gWdRbqWRv4KcCZM="}]}]}