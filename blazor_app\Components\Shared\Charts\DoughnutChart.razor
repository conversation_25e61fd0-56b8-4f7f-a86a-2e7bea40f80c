@inherits BaseChart
@inject IJSRuntime JSRuntime

@if (IsLoading)
{
    <div class="text-center py-4">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">@LoadingMessage</span>
        </div>
        <p class="mt-2 text-muted">@LoadingMessage</p>
    </div>
}
else if (Data?.Any() != true)
{
    <div class="text-center py-4 text-muted">
        <i class="fas fa-chart-doughnut fa-2x mb-2"></i>
        <p>@L("No data available", "لا توجد بيانات متاحة")</p>
    </div>
}
else
{
    <div class="chart-container @CssClass" style="@Style">
        <canvas id="@CanvasId" width="@Width" height="@Height"></canvas>
        @if (ShowCenterText && !string.IsNullOrEmpty(CenterText))
        {
            <div class="chart-center-text">
                <div class="center-text-value">@CenterText</div>
                @if (!string.IsNullOrEmpty(CenterSubText))
                {
                    <div class="center-text-label">@CenterSubText</div>
                }
            </div>
        }
    </div>
}

<style>
    .chart-container {
        position: relative;
    }

    .chart-center-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        pointer-events: none;
    }

    .center-text-value {
        font-size: 1.5rem;
        font-weight: bold;
        color: #333;
    }

    .center-text-label {
        font-size: 0.875rem;
        color: #666;
        margin-top: 0.25rem;
    }
</style>

@code {
    /// <summary>
    /// Chart data labels
    /// </summary>
    [Parameter] public string[]? Labels { get; set; }

    /// <summary>
    /// Chart data values
    /// </summary>
    [Parameter] public int[]? Data { get; set; }

    /// <summary>
    /// Chart colors
    /// </summary>
    [Parameter] public string[]? Colors { get; set; }

    /// <summary>
    /// Cutout percentage (0-100)
    /// </summary>
    [Parameter] public int Cutout { get; set; } = 60;

    /// <summary>
    /// Whether to show center text
    /// </summary>
    [Parameter] public bool ShowCenterText { get; set; } = false;

    /// <summary>
    /// Center text (main value)
    /// </summary>
    [Parameter] public string CenterText { get; set; } = "";

    /// <summary>
    /// Center sub text (label)
    /// </summary>
    [Parameter] public string CenterSubText { get; set; } = "";

    /// <summary>
    /// Default colors if none provided - Corporate Professional Palette
    /// </summary>
    private readonly string[] DefaultColors = new[]
    {
        "#1e3a8a", "#374151", "#065f46", "#7c2d12", "#6b7280",
        "#1e40af", "#4b5563", "#047857", "#92400e", "#9ca3af"
    };

    protected override async Task InitializeChart()
    {
        if (Data?.Any() == true && Labels?.Any() == true)
        {
            await CreateChart();
        }
    }

    public override async Task UpdateChart()
    {
        if (Data?.Any() == true && Labels?.Any() == true)
        {
            await CreateChart();
        }
    }

    private async Task CreateChart()
    {
        try
        {
            var colors = Colors ?? DefaultColors.Take(Data?.Length ?? 0).ToArray();
            await JSRuntime.InvokeVoidAsync("createDoughnutChart", CanvasId, Labels, Data, colors, Title);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error creating doughnut chart: {ex.Message}");
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        if (Data?.Any() == true && Labels?.Any() == true)
        {
            await UpdateChart();
        }
    }
}
