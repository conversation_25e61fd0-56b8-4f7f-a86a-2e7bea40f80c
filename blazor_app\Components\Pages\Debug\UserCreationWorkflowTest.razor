@page "/debug/user-creation-workflow-test"
@using Microsoft.AspNetCore.Identity
@using Microsoft.EntityFrameworkCore
@using System.ComponentModel.DataAnnotations
@using EmployeeRatingSystem.Blazor.Data
@using EmployeeRatingSystem.Blazor.Models
@using Microsoft.AspNetCore.Components.Authorization
@inject ApplicationDbContext DbContext
@inject UserManager<ApplicationUser> UserManager
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider

<PageTitle>User Creation Workflow Test</PageTitle>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-vial me-2"></i>
                        User Creation Workflow Test
                    </h4>
                </div>
                <div class="card-body">
                    
                    <!-- Current User Info -->
                    <div class="alert alert-info">
                        <h5><i class="fas fa-user me-2"></i>Current User Information</h5>
                        @if (currentUser != null)
                        {
                            <p><strong>Name:</strong> @currentUser.EnglishName (@currentUser.ArabicName)</p>
                            <p><strong>Email:</strong> @currentUser.Email</p>
                            <p><strong>Employee ID:</strong> @currentUser.EmployeeId</p>
                            <p><strong>Role:</strong> @GetRoleDisplayName(currentUser.Role)</p>
                            <p><strong>Can Create Users:</strong> 
                                @if (CanCreateUsers())
                                {
                                    <span class="badge bg-success">✓ Yes</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger">✗ No</span>
                                }
                            </p>
                        }
                        else
                        {
                            <p class="text-warning">No current user found or not authenticated</p>
                        }
                    </div>

                    <!-- Authorization Test -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-shield-alt me-2"></i>Authorization Test</h5>
                        </div>
                        <div class="card-body">
                            <p>Testing role-based user creation permissions:</p>
                            <ul>
                                <li><strong>Super Admin:</strong> <span class="badge bg-success">✓ Can create users</span></li>
                                <li><strong>Excellence Team:</strong> <span class="badge bg-success">✓ Can create users</span></li>
                                <li><strong>Manager:</strong> <span class="badge bg-danger">✗ Cannot create users</span></li>
                                <li><strong>Direct Supervisor:</strong> <span class="badge bg-danger">✗ Cannot create users</span></li>
                                <li><strong>Employee:</strong> <span class="badge bg-danger">✗ Cannot create users</span></li>
                            </ul>
                            
                            @if (currentUser != null)
                            {
                                <div class="mt-3">
                                    <strong>Your current role (@GetRoleDisplayName(currentUser.Role)) permissions:</strong>
                                    @if (CanCreateUsers())
                                    {
                                        <span class="badge bg-success ms-2">✓ You can create users</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-danger ms-2">✗ You cannot create users</span>
                                    }
                                </div>
                            }
                        </div>
                    </div>

                    <!-- Test User Creation Form -->
                    @if (CanCreateUsers())
                    {
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-plus me-2"></i>Test User Creation</h5>
                            </div>
                            <div class="card-body">
                                <EditForm Model="testUser" OnValidSubmit="CreateTestUser">
                                    <DataAnnotationsValidator />
                                    <ValidationSummary class="alert alert-danger" />
                                    
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label class="form-label">Employee ID <span class="text-danger">*</span></label>
                                            <InputText @bind-Value="testUser.EmployeeId" class="form-control" placeholder="TEST001" />
                                            <ValidationMessage For="() => testUser.EmployeeId" />
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Email <span class="text-danger">*</span></label>
                                            <InputText @bind-Value="testUser.Email" class="form-control" type="email" placeholder="<EMAIL>" />
                                            <ValidationMessage For="() => testUser.Email" />
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Name <span class="text-danger">*</span></label>
                                            <InputText @bind-Value="testUser.Name" class="form-control" placeholder="Test User" />
                                            <ValidationMessage For="() => testUser.Name" />
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Arabic Name <span class="text-danger">*</span></label>
                                            <InputText @bind-Value="testUser.ArabicName" class="form-control" placeholder="مستخدم تجريبي" />
                                            <ValidationMessage For="() => testUser.ArabicName" />
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Role <span class="text-danger">*</span></label>
                                            <InputSelect @bind-Value="testUser.Role" class="form-select">
                                                @foreach (var role in Enum.GetValues<UserRole>())
                                                {
                                                    <option value="@role">@GetRoleDisplayName(role)</option>
                                                }
                                            </InputSelect>
                                            <ValidationMessage For="() => testUser.Role" />
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Password</label>
                                            <InputText @bind-Value="testUser.Password" class="form-control" type="password" placeholder="Leave empty for default" />
                                            <small class="text-muted">Leave empty to use default password: Password123!</small>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <button type="submit" class="btn btn-primary" disabled="@isCreating">
                                            @if (isCreating)
                                            {
                                                <span class="spinner-border spinner-border-sm me-2"></span>
                                            }
                                            <i class="fas fa-plus me-2"></i>
                                            Create Test User
                                        </button>
                                        <button type="button" class="btn btn-secondary ms-2" @onclick="GenerateRandomTestUser">
                                            <i class="fas fa-random me-2"></i>
                                            Generate Random Data
                                        </button>
                                    </div>
                                </EditForm>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-warning">
                            <h5><i class="fas fa-exclamation-triangle me-2"></i>Access Denied</h5>
                            <p>You do not have permission to create users. Only Super Admin and Excellence Team members can create users.</p>
                        </div>
                    }

                    <!-- Test Results -->
                    @if (!string.IsNullOrEmpty(testResult))
                    {
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-clipboard-check me-2"></i>Test Results</h5>
                            </div>
                            <div class="card-body">
                                <pre class="bg-light p-3 rounded">@testResult</pre>
                            </div>
                        </div>
                    }

                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private ApplicationUser? currentUser = null;
    private TestUserModel testUser = new();
    private bool isCreating = false;
    private string testResult = "";

    protected override async Task OnInitializedAsync()
    {
        await LoadCurrentUser();
        GenerateRandomTestUser();
    }

    private async Task LoadCurrentUser()
    {
        try
        {
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                var userEmail = authState.User.Identity.Name;
                if (!string.IsNullOrEmpty(userEmail))
                {
                    currentUser = await UserManager.FindByEmailAsync(userEmail);
                }
            }
        }
        catch (Exception ex)
        {
            testResult += $"Error loading current user: {ex.Message}\n";
        }
    }

    private bool CanCreateUsers()
    {
        return currentUser?.Role == UserRole.SUPER_ADMIN || 
               currentUser?.Role == UserRole.EXCELLENCE_TEAM;
    }

    private string GetRoleDisplayName(UserRole role)
    {
        return role switch
        {
            UserRole.SUPER_ADMIN => "Super Admin (سوبر أدمين)",
            UserRole.MANAGER => "Manager (المدير)",
            UserRole.SUPERVISOR => "Direct Supervisor (المسؤول المباشر)",
            UserRole.EXCELLENCE_TEAM => "Excellence Team (فريق التميز)",
            UserRole.EMPLOYEE => "Employee (موظف)",
            _ => role.ToString()
        };
    }

    private void GenerateRandomTestUser()
    {
        var random = new Random();
        var timestamp = DateTime.Now.ToString("MMddHHmm");
        
        testUser = new TestUserModel
        {
            EmployeeId = $"TEST{timestamp}",
            Email = $"test{timestamp}@example.com",
            Name = $"Test User {timestamp}",
            ArabicName = $"مستخدم تجريبي {timestamp}",
            Role = UserRole.EMPLOYEE,
            Password = ""
        };
    }

    private async Task CreateTestUser()
    {
        if (!CanCreateUsers())
        {
            testResult = "ERROR: Access denied. You do not have permission to create users.";
            return;
        }

        isCreating = true;
        testResult = "";
        StateHasChanged();

        try
        {
            testResult += $"Starting user creation test...\n";
            testResult += $"Employee ID: {testUser.EmployeeId}\n";
            testResult += $"Email: {testUser.Email}\n";
            testResult += $"Role: {GetRoleDisplayName(testUser.Role)}\n\n";

            // Check for duplicates
            var existingUserByEmail = await UserManager.FindByEmailAsync(testUser.Email);
            if (existingUserByEmail != null)
            {
                testResult += "ERROR: A user with this email already exists.\n";
                return;
            }

            var existingUserByEmployeeId = await DbContext.Users
                .FirstOrDefaultAsync(u => u.EmployeeId == testUser.EmployeeId && !u.IsDeleted);
            if (existingUserByEmployeeId != null)
            {
                testResult += "ERROR: A user with this Employee ID already exists.\n";
                return;
            }

            // Create new user
            var newUser = new ApplicationUser
            {
                EmployeeId = testUser.EmployeeId,
                Email = testUser.Email,
                UserName = testUser.Email,
                EnglishName = testUser.Name,
                ArabicName = testUser.ArabicName,
                Role = testUser.Role,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            var password = !string.IsNullOrEmpty(testUser.Password) ? testUser.Password : "Password123!";
            var result = await UserManager.CreateAsync(newUser, password);

            if (result.Succeeded)
            {
                testResult += "SUCCESS: User created successfully!\n";
                testResult += $"Generated Password: {password}\n";
                testResult += $"User ID: {newUser.Id}\n";
                testResult += $"Created At: {newUser.CreatedAt}\n\n";
                testResult += "The user should now appear in the Users page and be able to login.\n";
                
                // Generate new test data for next test
                GenerateRandomTestUser();
            }
            else
            {
                testResult += "ERROR: User creation failed.\n";
                testResult += "Errors:\n";
                foreach (var error in result.Errors)
                {
                    testResult += $"- {error.Description}\n";
                }
            }
        }
        catch (Exception ex)
        {
            testResult += $"EXCEPTION: {ex.Message}\n";
            testResult += $"Stack Trace: {ex.StackTrace}\n";
        }
        finally
        {
            isCreating = false;
            StateHasChanged();
        }
    }

    public class TestUserModel
    {
        [Required(ErrorMessage = "Employee ID is required")]
        [StringLength(50, ErrorMessage = "Employee ID cannot exceed 50 characters")]
        public string EmployeeId { get; set; } = "";

        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Invalid email format")]
        [StringLength(255, ErrorMessage = "Email cannot exceed 255 characters")]
        public string Email { get; set; } = "";

        [Required(ErrorMessage = "Name is required")]
        [StringLength(255, ErrorMessage = "Name cannot exceed 255 characters")]
        public string Name { get; set; } = "";

        [Required(ErrorMessage = "Arabic name is required")]
        [StringLength(255, ErrorMessage = "Arabic name cannot exceed 255 characters")]
        public string ArabicName { get; set; } = "";

        [Required(ErrorMessage = "Role is required")]
        public UserRole Role { get; set; } = UserRole.EMPLOYEE;

        public string Password { get; set; } = "";
    }
}
