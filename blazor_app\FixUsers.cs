using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using EmployeeRatingSystem.Blazor.Data;
using EmployeeRatingSystem.Blazor.Models;

namespace EmployeeRatingSystem.Blazor
{
    public class FixUsers
    {
        public static async Task FixAllUsersAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<ApplicationUser>>();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

            Console.WriteLine("Starting user fix process...");

            // Get all users
            var users = await userManager.Users.ToListAsync();
            
            Console.WriteLine($"Found {users.Count} users");

            foreach (var user in users)
            {
                Console.WriteLine($"Checking user: {user.EmployeeId} - {user.EnglishName}");
                Console.WriteLine($"  IsActive: {user.IsActive}");
                Console.WriteLine($"  IsDeleted: {user.IsDeleted}");
                Console.WriteLine($"  LockoutEnd: {user.LockoutEnd}");
                Console.WriteLine($"  AccessFailedCount: {user.AccessFailedCount}");

                bool needsUpdate = false;

                // Activate user if inactive
                if (!user.IsActive)
                {
                    user.IsActive = true;
                    needsUpdate = true;
                    Console.WriteLine($"  -> Activating user");
                }

                // Undelete user if deleted
                if (user.IsDeleted)
                {
                    user.IsDeleted = false;
                    needsUpdate = true;
                    Console.WriteLine($"  -> Undeleting user");
                }

                // Reset lockout
                if (user.LockoutEnd.HasValue && user.LockoutEnd > DateTimeOffset.UtcNow)
                {
                    await userManager.SetLockoutEndDateAsync(user, null);
                    Console.WriteLine($"  -> Removing lockout");
                }

                // Reset failed attempts
                if (user.AccessFailedCount > 0)
                {
                    await userManager.ResetAccessFailedCountAsync(user);
                    Console.WriteLine($"  -> Resetting failed attempts");
                }

                // Reset password to default
                var token = await userManager.GeneratePasswordResetTokenAsync(user);
                var passwordResult = await userManager.ResetPasswordAsync(user, token, "Password123!");
                
                if (passwordResult.Succeeded)
                {
                    Console.WriteLine($"  -> Password reset to 'Password123!'");
                }
                else
                {
                    Console.WriteLine($"  -> Password reset failed: {string.Join(", ", passwordResult.Errors.Select(e => e.Description))}");
                }

                // Update user if needed
                if (needsUpdate)
                {
                    var updateResult = await userManager.UpdateAsync(user);
                    if (updateResult.Succeeded)
                    {
                        Console.WriteLine($"  -> User updated successfully");
                    }
                    else
                    {
                        Console.WriteLine($"  -> User update failed: {string.Join(", ", updateResult.Errors.Select(e => e.Description))}");
                    }
                }

                Console.WriteLine($"  -> User {user.EmployeeId} processed");
                Console.WriteLine();
            }

            Console.WriteLine("User fix process completed!");
        }
    }
}
