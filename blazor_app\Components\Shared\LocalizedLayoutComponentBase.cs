using Microsoft.AspNetCore.Components;
using EmployeeRatingSystem.Blazor.Services;

namespace EmployeeRatingSystem.Blazor.Components.Shared
{
    /// <summary>
    /// Base class for layout components that need localization support
    /// </summary>
    public class LocalizedLayoutComponentBase : LayoutComponentBase
    {
        [Inject] protected ILocalizationService LocalizationService { get; set; } = default!;

        /// <summary>
        /// Gets whether the current language is Arabic
        /// </summary>
        protected bool IsArabic => LocalizationService.CurrentLanguage == "ar";

        /// <summary>
        /// Gets whether the current layout is RTL
        /// </summary>
        protected bool IsRTL => IsArabic;

        /// <summary>
        /// Localizes a string based on the current language
        /// </summary>
        protected string L(string english, string arabic = "")
        {
            return LocalizationService.GetLocalizedString(english, arabic);
        }

        /// <summary>
        /// Gets the appropriate CSS class for layout direction
        /// </summary>
        protected string GetLayoutClass()
        {
            return IsRTL ? "rtl-layout" : "";
        }

        /// <summary>
        /// Gets the appropriate CSS class for body
        /// </summary>
        protected string GetBodyClass()
        {
            return IsRTL ? "rtl-layout" : "";
        }

        /// <summary>
        /// Gets margin-end class based on layout direction
        /// </summary>
        protected string GetMarginEnd(int size = 2)
        {
            var sizeClass = size switch
            {
                1 => "1",
                2 => "2",
                3 => "3",
                4 => "4",
                5 => "5",
                _ => "2"
            };
            return IsRTL ? $"ms-{sizeClass}" : $"me-{sizeClass}";
        }

        /// <summary>
        /// Gets margin-start class based on layout direction
        /// </summary>
        protected string GetMarginStart(int size = 2)
        {
            var sizeClass = size switch
            {
                1 => "1",
                2 => "2",
                3 => "3",
                4 => "4",
                5 => "5",
                _ => "2"
            };
            return IsRTL ? $"me-{sizeClass}" : $"ms-{sizeClass}";
        }

        /// <summary>
        /// Gets the page title with proper localization
        /// </summary>
        protected string GetPageTitle(string english, string arabic = "")
        {
            var title = L(english, arabic);
            var appName = L("Employee Rating System", "نظام تقييم الموظفين");
            return $"{title} - {appName}";
        }
    }
}
