@using EmployeeRatingSystem.Blazor.Models
@using EmployeeRatingSystem.Blazor.Services
@inherits LocalizedComponentBase
@inject IEvaluationAttachmentService EvaluationAttachmentService
@inject IJSRuntime JSRuntime

<div class="evaluation-attachments-list">
    @if (isLoading)
    {
        <div class="text-center py-2">
            <div class="spinner-border spinner-border-sm" role="status">
                <span class="visually-hidden">@L("Loading...", "جاري التحميل...")</span>
            </div>
            <small class="text-muted @GetMarginStart(2)">@L("Loading attachments...", "جاري تحميل المرفقات...")</small>
        </div>
    }
    else if (attachments.Any())
    {
        <div class="list-group list-group-flush">
            @foreach (var attachment in attachments)
            {
                <div class="list-group-item d-flex justify-content-between align-items-center p-2">
                    <div class="d-flex align-items-center">
                        <i class="@GetFileIcon(attachment.FileName) @GetMarginEnd(2) text-primary"></i>
                        <div>
                            <div class="fw-bold small">@attachment.FileName</div>
                            <small class="text-muted">
                                @L("Size", "الحجم"): @FormatFileSize(attachment.FileSize) | 
                                @L("Uploaded", "تم الرفع"): @attachment.UploadedAt.ToString("dd/MM/yyyy HH:mm")
                            </small>
                            @if (!string.IsNullOrEmpty(attachment.DescriptionEn) || !string.IsNullOrEmpty(attachment.DescriptionAr))
                            {
                                <div class="small text-muted mt-1">
                                    @(IsArabic ? attachment.DescriptionAr : attachment.DescriptionEn)
                                </div>
                            }
                        </div>
                    </div>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-primary btn-sm" 
                                @onclick="@(() => DownloadAttachment(attachment))"
                                title="@L("Download", "تحميل")">
                            <i class="fas fa-download"></i>
                        </button>
                        @if (CanDeleteAttachment)
                        {
                            <button type="button" class="btn btn-outline-danger btn-sm" 
                                    @onclick="@(() => DeleteAttachment(attachment))"
                                    title="@L("Delete", "حذف")">
                                <i class="fas fa-trash"></i>
                            </button>
                        }
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="text-center py-3 text-muted">
            <i class="fas fa-paperclip fa-2x mb-2"></i>
            <div class="small">@L("No attachments uploaded yet", "لم يتم رفع أي مرفقات بعد")</div>
        </div>
    }
</div>

@code {
    [Parameter] public int? EvaluationId { get; set; }
    [Parameter] public int? SupervisorEvaluationId { get; set; }
    [Parameter] public string AttachmentType { get; set; } = EvaluationAttachmentTypes.ExceptionalWork;
    [Parameter] public bool CanDeleteAttachment { get; set; } = true;
    [Parameter] public EventCallback OnAttachmentDeleted { get; set; }

    private List<EvaluationAttachment> attachments = new();
    private bool isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadAttachments();
    }

    protected override async Task OnParametersSetAsync()
    {
        await LoadAttachments();
    }

    public async Task RefreshAttachments()
    {
        await LoadAttachments();
    }

    private async Task LoadAttachments()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            if (EvaluationId.HasValue)
            {
                attachments = await EvaluationAttachmentService.GetAttachmentsByTypeAsync(EvaluationId.Value, AttachmentType);
            }
            else if (SupervisorEvaluationId.HasValue)
            {
                attachments = await EvaluationAttachmentService.GetSupervisorEvaluationAttachmentsByTypeAsync(SupervisorEvaluationId.Value, AttachmentType);
            }
            else
            {
                attachments = new List<EvaluationAttachment>();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading attachments: {ex.Message}");
            attachments = new List<EvaluationAttachment>();
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task DownloadAttachment(EvaluationAttachment attachment)
    {
        try
        {
            var fileContent = await EvaluationAttachmentService.GetAttachmentContentAsync(attachment.Id);
            var fileName = attachment.FileName;
            var contentType = attachment.ContentType;

            // Convert to base64 for download
            var base64 = Convert.ToBase64String(fileContent);
            var dataUrl = $"data:{contentType};base64,{base64}";

            await JSRuntime.InvokeVoidAsync("downloadFile", dataUrl, fileName);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error downloading attachment: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", L("Error downloading file", "خطأ في تحميل الملف"));
        }
    }

    private async Task DeleteAttachment(EvaluationAttachment attachment)
    {
        try
        {
            var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", 
                L("Are you sure you want to delete this attachment?", "هل أنت متأكد من حذف هذا المرفق؟"));

            if (confirmed)
            {
                var success = await EvaluationAttachmentService.DeleteAttachmentAsync(attachment.Id, "CurrentUser");
                if (success)
                {
                    await LoadAttachments();
                    await OnAttachmentDeleted.InvokeAsync();
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", L("Error deleting attachment", "خطأ في حذف المرفق"));
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error deleting attachment: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", L("Error deleting attachment", "خطأ في حذف المرفق"));
        }
    }

    private string GetFileIcon(string fileName)
    {
        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        return extension switch
        {
            ".pdf" => "fas fa-file-pdf",
            ".doc" or ".docx" => "fas fa-file-word",
            ".xls" or ".xlsx" => "fas fa-file-excel",
            ".jpg" or ".jpeg" or ".png" => "fas fa-file-image",
            ".txt" => "fas fa-file-alt",
            _ => "fas fa-file"
        };
    }

    private string FormatFileSize(long bytes)
    {
        if (bytes < 1024)
            return $"{bytes} B";
        else if (bytes < 1024 * 1024)
            return $"{bytes / 1024.0:F1} KB";
        else
            return $"{bytes / (1024.0 * 1024.0):F1} MB";
    }
}

<script>
    window.downloadFile = function(dataUrl, fileName) {
        const link = document.createElement('a');
        link.href = dataUrl;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };
</script>
