@page "/departments"
@using Microsoft.EntityFrameworkCore
@using EmployeeRatingSystem.Blazor.Components.Shared
@using EmployeeRatingSystem.Blazor.Services
@using EmployeeRatingSystem.Blazor.Models
@using EmployeeRatingSystem.Blazor.Data
@using System.ComponentModel.DataAnnotations

@inherits LocalizedComponentBase
@inject ApplicationDbContext DbContext
@inject IBreadcrumbService BreadcrumbService
@inject IJSRuntime JSRuntime

<PageTitle>@GetPageTitle("Departments", "الأقسام")</PageTitle>

<AuthenticationGuard>
<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/dashboard">@L("Dashboard", "لوحة التحكم")</a></li>
        <li class="breadcrumb-item active" aria-current="page">@L("Departments", "الأقسام")</li>
    </ol>
</nav>

<!-- Page Header -->
<div class="page-header mb-4">
    <div class="d-flex justify-content-between align-items-start flex-wrap">
        <div class="flex-grow-1">
            <h1 class="h1 mb-2">
                <i class="fas fa-sitemap @GetMarginEnd() text-primary"></i>
                @L("Departments", "الأقسام")
            </h1>
            <p class="lead mb-0">@L("Manage organizational departments and hierarchical structure", "إدارة الأقسام التنظيمية والهيكل الهرمي")</p>
        </div>

        <div class="@GetMarginStart() mt-2 mt-md-0">
            <div class="d-flex gap-2 @GetLayoutClass()">
                <button class="btn btn-primary" @onclick="ShowCreateDepartmentModal">
                    <i class="fas fa-plus @GetMarginEnd(1)"></i>
                    @L("Create Department", "إنشاء قسم")
                </button>
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-cog @GetMarginEnd(1)"></i>
                        @L("Actions", "الإجراءات")
                    </button>
                    <ul class="dropdown-menu">
                        <li><button class="dropdown-item" @onclick="ExportDepartments">
                            <i class="fas fa-download @GetMarginEnd(1)"></i>
                            @L("Export", "تصدير")
                        </button></li>
                        <li><button class="dropdown-item" @onclick="RefreshData">
                            <i class="fas fa-sync @GetMarginEnd(1)"></i>
                            @L("Refresh", "تحديث")
                        </button></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stat-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-primary text-white @GetMarginEnd()">
                        <i class="fas fa-sitemap"></i>
                    </div>
                    <div>
                        <h5 class="card-title mb-1">@totalDepartments</h5>
                        <p class="card-text text-muted mb-0">@L("Total Departments", "إجمالي الأقسام")</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stat-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-success text-white @GetMarginEnd()">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div>
                        <h5 class="card-title mb-1">@activeDepartments</h5>
                        <p class="card-text text-muted mb-0">@L("Active Departments", "الأقسام النشطة")</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stat-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-info text-white @GetMarginEnd()">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <div>
                        <h5 class="card-title mb-1">@maxDepth</h5>
                        <p class="card-text text-muted mb-0">@L("Max Depth", "أقصى عمق")</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stat-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-warning text-white @GetMarginEnd()">
                        <i class="fas fa-users"></i>
                    </div>
                    <div>
                        <h5 class="card-title mb-1">@totalEmployees</h5>
                        <p class="card-text text-muted mb-0">@L("Total Employees", "إجمالي الموظفين")</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-4">
                <label class="form-label">@L("Search", "البحث")</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" @bind="searchTerm" @bind:event="oninput"
                           placeholder="@L("Search departments...", "البحث في الأقسام...")" />
                </div>
            </div>
            <div class="col-md-3">
                <label class="form-label">@L("Status", "الحالة")</label>
                <select class="form-select" @bind="statusFilter">
                    <option value="">@L("All Statuses", "جميع الحالات")</option>
                    <option value="active">@L("Active", "نشط")</option>
                    <option value="inactive">@L("Inactive", "غير نشط")</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">@L("View Mode", "وضع العرض")</label>
                <select class="form-select" @bind="viewMode">
                    <option value="tree">@L("Tree View", "عرض شجري")</option>
                    <option value="list">@L("List View", "عرض قائمة")</option>
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button class="btn btn-outline-secondary w-100" @onclick="ClearFilters">
                    <i class="fas fa-times @GetMarginEnd(1)"></i>
                    @L("Clear", "مسح")
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Departments Display -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-@(viewMode == "tree" ? "sitemap" : "list") @GetMarginEnd(1)"></i>
                @L("Departments", "الأقسام") (@GetFilteredDepartments().Count())
            </h5>
            <div class="d-flex gap-2">
                <button class="btn btn-sm btn-outline-secondary" @onclick="() => ExpandAll(true)">
                    <i class="fas fa-expand-arrows-alt @GetMarginEnd(1)"></i>
                    @L("Expand All", "توسيع الكل")
                </button>
                <button class="btn btn-sm btn-outline-secondary" @onclick="() => ExpandAll(false)">
                    <i class="fas fa-compress-arrows-alt @GetMarginEnd(1)"></i>
                    @L("Collapse All", "طي الكل")
                </button>
            </div>
        </div>
    </div>
    <div class="card-body">
        @if (isLoading)
        {
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">@L("Loading...", "جاري التحميل...")</span>
                </div>
                <p class="mt-2 text-muted">@L("Loading departments...", "جاري تحميل الأقسام...")</p>
            </div>
        }
        else if (!GetFilteredDepartments().Any())
        {
            <div class="text-center py-5">
                <i class="fas fa-sitemap fa-3x text-muted mb-3"></i>
                <h5>@L("No departments found", "لم يتم العثور على أقسام")</h5>
                <p class="text-muted">@L("Create your first department to get started", "أنشئ أول قسم للبدء")</p>
                <button class="btn btn-primary" @onclick="ShowCreateDepartmentModal">
                    <i class="fas fa-plus @GetMarginEnd(1)"></i>
                    @L("Create Department", "إنشاء قسم")
                </button>
            </div>
        }
        else
        {
            @if (viewMode == "tree")
            {
                <div class="department-tree">
                    @foreach (var rootDept in GetRootDepartments())
                    {
                        <DepartmentTreeNode Department="rootDept"
                                          IsExpanded="expandedNodes.Contains(rootDept.Id)"
                                          OnToggleExpand="ToggleNodeExpansion"
                                          OnEdit="EditDepartment"
                                          OnDelete="DeleteDepartment"
                                          OnAddChild="AddChildDepartment"
                                          IsArabic="IsArabic" />
                    }
                </div>
            }
            else
            {
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th scope="col" class="@GetTextStart()">@L("Department", "القسم")</th>
                                <th scope="col" class="@GetTextStart()">@L("Code", "الرمز")</th>
                                <th scope="col" class="@GetTextStart()">@L("Parent", "الأصل")</th>
                                <th scope="col" class="text-center">@L("Level", "المستوى")</th>
                                <th scope="col" class="text-center">@L("Employees", "الموظفون")</th>
                                <th scope="col" class="text-center">@L("Status", "الحالة")</th>
                                <th scope="col" class="text-center">@L("Actions", "الإجراءات")</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var dept in GetFilteredDepartments().OrderBy(d => d.Level).ThenBy(d => d.Order))
                            {
                                <tr>
                                    <td class="@GetTextStart()">
                                        <div class="d-flex align-items-center">
                                            <span style="@GetIndentStyle(dept.Level)"></span>
                                            <div class="department-icon @GetMarginEnd(1)">
                                                <i class="fas fa-building text-primary"></i>
                                            </div>
                                            <div>
                                                <div class="fw-medium">@GetDepartmentDisplayName(dept)</div>
                                                @if (!string.IsNullOrEmpty(GetDepartmentDescription(dept)))
                                                {
                                                    <small class="text-muted">@GetDepartmentDescription(dept)</small>
                                                }
                                            </div>
                                        </div>
                                    </td>
                                    <td class="@GetTextStart()">
                                        <span class="badge bg-light text-dark">@dept.Code</span>
                                    </td>
                                    <td class="@GetTextStart()">
                                        @if (dept.Parent != null)
                                        {
                                            <span class="text-muted">@GetDepartmentDisplayName(dept.Parent)</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">@L("Root", "جذر")</span>
                                        }
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-info">@dept.Level</span>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-secondary">@dept.GetDirectEmployeeCount()</span>
                                    </td>
                                    <td class="text-center">
                                        @if (dept.IsActive)
                                        {
                                            <span class="badge bg-success">@L("Active", "نشط")</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-danger">@L("Inactive", "غير نشط")</span>
                                        }
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-outline-primary" @onclick="() => EditDepartment(dept)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" @onclick="() => AddChildDepartment(dept)">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" @onclick="() => DeleteDepartment(dept)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
        }
    </div>
</div>

<!-- Create/Edit Department Modal -->
@if (showDepartmentModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-@(isEditMode ? "edit" : "plus") @GetMarginEnd(1)"></i>
                        @(isEditMode ? L("Edit Department", "تعديل قسم") : L("Create Department", "إنشاء قسم"))
                    </h5>
                    <button type="button" class="btn-close" @onclick="CloseDepartmentModal"></button>
                </div>
                <div class="modal-body">
                    <EditForm Model="departmentModel" OnValidSubmit="SaveDepartment" FormName="departmentForm">
                        <DataAnnotationsValidator />

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">@L("English Name", "الاسم بالإنجليزية") *</label>
                                <InputText @bind-Value="departmentModel.NameEn" class="form-control"
                                          placeholder="@L("Enter English name", "أدخل الاسم بالإنجليزية")" />
                                <ValidationMessage For="@(() => departmentModel.NameEn)" />
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">@L("Arabic Name", "الاسم بالعربية") *</label>
                                <InputText @bind-Value="departmentModel.NameAr" class="form-control"
                                          placeholder="@L("Enter Arabic name", "أدخل الاسم بالعربية")" />
                                <ValidationMessage For="@(() => departmentModel.NameAr)" />
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">@L("Department Code", "رمز القسم") *</label>
                                <InputText @bind-Value="departmentModel.Code" class="form-control"
                                          placeholder="@L("Enter department code", "أدخل رمز القسم")" />
                                <ValidationMessage For="@(() => departmentModel.Code)" />
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">@L("Parent Department", "القسم الأصل")</label>
                                <InputSelect @bind-Value="departmentModel.ParentId" class="form-select">
                                    <option value="">@L("No Parent (Root Department)", "لا يوجد أصل (قسم جذر)")</option>
                                    @foreach (var dept in GetAvailableParentDepartments())
                                    {
                                        <option value="@dept.Id">@GetDepartmentDisplayName(dept)</option>
                                    }
                                </InputSelect>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">@L("English Description", "الوصف بالإنجليزية")</label>
                                <InputTextArea @bind-Value="departmentModel.DescriptionEn" class="form-control" rows="3"
                                              placeholder="@L("Enter English description", "أدخل الوصف بالإنجليزية")" />
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">@L("Arabic Description", "الوصف بالعربية")</label>
                                <InputTextArea @bind-Value="departmentModel.DescriptionAr" class="form-control" rows="3"
                                              placeholder="@L("Enter Arabic description", "أدخل الوصف بالعربية")" />
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">@L("Display Order", "ترتيب العرض")</label>
                                <InputNumber @bind-Value="departmentModel.Order" class="form-control" />
                            </div>
                            <div class="col-md-6 mb-3 d-flex align-items-end">
                                <div class="form-check">
                                    <InputCheckbox @bind-Value="departmentModel.IsActive" class="form-check-input" id="isActive" />
                                    <label class="form-check-label" for="isActive">
                                        @L("Active", "نشط")
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <button type="button" class="btn btn-secondary" @onclick="CloseDepartmentModal">
                                @L("Cancel", "إلغاء")
                            </button>
                            <button type="submit" class="btn btn-primary" disabled="@isSaving">
                                @if (isSaving)
                                {
                                    <span class="spinner-border spinner-border-sm @GetMarginEnd(1)" role="status"></span>
                                }
                                <i class="fas fa-save @GetMarginEnd(1)"></i>
                                @L("Save", "حفظ")
                            </button>
                        </div>
                    </EditForm>
                </div>
            </div>
        </div>
    </div>
}

<!-- Confirmation Modal -->
@if (showConfirmModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle @GetMarginEnd(1) text-warning"></i>
                        @L("Confirm Action", "تأكيد الإجراء")
                    </h5>
                    <button type="button" class="btn-close" @onclick="CloseConfirmModal"></button>
                </div>
                <div class="modal-body">
                    <p>@confirmMessage</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="CloseConfirmModal">
                        @L("Cancel", "إلغاء")
                    </button>
                    <button type="button" class="btn btn-danger" @onclick="ConfirmAction" disabled="@isSaving">
                        @if (isSaving)
                        {
                            <span class="spinner-border spinner-border-sm @GetMarginEnd(1)" role="status"></span>
                        }
                        @L("Confirm", "تأكيد")
                    </button>
                </div>
            </div>
        </div>
    </div>
}

@code {
    // Data properties
    private List<Department> departments = new();
    private HashSet<int> expandedNodes = new();

    // Statistics
    private int totalDepartments = 0;
    private int activeDepartments = 0;
    private int maxDepth = 0;
    private int totalEmployees = 0;

    // UI state
    private bool isLoading = true;
    private bool isSaving = false;
    private string searchTerm = string.Empty;
    private string statusFilter = string.Empty;
    private string viewMode = "tree";

    // Modal state
    private bool showDepartmentModal = false;
    private bool showConfirmModal = false;
    private bool isEditMode = false;
    private DepartmentModel departmentModel = new();
    private string confirmMessage = string.Empty;
    private Func<Task>? confirmAction;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            // Load departments with their relationships
            departments = await DbContext.Departments
                .Include(d => d.Parent)
                .Include(d => d.Children)
                .Include(d => d.PrimaryUsers)
                .Where(d => !d.IsDeleted)
                .OrderBy(d => d.Level)
                .ThenBy(d => d.Order)
                .ThenBy(d => d.NameEn)
                .ToListAsync();

            // Calculate statistics
            CalculateStatistics();

            // Expand root nodes by default
            foreach (var rootDept in GetRootDepartments())
            {
                expandedNodes.Add(rootDept.Id);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading departments: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void CalculateStatistics()
    {
        totalDepartments = departments.Count;
        activeDepartments = departments.Count(d => d.IsActive);
        maxDepth = departments.Any() ? departments.Max(d => d.Level) + 1 : 0;
        totalEmployees = departments.Sum(d => d.GetDirectEmployeeCount());
    }

    private IEnumerable<Department> GetFilteredDepartments()
    {
        var filtered = departments.AsEnumerable();

        // Apply search filter
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            filtered = filtered.Where(d =>
                d.NameEn.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                d.NameAr.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                d.Code.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));
        }

        // Apply status filter
        if (!string.IsNullOrEmpty(statusFilter))
        {
            filtered = statusFilter switch
            {
                "active" => filtered.Where(d => d.IsActive),
                "inactive" => filtered.Where(d => !d.IsActive),
                _ => filtered
            };
        }

        return filtered;
    }

    private IEnumerable<Department> GetRootDepartments()
    {
        return GetFilteredDepartments().Where(d => d.ParentId == null);
    }

    private IEnumerable<Department> GetAvailableParentDepartments()
    {
        var availableDepts = departments.Where(d => d.IsActive);

        // If editing, exclude the current department and its descendants
        if (isEditMode && departmentModel.Id > 0)
        {
            var currentDept = departments.FirstOrDefault(d => d.Id == departmentModel.Id);
            if (currentDept != null)
            {
                var excludeIds = new HashSet<int> { currentDept.Id };
                AddDescendantIds(currentDept, excludeIds);
                availableDepts = availableDepts.Where(d => !excludeIds.Contains(d.Id));
            }
        }

        return availableDepts.OrderBy(d => d.Level).ThenBy(d => d.NameEn);
    }

    private void AddDescendantIds(Department department, HashSet<int> excludeIds)
    {
        foreach (var child in department.Children.Where(c => !c.IsDeleted))
        {
            excludeIds.Add(child.Id);
            AddDescendantIds(child, excludeIds);
        }
    }

    private string GetDepartmentDisplayName(Department department)
    {
        return IsArabic ? department.NameAr : department.NameEn;
    }

    private string GetDepartmentDescription(Department department)
    {
        return IsArabic ? department.DescriptionAr : department.DescriptionEn;
    }

    private string GetIndentStyle(int level)
    {
        return $"margin-{(IsRTL ? "right" : "left")}: {level * 20}px;";
    }

    // Modal management
    private void ShowCreateDepartmentModal()
    {
        isEditMode = false;
        departmentModel = new DepartmentModel();
        showDepartmentModal = true;
        StateHasChanged();
    }

    private void EditDepartment(Department department)
    {
        isEditMode = true;
        departmentModel = new DepartmentModel
        {
            Id = department.Id,
            NameEn = department.NameEn,
            NameAr = department.NameAr,
            Code = department.Code,
            DescriptionEn = department.DescriptionEn,
            DescriptionAr = department.DescriptionAr,
            ParentId = department.ParentId,
            Order = department.Order,
            IsActive = department.IsActive
        };
        showDepartmentModal = true;
        StateHasChanged();
    }

    private void AddChildDepartment(Department parentDepartment)
    {
        isEditMode = false;
        departmentModel = new DepartmentModel
        {
            ParentId = parentDepartment.Id
        };
        showDepartmentModal = true;
        StateHasChanged();
    }

    private async Task SaveDepartment()
    {
        isSaving = true;
        StateHasChanged();

        try
        {
            if (isEditMode)
            {
                // Update existing department
                var existingDept = await DbContext.Departments.FindAsync(departmentModel.Id);
                if (existingDept != null)
                {
                    existingDept.NameEn = departmentModel.NameEn;
                    existingDept.NameAr = departmentModel.NameAr;
                    existingDept.Code = departmentModel.Code;
                    existingDept.DescriptionEn = departmentModel.DescriptionEn;
                    existingDept.DescriptionAr = departmentModel.DescriptionAr;
                    existingDept.ParentId = departmentModel.ParentId;
                    existingDept.Order = departmentModel.Order;
                    existingDept.IsActive = departmentModel.IsActive;
                    existingDept.UpdatedAt = DateTime.UtcNow;

                    // Update level based on parent
                    if (departmentModel.ParentId.HasValue)
                    {
                        var parent = await DbContext.Departments.FindAsync(departmentModel.ParentId.Value);
                        existingDept.Level = parent?.Level + 1 ?? 0;
                    }
                    else
                    {
                        existingDept.Level = 0;
                    }
                }
            }
            else
            {
                // Create new department
                var newDept = new Department
                {
                    NameEn = departmentModel.NameEn,
                    NameAr = departmentModel.NameAr,
                    Code = departmentModel.Code,
                    DescriptionEn = departmentModel.DescriptionEn,
                    DescriptionAr = departmentModel.DescriptionAr,
                    ParentId = departmentModel.ParentId,
                    Order = departmentModel.Order,
                    IsActive = departmentModel.IsActive,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                // Set level based on parent
                if (departmentModel.ParentId.HasValue)
                {
                    var parent = await DbContext.Departments.FindAsync(departmentModel.ParentId.Value);
                    newDept.Level = parent?.Level + 1 ?? 0;
                }
                else
                {
                    newDept.Level = 0;
                }

                DbContext.Departments.Add(newDept);
            }

            await DbContext.SaveChangesAsync();
            await LoadData();
            CloseDepartmentModal();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error saving department: {ex.Message}");
            // Could show error message to user
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    private void DeleteDepartment(Department department)
    {
        var deptName = GetDepartmentDisplayName(department);
        var hasChildren = department.Children.Any(c => !c.IsDeleted);
        var hasEmployees = department.GetDirectEmployeeCount() > 0;

        if (hasChildren || hasEmployees)
        {
            var issues = new List<string>();
            if (hasChildren)
                issues.Add(L("sub-departments", "أقسام فرعية"));
            if (hasEmployees)
                issues.Add(L("employees", "موظفين"));

            confirmMessage = L($"Cannot delete department '{deptName}' because it contains {string.Join(" and ", issues)}. Please move or delete them first.",
                             $"لا يمكن حذف القسم '{deptName}' لأنه يحتوي على {string.Join(" و ", issues)}. يرجى نقلهم أو حذفهم أولاً.");
        }
        else
        {
            confirmMessage = L($"Are you sure you want to delete department '{deptName}'? This action cannot be undone.",
                             $"هل أنت متأكد من أنك تريد حذف القسم '{deptName}'؟ لا يمكن التراجع عن هذا الإجراء.");

            confirmAction = async () =>
            {
                try
                {
                    department.IsDeleted = true;
                    department.DeletedAt = DateTime.UtcNow;
                    await DbContext.SaveChangesAsync();
                    await LoadData();
                    CloseConfirmModal();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error deleting department: {ex.Message}");
                }
            };
        }

        showConfirmModal = true;
        StateHasChanged();
    }

    // Tree node management
    private void ToggleNodeExpansion(int departmentId)
    {
        if (expandedNodes.Contains(departmentId))
        {
            expandedNodes.Remove(departmentId);
        }
        else
        {
            expandedNodes.Add(departmentId);
        }
        StateHasChanged();
    }

    private void ExpandAll(bool expand)
    {
        if (expand)
        {
            foreach (var dept in departments)
            {
                expandedNodes.Add(dept.Id);
            }
        }
        else
        {
            expandedNodes.Clear();
        }
        StateHasChanged();
    }

    // Utility methods
    private void ClearFilters()
    {
        searchTerm = string.Empty;
        statusFilter = string.Empty;
        StateHasChanged();
    }

    private async Task RefreshData()
    {
        await LoadData();
    }

    private async Task ExportDepartments()
    {
        // TODO: Implement export functionality
        await JSRuntime.InvokeVoidAsync("alert", L("Export functionality will be implemented soon", "سيتم تنفيذ وظيفة التصدير قريباً"));
    }

    private void CloseDepartmentModal()
    {
        showDepartmentModal = false;
        departmentModel = new DepartmentModel();
        StateHasChanged();
    }

    private void CloseConfirmModal()
    {
        showConfirmModal = false;
        confirmMessage = string.Empty;
        confirmAction = null;
        StateHasChanged();
    }

    private async Task ConfirmAction()
    {
        if (confirmAction != null)
        {
            isSaving = true;
            StateHasChanged();

            await confirmAction();

            isSaving = false;
            StateHasChanged();
        }
    }

    // Model for department form
    public class DepartmentModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "English name is required")]
        [StringLength(255, ErrorMessage = "English name cannot exceed 255 characters")]
        public string NameEn { get; set; } = string.Empty;

        [Required(ErrorMessage = "Arabic name is required")]
        [StringLength(255, ErrorMessage = "Arabic name cannot exceed 255 characters")]
        public string NameAr { get; set; } = string.Empty;

        [Required(ErrorMessage = "Department code is required")]
        [StringLength(50, ErrorMessage = "Department code cannot exceed 50 characters")]
        public string Code { get; set; } = string.Empty;

        public string DescriptionEn { get; set; } = string.Empty;
        public string DescriptionAr { get; set; } = string.Empty;
        public int? ParentId { get; set; }
        public int Order { get; set; } = 0;
        public bool IsActive { get; set; } = true;
    }
}
</AuthenticationGuard>