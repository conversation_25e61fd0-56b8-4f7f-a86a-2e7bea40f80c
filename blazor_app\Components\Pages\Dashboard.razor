@page "/dashboard"
@rendermode InteractiveServer
@using Microsoft.AspNetCore.Components.Authorization
@using EmployeeRatingSystem.Blazor.Models
@using EmployeeRatingSystem.Blazor.Components.Shared
@using EmployeeRatingSystem.Blazor.Services
@using static EmployeeRatingSystem.Blazor.Services.DashboardStatisticsService
@inherits LocalizedComponentBase
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IEmployeeAuthenticationService EmployeeAuthService
@inject DashboardStatisticsService DashboardStatsService

<PageTitle>@GetPageTitle("Dashboard", "لوحة التحكم")</PageTitle>

<AuthenticationGuard>
<div class="container py-4">
    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">@L("Loading dashboard...", "جاري تحميل لوحة التحكم...")</p>
        </div>
    }
    else if (currentUser != null)
    {
        <!-- Enhanced Welcome Header -->
        <div class="row mb-4">
            <div class="col">
                <div class="card bg-gradient-primary text-white border-0 shadow">
                    <div class="card-body py-4">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <div class="profile-avatar profile-avatar-lg bg-white bg-opacity-20 border border-white border-opacity-30">
                                    @GetUserInitials(currentUser)
                                </div>
                            </div>
                            <div class="col">
                                <div class="row">
                                    <div class="col-md-8">
                                        <h2 class="h4 mb-1 fw-bold">
                                            @L("Welcome back", "مرحباً بعودتك"), @GetUserDisplayName(currentUser)!
                                        </h2>
                                        <p class="mb-2 opacity-90">
                                            @GetRoleDisplayName(currentUser.Role) • @L("Employee ID", "رقم الموظف"): @currentUser.EmployeeId
                                        </p>
                                        @if (currentUser.PrimaryDepartment != null)
                                        {
                                            <p class="mb-0 opacity-75">
                                                <i class="fas fa-building @GetMarginEnd(1)"></i>
                                                @(IsArabic ? currentUser.PrimaryDepartment.NameAr : currentUser.PrimaryDepartment.NameEn)
                                            </p>
                                        }
                                    </div>
                                    <div class="col-md-4 text-md-end">
                                        <div class="d-flex flex-column align-items-md-end">
                                            <small class="opacity-75 mb-1">
                                                <i class="fas fa-calendar @GetMarginEnd(1)"></i>
                                                @DateTime.Now.ToString("dddd, MMMM dd, yyyy", IsArabic ? new System.Globalization.CultureInfo("ar-SA") : new System.Globalization.CultureInfo("en-US"))
                                            </small>
                                            <small class="opacity-75">
                                                <i class="fas fa-clock @GetMarginEnd(1)"></i>
                                                @DateTime.Now.ToString("HH:mm")
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Access Toolbar -->
        <div class="row mb-4">
            <div class="col">
                <div class="card border-0 shadow-sm">
                    <div class="card-body py-3">
                        <div class="d-flex flex-wrap align-items-center justify-content-between">
                            <div class="d-flex align-items-center">
                                <h6 class="mb-0 @GetMarginEnd(3)">@L("Quick Actions", "إجراءات سريعة"):</h6>
                                <div class="btn-group" role="group">
                                    @if (currentUser.Role == UserRole.SUPER_ADMIN || currentUser.Role == UserRole.EXCELLENCE_TEAM || currentUser.Role == UserRole.MANAGER)
                                    {
                                        <a href="/evaluations/comprehensive" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-plus @GetMarginEnd(1)"></i>@L("New Evaluation", "تقييم جديد")
                                        </a>
                                        <a href="/evaluations/quarterly" class="btn btn-outline-success btn-sm">
                                            <i class="fas fa-calendar-alt @GetMarginEnd(1)"></i>@L("Quarterly", "ربع سنوي")
                                        </a>
                                        <a href="/evaluations/reports" class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-chart-bar @GetMarginEnd(1)"></i>@L("Reports", "التقارير")
                                        </a>
                                    }
                                    else
                                    {
                                        <a href="/evaluations" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-star @GetMarginEnd(1)"></i>@L("My Evaluations", "تقييماتي")
                                        </a>
                                        <a href="/profile" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-user @GetMarginEnd(1)"></i>@L("Profile", "الملف الشخصي")
                                        </a>
                                    }
                                </div>
                            </div>
                            <div class="text-muted small">
                                <i class="fas fa-clock @GetMarginEnd(1)"></i>
                                @L("Last login", "آخر دخول"): @DateTime.Now.ToString("MMM dd, HH:mm")
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Role-based Dashboard Content -->
        @if (currentUser.Role == UserRole.SUPER_ADMIN || currentUser.Role == UserRole.EXCELLENCE_TEAM)
        {
            <!-- Super Admin & Excellence Team Dashboard -->
            <div class="row mb-4">
                <div class="col">
                    <h3 class="h5 mb-3">
                        @if (currentUser.Role == UserRole.SUPER_ADMIN)
                        {
                            <i class="fas fa-crown @GetMarginEnd(1) text-warning"></i>
                            @L("Super Administrator Dashboard", "لوحة تحكم المدير العام")
                        }
                        else
                        {
                            <i class="fas fa-star @GetMarginEnd(1) text-warning"></i>
                            @L("Excellence Team Dashboard", "لوحة تحكم فريق التميز")
                        }
                    </h3>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row g-4 mb-4">
                <div class="col-md-6 col-lg-3">
                    <div class="card stat-card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="stat-icon bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                <i class="fas fa-users fa-lg text-primary"></i>
                            </div>
                            <h3 class="h2 mb-1 text-primary">@(statistics?.TotalUsers ?? 0)</h3>
                            <p class="text-muted mb-0">@L("Total Users", "إجمالي المستخدمين")</p>
                            <small class="text-success">
                                <i class="fas fa-arrow-up"></i> @(statistics?.TotalEmployees ?? 0) @L("employees", "موظف")
                            </small>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-lg-3">
                    <div class="card stat-card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="stat-icon bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                <i class="fas fa-clipboard-list fa-lg text-info"></i>
                            </div>
                            <h3 class="h2 mb-1 text-info">@(statistics?.PendingEvaluations ?? 0)</h3>
                            <p class="text-muted mb-0">@L("Pending Evaluations", "التقييمات المعلقة")</p>
                            <small class="text-warning">
                                <i class="fas fa-clock"></i> @L("awaiting review", "في انتظار المراجعة")
                            </small>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-lg-3">
                    <div class="card stat-card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="stat-icon bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                <i class="fas fa-calendar-check fa-lg text-success"></i>
                            </div>
                            <h3 class="h2 mb-1 text-success">@(statistics?.CompletedEvaluations ?? 0)</h3>
                            <p class="text-muted mb-0">@L("Completed This Month", "مكتمل هذا الشهر")</p>
                            <small class="text-success">
                                <i class="fas fa-check-circle"></i> @L("evaluations done", "تقييم منجز")
                            </small>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-lg-3">
                    <div class="card stat-card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="stat-icon bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                <i class="fas fa-chart-line fa-lg text-warning"></i>
                            </div>
                            <h3 class="h2 mb-1 text-warning">@(statistics?.AveragePerformanceScore.ToString("F1", System.Globalization.CultureInfo.InvariantCulture) ?? "0.0")%</h3>
                            <p class="text-muted mb-0">@L("Average Performance", "متوسط الأداء")</p>
                            <small class="text-success">
                                <i class="fas fa-trophy"></i> @(statistics?.CompletionRate.ToString("F1", System.Globalization.CultureInfo.InvariantCulture) ?? "0.0")% @L("completion", "مكتمل")
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row g-4">
                <div class="col-md-6 col-lg-3">
                    <div class="card text-center h-100 border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-users fa-2x text-primary mb-3"></i>
                            <h5 class="card-title">@L("User Management", "إدارة المستخدمين")</h5>
                            <p class="card-text">@L("Manage all system users and roles", "إدارة جميع مستخدمي النظام والأدوار")</p>
                            <a href="/users" class="btn btn-primary">@L("Manage Users", "إدارة المستخدمين")</a>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-lg-3">
                    <div class="card text-center h-100 border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-sitemap fa-2x text-success mb-3"></i>
                            <h5 class="card-title">@L("Departments", "الأقسام")</h5>
                            <p class="card-text">@L("Manage organizational structure", "إدارة الهيكل التنظيمي")</p>
                            <a href="/departments" class="btn btn-success">@L("Manage Departments", "إدارة الأقسام")</a>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-lg-3">
                    <div class="card text-center h-100 border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-clipboard-list fa-2x text-info mb-3"></i>
                            <h5 class="card-title">@L("Evaluations", "التقييمات")</h5>
                            <p class="card-text">@L("View and manage all evaluations", "عرض وإدارة جميع التقييمات")</p>
                            <a href="/evaluations" class="btn btn-info">@L("View Evaluations", "عرض التقييمات")</a>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-lg-3">
                    <div class="card text-center h-100 border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-chart-bar fa-2x text-warning mb-3"></i>
                            <h5 class="card-title">@L("Reports", "التقارير")</h5>
                            <p class="card-text">@L("System-wide analytics and reports", "تحليلات وتقارير النظام")</p>
                            <a href="/evaluations/reports" class="btn btn-warning">@L("View Reports", "عرض التقارير")</a>
                        </div>
                    </div>
                </div>
            </div>
        }
        else if (currentUser.Role == UserRole.MANAGER)
        {
            <!-- Manager Dashboard -->
            <div class="row mb-4">
                <div class="col">
                    <h3 class="h5 mb-3">
                        <i class="fas fa-user-tie @GetMarginEnd(1) text-primary"></i>
                        @L("Manager Dashboard", "لوحة تحكم المدير")
                    </h3>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row g-4 mb-4">
                <div class="col-md-6 col-lg-3">
                    <div class="card stat-card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="stat-icon bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                <i class="fas fa-users fa-lg text-primary"></i>
                            </div>
                            <h3 class="h2 mb-1 text-primary">@(statistics?.TeamSize ?? 0)</h3>
                            <p class="text-muted mb-0">@L("Team Size", "حجم الفريق")</p>
                            <small class="text-info">
                                <i class="fas fa-building"></i> @(statistics?.ManagedDepartments ?? 0) @L("departments", "قسم")
                            </small>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-lg-3">
                    <div class="card stat-card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="stat-icon bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                <i class="fas fa-clock fa-lg text-warning"></i>
                            </div>
                            <h3 class="h2 mb-1 text-warning">@(statistics?.PendingEvaluations ?? 0)</h3>
                            <p class="text-muted mb-0">@L("Pending Reviews", "المراجعات المعلقة")</p>
                            <small class="text-danger">
                                <i class="fas fa-exclamation-triangle"></i> @L("awaiting approval", "في انتظار الموافقة")
                            </small>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-lg-3">
                    <div class="card stat-card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="stat-icon bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                <i class="fas fa-chart-line fa-lg text-success"></i>
                            </div>
                            <h3 class="h2 mb-1 text-success">@(statistics?.AveragePerformanceScore.ToString("F1", System.Globalization.CultureInfo.InvariantCulture) ?? "0.0")%</h3>
                            <p class="text-muted mb-0">@L("Team Performance", "أداء الفريق")</p>
                            <small class="text-success">
                                <i class="fas fa-trophy"></i> @L("average score", "متوسط النقاط")
                            </small>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-lg-3">
                    <div class="card stat-card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="stat-icon bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                <i class="fas fa-percentage fa-lg text-info"></i>
                            </div>
                            <h3 class="h2 mb-1 text-info">@(statistics?.CompletionRate.ToString("F1", System.Globalization.CultureInfo.InvariantCulture) ?? "0.0")%</h3>
                            <p class="text-muted mb-0">@L("Completion Rate", "معدل الإنجاز")</p>
                            <small class="text-info">
                                <i class="fas fa-check-circle"></i> @(statistics?.CompletedEvaluations ?? 0)/@(statistics?.TotalEvaluations ?? 0) @L("completed", "مكتمل")
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row g-4">
                <div class="col-md-6">
                    <div class="card text-center h-100 border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-users fa-2x text-primary mb-3"></i>
                            <h5 class="card-title">@L("Team Management", "إدارة الفريق")</h5>
                            <p class="card-text">@L("Manage your team members", "إدارة أعضاء فريقك")</p>
                            <a href="/users" class="btn btn-primary">@L("Manage Team", "إدارة الفريق")</a>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card text-center h-100 border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-clipboard-list fa-2x text-success mb-3"></i>
                            <h5 class="card-title">@L("Evaluations", "التقييمات")</h5>
                            <p class="card-text">@L("Create and manage evaluations", "إنشاء وإدارة التقييمات")</p>
                            <a href="/evaluations/comprehensive" class="btn btn-success">@L("Start Evaluation", "بدء التقييم")</a>
                        </div>
                    </div>
                </div>
            </div>
        }
        else if (currentUser.Role == UserRole.SUPERVISOR)
        {
            <!-- Supervisor Dashboard -->
            <div class="row mb-4">
                <div class="col">
                    <h3 class="h5 mb-3">
                        <i class="fas fa-user-check @GetMarginEnd(1) text-info"></i>
                        @L("Supervisor Dashboard", "لوحة تحكم المشرف")
                    </h3>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row g-4 mb-4">
                <div class="col-md-6 col-lg-3">
                    <div class="card stat-card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="stat-icon bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                <i class="fas fa-clipboard-list fa-lg text-info"></i>
                            </div>
                            <h3 class="h2 mb-1 text-info">@(statistics?.AssignedEvaluations ?? 0)</h3>
                            <p class="text-muted mb-0">@L("Assigned Evaluations", "التقييمات المكلف بها")</p>
                            <small class="text-info">
                                <i class="fas fa-tasks"></i> @L("total assigned", "إجمالي المكلف")
                            </small>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-lg-3">
                    <div class="card stat-card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="stat-icon bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                <i class="fas fa-clock fa-lg text-warning"></i>
                            </div>
                            <h3 class="h2 mb-1 text-warning">@(statistics?.PendingEvaluations ?? 0)</h3>
                            <p class="text-muted mb-0">@L("Pending Work", "العمل المعلق")</p>
                            <small class="text-warning">
                                <i class="fas fa-hourglass-half"></i> @L("to complete", "للإنجاز")
                            </small>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-lg-3">
                    <div class="card stat-card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="stat-icon bg-danger bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                <i class="fas fa-exclamation-triangle fa-lg text-danger"></i>
                            </div>
                            <h3 class="h2 mb-1 text-danger">@(statistics?.OverdueEvaluations ?? 0)</h3>
                            <p class="text-muted mb-0">@L("Overdue Items", "العناصر المتأخرة")</p>
                            <small class="text-danger">
                                <i class="fas fa-calendar-times"></i> @L("past deadline", "تجاوز الموعد")
                            </small>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-lg-3">
                    <div class="card stat-card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="stat-icon bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                <i class="fas fa-chart-line fa-lg text-success"></i>
                            </div>
                            <h3 class="h2 mb-1 text-success">@(statistics?.RecentActivity ?? 0)</h3>
                            <p class="text-muted mb-0">@L("This Week", "هذا الأسبوع")</p>
                            <small class="text-success">
                                <i class="fas fa-calendar-week"></i> @L("evaluations", "تقييم")
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row g-4">
                <div class="col-md-6">
                    <div class="card text-center h-100 border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-clipboard-list fa-2x text-info mb-3"></i>
                            <h5 class="card-title">@L("My Evaluations", "تقييماتي")</h5>
                            <p class="card-text">@L("Conduct employee evaluations", "إجراء تقييمات الموظفين")</p>
                            <a href="/evaluations/comprehensive" class="btn btn-info">@L("Start Evaluation", "بدء التقييم")</a>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card text-center h-100 border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-chart-line fa-2x text-success mb-3"></i>
                            <h5 class="card-title">@L("Performance Reports", "تقارير الأداء")</h5>
                            <p class="card-text">@L("View team performance reports", "عرض تقارير أداء الفريق")</p>
                            <a href="/evaluations/reports" class="btn btn-success">@L("View Reports", "عرض التقارير")</a>
                        </div>
                    </div>
                </div>
            </div>
        }
        else if (currentUser.Role == UserRole.EMPLOYEE)
        {
            <!-- Employee Dashboard -->
            <div class="row mb-4">
                <div class="col">
                    <h3 class="h5 mb-3">
                        <i class="fas fa-user @GetMarginEnd(1) text-secondary"></i>
                        @L("Employee Dashboard", "لوحة تحكم الموظف")
                    </h3>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row g-4 mb-4">
                <div class="col-md-6 col-lg-3">
                    <div class="card stat-card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="stat-icon bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                <i class="fas fa-star fa-lg text-warning"></i>
                            </div>
                            <h3 class="h2 mb-1 text-warning">@(statistics?.LatestScore.ToString("F1", System.Globalization.CultureInfo.InvariantCulture) ?? "0.0")%</h3>
                            <p class="text-muted mb-0">@L("Latest Score", "أحدث نقاط")</p>
                            <small class="text-success">
                                <i class="fas fa-trophy"></i> @L("my performance", "أدائي")
                            </small>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-lg-3">
                    <div class="card stat-card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="stat-icon bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                <i class="fas fa-chart-line fa-lg text-info"></i>
                            </div>
                            <h3 class="h2 mb-1 text-info">@(statistics?.AveragePerformanceScore.ToString("F1", System.Globalization.CultureInfo.InvariantCulture) ?? "0.0")%</h3>
                            <p class="text-muted mb-0">@L("Average Score", "متوسط النقاط")</p>
                            <small class="text-info">
                                <i class="fas fa-calculator"></i> @(statistics?.TotalEvaluations ?? 0) @L("evaluations", "تقييم")
                            </small>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-lg-3">
                    <div class="card stat-card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="stat-icon bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                <i class="fas fa-medal fa-lg text-primary"></i>
                            </div>
                            <h3 class="h2 mb-1 text-primary">#@(statistics?.DepartmentRanking ?? 0)</h3>
                            <p class="text-muted mb-0">@L("Department Ranking", "ترتيب القسم")</p>
                            <small class="text-primary">
                                <i class="fas fa-users"></i> @L("in department", "في القسم")
                            </small>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-lg-3">
                    <div class="card stat-card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="stat-icon bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                <i class="fas fa-calendar-alt fa-lg text-success"></i>
                            </div>
                            <h3 class="h2 mb-1 text-success">@(statistics?.DaysUntilNextReview ?? 0)</h3>
                            <p class="text-muted mb-0">@L("Days Until Review", "أيام حتى المراجعة")</p>
                            <small class="text-success">
                                <i class="fas fa-clock"></i> @L("next evaluation", "التقييم القادم")
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row g-4">
                <div class="col-md-6">
                    <div class="card text-center h-100 border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-star fa-2x text-warning mb-3"></i>
                            <h5 class="card-title">@L("My Evaluations", "تقييماتي")</h5>
                            <p class="card-text">@L("View your performance evaluations", "عرض تقييمات أدائك")</p>
                            <a href="/evaluations" class="btn btn-warning">@L("View My Evaluations", "عرض تقييماتي")</a>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card text-center h-100 border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-user-circle fa-2x text-primary mb-3"></i>
                            <h5 class="card-title">@L("My Profile", "ملفي الشخصي")</h5>
                            <p class="card-text">@L("Update your personal information", "تحديث معلوماتك الشخصية")</p>
                            <a href="/profile" class="btn btn-primary">@L("Edit Profile", "تعديل الملف الشخصي")</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Insights Section -->
            @if (currentUser.Role == UserRole.SUPER_ADMIN || currentUser.Role == UserRole.EXCELLENCE_TEAM || currentUser.Role == UserRole.MANAGER)
            {
                <div class="row mt-5">
                    <div class="col-lg-8">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-transparent border-bottom-0 pb-0">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-chart-line @GetMarginEnd(2) text-primary"></i>
                                    @L("Performance Overview", "نظرة عامة على الأداء")
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <div class="text-center p-3 bg-light rounded">
                                            <div class="h4 text-success mb-1">@(statistics?.CompletionRate.ToString("F1", System.Globalization.CultureInfo.InvariantCulture) ?? "0.0")%</div>
                                            <small class="text-muted">@L("Completion Rate", "معدل الإنجاز")</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="text-center p-3 bg-light rounded">
                                            <div class="h4 text-primary mb-1">@(statistics?.AveragePerformanceScore.ToString("F1", System.Globalization.CultureInfo.InvariantCulture) ?? "0.0")%</div>
                                            <small class="text-muted">@L("Average Score", "متوسط النقاط")</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="text-center p-3 bg-light rounded">
                                            <div class="h4 text-warning mb-1">@(statistics?.PendingEvaluations ?? 0)</div>
                                            <small class="text-muted">@L("Pending Items", "العناصر المعلقة")</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-4">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <small class="text-muted">@L("System Health", "صحة النظام")</small>
                                        <small class="text-success">@L("Excellent", "ممتاز")</small>
                                    </div>
                                    <div class="progress" style="height: 8px;">
                                        <div class="progress-bar bg-success" role="progressbar" style="width: @(statistics?.CompletionRate ?? 0)%" aria-valuenow="@(statistics?.CompletionRate ?? 0)" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-header bg-transparent border-bottom-0 pb-0">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-bell @GetMarginEnd(2) text-warning"></i>
                                    @L("Quick Insights", "رؤى سريعة")
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="flex-shrink-0">
                                        <div class="bg-success bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                            <i class="fas fa-trophy text-success"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 @GetMarginStart(3)">
                                        <div class="fw-bold">@L("Top Performers", "أفضل الأداء")</div>
                                        <small class="text-muted">@(statistics?.TotalEmployees ?? 0) @L("employees evaluated", "موظف تم تقييمه")</small>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center mb-3">
                                    <div class="flex-shrink-0">
                                        <div class="bg-info bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                            <i class="fas fa-calendar-check text-info"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 @GetMarginStart(3)">
                                        <div class="fw-bold">@L("This Month", "هذا الشهر")</div>
                                        <small class="text-muted">@(statistics?.RecentActivity ?? 0) @L("evaluations completed", "تقييم مكتمل")</small>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <div class="bg-warning bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                            <i class="fas fa-clock text-warning"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 @GetMarginStart(3)">
                                        <div class="fw-bold">@L("Upcoming", "القادم")</div>
                                        <small class="text-muted">@L("Next review cycle starts soon", "دورة المراجعة القادمة تبدأ قريباً")</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }

        <!-- Recent Activity Section -->
        @if (recentActivities.Any())
        {
            <div class="row mt-5">
                <div class="col">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-transparent border-bottom-0 pb-0">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-history @GetMarginEnd(2) text-muted"></i>
                                @L("Recent Activity", "النشاط الأخير")
                            </h5>
                        </div>
                        <div class="card-body pt-3">
                            <div class="timeline">
                                @foreach (var activity in recentActivities.Take(5))
                                {
                                    <div class="timeline-item d-flex align-items-start mb-3">
                                        <div class="timeline-marker @GetMarginEnd(3)">
                                            <div class="<EMAIL> bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                <i class="@activity.Icon <EMAIL>"></i>
                                            </div>
                                        </div>
                                        <div class="timeline-content flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-1">@activity.Title</h6>
                                                    <p class="text-muted mb-0 small">@activity.Description</p>
                                                </div>
                                                <small class="text-muted">
                                                    @GetTimeAgo(activity.Timestamp)
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                            @if (recentActivities.Count > 5)
                            {
                                <div class="text-center mt-3">
                                    <a href="/evaluations" class="btn btn-outline-primary btn-sm">
                                        @L("View All Activity", "عرض جميع الأنشطة")
                                    </a>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
    }

</div>
</AuthenticationGuard>

@code {
    private ApplicationUser? currentUser;
    private bool isLoading = true;
    private DashboardStatistics? statistics;
    private List<RecentActivity> recentActivities = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadCurrentUser();
    }

    private async Task LoadCurrentUser()
    {
        try
        {
            isLoading = true;
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();

            if (authState.User.Identity?.IsAuthenticated == true)
            {
                var employeeId = authState.User.FindFirst("EmployeeId")?.Value;

                if (!string.IsNullOrEmpty(employeeId))
                {
                    currentUser = await EmployeeAuthService.FindByEmployeeIdAsync(employeeId);

                    // Load dashboard statistics with error handling
                    if (currentUser != null)
                    {
                        try
                        {
                            statistics = await DashboardStatsService.GetStatisticsForUserAsync(currentUser);
                            recentActivities = await DashboardStatsService.GetRecentActivitiesAsync(currentUser.Id, currentUser.Role, 5);
                        }
                        catch (Exception ex)
                        {
                            // Use empty statistics as fallback
                            statistics = new DashboardStatistics();
                            recentActivities = new List<RecentActivity>();
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            // Handle any unexpected errors gracefully
        }
        finally
        {
            isLoading = false;
        }
    }

    private string GetUserDisplayName(ApplicationUser? user)
    {
        if (user == null) return string.Empty;

        if (IsArabic && !string.IsNullOrEmpty(user.ArabicName))
            return user.ArabicName;

        if (!string.IsNullOrEmpty(user.EnglishName))
            return user.EnglishName;

        return user.EmployeeId ?? "Unknown User";
    }

    private string GetUserInitials(ApplicationUser? user)
    {
        if (user == null) return "?";

        var name = GetUserDisplayName(user);
        var parts = name.Split(' ', StringSplitOptions.RemoveEmptyEntries);

        if (parts.Length >= 2)
        {
            return $"{parts[0][0]}{parts[1][0]}".ToUpper();
        }
        return parts[0][0].ToString().ToUpper();
    }

    private string GetRoleDisplayName(UserRole role)
    {
        return role switch
        {
            UserRole.SUPER_ADMIN => L("Super Admin", "سوبر أدمين"),
            UserRole.MANAGER => L("Manager", "المدير"),
            UserRole.SUPERVISOR => L("Direct Supervisor", "المسؤول المباشر"),
            UserRole.EXCELLENCE_TEAM => L("Excellence Team", "فريق التميز"),
            UserRole.EMPLOYEE => L("Employee", "موظف"),
            _ => L("Unknown", "غير معروف")
        };
    }

    private string GetTimeAgo(DateTime timestamp)
    {
        var timeSpan = DateTime.Now - timestamp;

        if (timeSpan.TotalMinutes < 1)
            return L("Just now", "الآن");
        if (timeSpan.TotalMinutes < 60)
            return L($"{(int)timeSpan.TotalMinutes} minutes ago", $"منذ {(int)timeSpan.TotalMinutes} دقيقة");
        if (timeSpan.TotalHours < 24)
            return L($"{(int)timeSpan.TotalHours} hours ago", $"منذ {(int)timeSpan.TotalHours} ساعة");
        if (timeSpan.TotalDays < 7)
            return L($"{(int)timeSpan.TotalDays} days ago", $"منذ {(int)timeSpan.TotalDays} يوم");

        return timestamp.ToString("MMM dd", IsArabic ? new System.Globalization.CultureInfo("ar-SA") : new System.Globalization.CultureInfo("en-US"));
    }


}
