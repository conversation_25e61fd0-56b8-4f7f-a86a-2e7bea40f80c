@page "/evaluations"
@rendermode InteractiveServer
@using EmployeeRatingSystem.Blazor.Components.Shared
@using EmployeeRatingSystem.Blazor.Services
@using EmployeeRatingSystem.Blazor.Models
@using EmployeeRatingSystem.Blazor.Data
@using Microsoft.EntityFrameworkCore
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Identity
@inherits LocalizedComponentBase
@inject ApplicationDbContext DbContext
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserManager<ApplicationUser> UserManager
@inject IEvaluationService EvaluationService
@inject IEmployeeManagementService EmployeeManagementService
@inject NavigationManager NavigationManager

<PageTitle>@L("Evaluations", "التقييمات")</PageTitle>

@* Debug: Testing hot reload - Updated at @DateTime.Now.ToString("HH:mm:ss") *@

<!-- Page Header -->
<div class="page-header @GetLayoutClass()">
    <!-- Breadcrumb -->
    <nav aria-label="@L("Breadcrumb", "مسار التنقل")">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="/" class="text-decoration-none">@L("Home", "الرئيسية")</a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                @L("Evaluations", "التقييمات")
            </li>
        </ol>
    </nav>

    <div class="d-flex justify-content-between align-items-start flex-wrap">
        <div class="flex-grow-1">
            <h1 class="h1 mb-2">
                <i class="fas fa-chart-line @GetMarginEnd() text-primary"></i>
                @L("Evaluations", "التقييمات")
            </h1>
            <p class="lead mb-0">@L("Monthly employee performance evaluation system", "نظام التقييم الشهري لأداء الموظفين")</p>
        </div>

        <div class="@GetMarginStart() mt-2 mt-md-0">
            <div class="d-flex gap-2 @GetLayoutClass()">
                @* Monthly Evaluation button - Only visible to roles that can create evaluations *@
                @if (currentUser?.Role != UserRole.EMPLOYEE)
                {
                    <a href="/evaluations/comprehensive" class="btn btn-primary">
                        <i class="fas fa-calculator @GetMarginEnd(1)"></i>
                        @L("Monthly Evaluation", "التقييم الشهري")
                    </a>
                }
                <button class="btn btn-outline-secondary" @onclick="ExportEvaluations">
                    <i class="fas fa-download @GetMarginEnd(1)"></i>
                    @L("Export", "تصدير")
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="d-flex align-items-center">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="@GetMarginStart()">
                        <div class="stat-value">@totalEvaluations</div>
                        <div class="stat-label">@L("Total Evaluations", "إجمالي التقييمات")</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="d-flex align-items-center">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="@GetMarginStart()">
                        <div class="stat-value">@pendingEvaluations</div>
                        <div class="stat-label">@L("Pending", "في الانتظار")</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="d-flex align-items-center">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="@GetMarginStart()">
                        <div class="stat-value">@approvedEvaluations</div>
                        <div class="stat-label">@L("Approved", "معتمد")</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="d-flex align-items-center">
                    <div class="stat-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="@GetMarginStart()">
                        <div class="stat-value">@(FormatPercentage(averageScore))%</div>
                        <div class="stat-label">@L("Average Score", "المتوسط")</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="container-fluid">
    <!-- Filters -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">@L("Search", "البحث")</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" @bind="searchTerm" @bind:event="oninput" @bind:after="FilterEvaluations"
                               placeholder="@L("Search by employee name, ID, or department...", "البحث باسم الموظف أو الرقم أو القسم...")" />
                    </div>
                </div>
                <div class="col-md-3">
                    <label class="form-label">@L("Department", "القسم")</label>
                    <select class="form-select" @bind="departmentFilter" @bind:after="FilterEvaluations">
                        <option value="">@L("All Departments", "جميع الأقسام")</option>
                        @foreach (var dept in departments)
                        {
                            <option value="@dept.Id">@(IsArabic ? dept.NameAr : dept.NameEn)</option>
                        }
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">@L("Date Range", "نطاق التاريخ")</label>
                    <select class="form-select" @bind="dateFilter" @bind:after="FilterEvaluations">
                        <option value="">@L("All Time", "كل الأوقات")</option>
                        <option value="today">@L("Today", "اليوم")</option>
                        <option value="week">@L("This Week", "هذا الأسبوع")</option>
                        <option value="month">@L("This Month", "هذا الشهر")</option>
                        <option value="quarter">@L("This Quarter", "هذا الربع")</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Evaluations Table -->
    <div class="card shadow-sm">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    @L("Evaluation Records", "سجلات التقييم") (@filteredEvaluations.Count)
                </h6>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary btn-sm" @onclick="RefreshData">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            @if (isLoading)
            {
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">@L("Loading", "جاري التحميل")</span>
                    </div>
                    <p class="mt-2 text-muted">@L("Loading evaluations...", "جاري تحميل التقييمات...")</p>
                </div>
            }
            else if (!filteredEvaluations.Any())
            {
                <div class="text-center py-5">
                    <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">@L("No evaluations found", "لم يتم العثور على تقييمات")</h5>
                    @if (currentUser?.Role != UserRole.EMPLOYEE)
                    {
                        <p class="text-muted">@L("Start by creating your first evaluation", "ابدأ بإنشاء أول تقييم")</p>
                        <a href="/evaluations/comprehensive" class="btn btn-primary">
                            <i class="fas fa-calculator @GetMarginEnd(1)"></i>
                            @L("Create Monthly Evaluation", "إنشاء تقييم شهري")
                        </a>
                    }
                    else
                    {
                        <p class="text-muted">@L("No evaluations available in your department", "لا توجد تقييمات متاحة في قسمك")</p>
                    }
                </div>
            }
            else
            {
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th class="@GetTextStart()">@L("Employee", "الموظف")</th>
                                <th class="@GetTextStart()">@L("Evaluator", "المقيم")</th>
                                <th class="@GetTextStart()">@L("Department", "القسم")</th>
                                <th class="text-center">@L("Score", "النتيجة")</th>
                                <th class="@GetTextStart()">@L("Date", "التاريخ")</th>
                                <th class="text-center">@L("Actions", "الإجراءات")</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var evaluation in GetPagedEvaluations())
                            {
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm @GetMarginEnd()">
                                                <div class="avatar-title bg-primary rounded-circle">
                                                    @GetUserInitials(evaluation.Employee)
                                                </div>
                                            </div>
                                            <div>
                                                <div class="fw-medium">@GetUserDisplayName(evaluation.Employee)</div>
                                                <small class="text-muted">@evaluation.Employee.EmployeeId</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="fw-medium">
                                            @if (evaluation.Evaluator != null)
                                            {
                                                @GetUserDisplayName(evaluation.Evaluator)
                                                @if (evaluation.OverallCommentsEn == "Comprehensive Evaluation")
                                                {
                                                    <small class="text-muted d-block">@L("Supervisor", "المشرف")</small>
                                                }
                                            }
                                            else if (evaluation.OverallCommentsEn == "Comprehensive Evaluation")
                                            {
                                                <span class="text-muted">@L("System Calculated", "محسوب تلقائياً")</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">@L("Unknown", "غير معروف")</span>
                                            }
                                        </div>
                                    </td>
                                    <td>
                                        @if (evaluation.Employee.PrimaryDepartment != null)
                                        {
                                            <span class="badge bg-light text-dark">
                                                @(IsArabic ? evaluation.Employee.PrimaryDepartment.NameAr : evaluation.Employee.PrimaryDepartment.NameEn)
                                            </span>
                                        }
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-@GetPerformanceClass(evaluation.TotalScore ?? 0) fs-6">
                                            @(FormatEvaluationPercentage(evaluation))%
                                        </span>
                                    </td>
                                    <td>
                                        <div title="@L("Created", "تاريخ الإنشاء"): @LocalizationService.FormatDateWithTime(evaluation.CreatedAt)">
                                            @LocalizationService.FormatDate(evaluation.CreatedAt)
                                        </div>
                                        <small class="text-muted d-block">@LocalizationService.FormatTimeOnly(evaluation.CreatedAt)</small>
                                        @if (evaluation.EvaluationPeriodStart != default && evaluation.EvaluationPeriodEnd != default)
                                        {
                                            <div class="mt-2">
                                                <small class="text-info d-block" title="@L("Evaluation Period", "فترة التقييم")">
                                                    <i class="fas fa-calendar-alt @GetMarginEnd(1)"></i>
                                                    @LocalizationService.FormatDate(evaluation.EvaluationPeriodStart) - @LocalizationService.FormatDate(evaluation.EvaluationPeriodEnd)
                                                </small>
                                            </div>
                                        }

                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group btn-group-sm" role="group">
                                            @if (CanViewEvaluation(evaluation))
                                            {
                                                <button class="btn btn-outline-primary" @onclick="() => ViewEvaluation(evaluation)"
                                                        title="@L("View", "عرض")">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            }
                                            @if (CanEditEvaluation(evaluation))
                                            {
                                                <button class="btn btn-outline-warning" @onclick="() => EditEvaluation(evaluation)"
                                                        title="@L("Edit", "تعديل")">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            }
                                            @if (evaluation.Status == EvaluationStatus.SUBMITTED)
                                            {
                                                <button class="btn btn-outline-success" @onclick="() => ApproveEvaluation(evaluation)"
                                                        title="@L("Approve", "اعتماد")">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" @onclick="() => RejectEvaluation(evaluation)"
                                                        title="@L("Reject", "رفض")">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            }
                                            @if (CanDeleteEvaluation(evaluation))
                                            {
                                                <button class="btn btn-outline-danger" @onclick="() => DeleteEvaluation(evaluation)"
                                                        title="@L("Delete", "حذف")" disabled="@isSaving">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            }
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if (totalPages > 1)
                {
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted">
                                @L("Showing", "عرض") @((currentPage - 1) * pageSize + 1) @L("to", "إلى") @Math.Min(currentPage * pageSize, filteredEvaluations.Count) @L("of", "من") @filteredEvaluations.Count @L("entries", "إدخال")
                            </div>
                            <nav>
                                <ul class="pagination pagination-sm mb-0">
                                    <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                                        <button class="page-link" @onclick="() => ChangePage(currentPage - 1)" disabled="@(currentPage == 1)">
                                            <i class="fas fa-chevron-left"></i>
                                        </button>
                                    </li>
                                    @for (int i = Math.Max(1, currentPage - 2); i <= Math.Min(totalPages, currentPage + 2); i++)
                                    {
                                        <li class="page-item @(i == currentPage ? "active" : "")">
                                            <button class="page-link" @onclick="() => ChangePage(i)">@i</button>
                                        </li>
                                    }
                                    <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                                        <button class="page-link" @onclick="() => ChangePage(currentPage + 1)" disabled="@(currentPage == totalPages)">
                                            <i class="fas fa-chevron-right"></i>
                                        </button>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                }
            }
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
@if (showConfirmModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle text-warning @GetMarginEnd(1)"></i>
                        @L("Confirm Delete", "تأكيد الحذف")
                    </h5>
                    <button type="button" class="btn-close" @onclick="CloseConfirmModal"></button>
                </div>
                <div class="modal-body">
                    <p>@confirmMessage</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="CloseConfirmModal">
                        @L("Cancel", "إلغاء")
                    </button>
                    <button type="button" class="btn btn-danger" @onclick="ConfirmAction" disabled="@isSaving">
                        @if (isSaving)
                        {
                            <span class="spinner-border spinner-border-sm @GetMarginEnd(1)" role="status" aria-hidden="true"></span>
                        }
                        @L("Delete", "حذف")
                    </button>
                </div>
            </div>
        </div>
    </div>
}

@* View Evaluation Modal *@
@if (showViewModal && selectedEvaluation != null)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-eye text-primary @GetMarginEnd(1)"></i>
                        @L("Evaluation Details", "تفاصيل التقييم")
                    </h5>
                    <button type="button" class="btn-close" @onclick="CloseViewModal"></button>
                </div>
                <div class="modal-body">
                    @if (isLoadingDetails)
                    {
                        <div class="text-center p-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">@L("Loading...", "جاري التحميل...")</span>
                            </div>
                            <p class="mt-2">@L("Loading evaluation details...", "جاري تحميل تفاصيل التقييم...")</p>
                        </div>
                    }
                    else
                    {
                        @* Employee Information Section *@
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-user @GetMarginEnd(1)"></i>
                                            @L("Employee Information", "معلومات الموظف")
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <strong>@L("Name", "الاسم"):</strong><br>
                                                @GetUserDisplayName(selectedEvaluation.Employee)
                                            </div>
                                            <div class="col-sm-6">
                                                <strong>@L("Employee ID", "رقم الموظف"):</strong><br>
                                                @selectedEvaluation.Employee?.EmployeeId
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-sm-6">
                                                <strong>@L("Department", "القسم"):</strong><br>
                                                @(IsArabic ? selectedEvaluation.Employee?.PrimaryDepartment?.NameAr : selectedEvaluation.Employee?.PrimaryDepartment?.NameEn)
                                            </div>
                                            <div class="col-sm-6">
                                                <strong>@L("Role", "الدور"):</strong><br>
                                                @GetRoleDisplayName(selectedEvaluation.Employee?.Role)
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-user-tie @GetMarginEnd(1)"></i>
                                            @L("Evaluator Information", "معلومات المقيم")
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <strong>@L("Name", "الاسم"):</strong><br>
                                                @GetUserDisplayName(selectedEvaluation.Evaluator)
                                            </div>
                                            <div class="col-sm-6">
                                                <strong>@L("Role", "الدور"):</strong><br>
                                                @GetRoleDisplayName(selectedEvaluation.Evaluator?.Role)
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-sm-6">
                                                <strong>@L("Evaluation Date", "تاريخ التقييم"):</strong><br>
                                                @selectedEvaluation.CreatedAt.ToString("yyyy-MM-dd")
                                            </div>
                                            <div class="col-sm-6">
                                                <strong>@L("Status", "الحالة"):</strong><br>
                                                <span class="badge bg-@GetStatusClass(selectedEvaluation.Status)">
                                                    @GetStatusDisplayName(selectedEvaluation.Status)
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @* Evaluation Period and Scores Section *@
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-chart-line @GetMarginEnd(1)"></i>
                                            @L("Evaluation Scores", "درجات التقييم")
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="text-center">
                                                    <h4 class="text-primary">@(selectedEvaluation.TotalScore.HasValue ? (FormatEvaluationPercentage(selectedEvaluation) + "%") : "N/A")</h4>
                                                    <small class="text-muted">@L("Total Score", "الدرجة الإجمالية")</small>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="text-center">
                                                    <h4 class="text-info">@(selectedEvaluation.PercentageScore?.ToString("F0") ?? "N/A")%</h4>
                                                    <small class="text-muted">@L("Percentage", "النسبة المئوية")</small>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="text-center">
                                                    <h4 class="text-success">@selectedEvaluation.PeriodStart.ToString("yyyy-MM-dd")</h4>
                                                    <small class="text-muted">@L("Period Start", "بداية الفترة")</small>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="text-center">
                                                    <h4 class="text-warning">@selectedEvaluation.PeriodEnd.ToString("yyyy-MM-dd")</h4>
                                                    <small class="text-muted">@L("Period End", "نهاية الفترة")</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @* Comments Section *@
                        @if (!string.IsNullOrEmpty(selectedEvaluation.OverallCommentsEn) || !string.IsNullOrEmpty(selectedEvaluation.OverallCommentsAr))
                        {
                            <div class="row mb-4">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">
                                                <i class="fas fa-comments @GetMarginEnd(1)"></i>
                                                @L("Comments and Feedback", "التعليقات والملاحظات")
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            @if (!string.IsNullOrEmpty(selectedEvaluation.OverallCommentsEn))
                                            {
                                                <div class="mb-3">
                                                    <strong>@L("English Comments", "التعليقات بالإنجليزية"):</strong>
                                                    <p class="mt-1">@selectedEvaluation.OverallCommentsEn</p>
                                                </div>
                                            }
                                            @if (!string.IsNullOrEmpty(selectedEvaluation.OverallCommentsAr))
                                            {
                                                <div>
                                                    <strong>@L("Arabic Comments", "التعليقات بالعربية"):</strong>
                                                    <p class="mt-1">@selectedEvaluation.OverallCommentsAr</p>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }

                        @* Monthly Evaluation Details (if applicable) *@
                        @if (selectedEvaluation.Id >= 10000 && selectedComprehensiveEvaluation != null)
                        {
                            <div class="row mb-4">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">
                                                <i class="fas fa-tasks @GetMarginEnd(1)"></i>
                                                @L("Monthly Evaluation Details", "تفاصيل التقييم الشهري")
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="text-center p-3 border rounded">
                                                        <h5 class="text-primary">@(selectedComprehensiveEvaluation.WorkVolumeScore.ToString("F2"))</h5>
                                                        <small>@L("Work Volume Score", "درجة حجم العمل")</small>
                                                        <br><small class="text-muted">(@(FormatPercentage(selectedComprehensiveEvaluation.WorkVolumePercentage))%)</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="text-center p-3 border rounded">
                                                        <h5 class="text-info">@(selectedComprehensiveEvaluation.AttendanceScore.ToString("F2"))</h5>
                                                        <small>@L("Attendance Score", "درجة الحضور")</small>
                                                        <br><small class="text-muted">(@(FormatPercentage(selectedComprehensiveEvaluation.AttendancePercentage))%)</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="text-center p-3 border rounded">
                                                        <h5 class="text-success">@(selectedComprehensiveEvaluation.SupervisorScore.ToString("F2"))</h5>
                                                        <small>@L("Supervisor Score", "درجة المشرف")</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row mt-3">
                                                <div class="col-md-6">
                                                    <strong>@L("Department Rank", "ترتيب القسم"):</strong> @(selectedComprehensiveEvaluation.DepartmentRank?.ToString() ?? "N/A")
                                                </div>
                                                <div class="col-md-6">
                                                    <strong>@L("Overall Rank", "الترتيب العام"):</strong> @(selectedComprehensiveEvaluation.OverallRank?.ToString() ?? "N/A")
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }

                        @* Traditional Evaluation Questions and Responses (if applicable) *@
                        @if (selectedEvaluation.Id < 10000 && selectedEvaluation.Responses?.Any() == true)
                        {
                            <div class="row">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">
                                                <i class="fas fa-question-circle @GetMarginEnd(1)"></i>
                                                @L("Evaluation Questions and Responses", "أسئلة التقييم والإجابات")
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            @foreach (var response in selectedEvaluation.Responses.OrderBy(r => r.Question?.Category?.Order).ThenBy(r => r.Question?.Order))
                                            {
                                                <div class="mb-3 p-3 border rounded">
                                                    <div class="row">
                                                        <div class="col-md-8">
                                                            <strong>@(IsArabic ? response.Question?.TextAr : response.Question?.TextEn)</strong>
                                                            @if (response.Question?.Category != null)
                                                            {
                                                                <br><small class="text-muted">@(IsArabic ? response.Question.Category.NameAr : response.Question.Category.NameEn)</small>
                                                            }
                                                        </div>
                                                        <div class="col-md-4 text-end">
                                                            <span class="badge bg-primary fs-6">@response.Score / @response.Question?.MaxScore</span>
                                                        </div>
                                                    </div>
                                                    @{
                                                        var responseComments = IsArabic ? response.CommentsAr : response.CommentsEn;
                                                    }
                                                    @if (!string.IsNullOrEmpty(responseComments))
                                                    {
                                                        <div class="mt-2">
                                                            <small class="text-muted">@L("Comments", "التعليقات"):</small>
                                                            <p class="mb-0">@responseComments</p>
                                                        </div>
                                                    }
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    }
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="CloseViewModal">
                        @L("Close", "إغلاق")
                    </button>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private bool isLoading = true;
    private string searchTerm = "";
    private string departmentFilter = "";
    private string dateFilter = "";

    private int currentPage = 1;
    private int pageSize = 10;
    private int totalPages = 1;

    // Statistics
    private int totalEvaluations = 0;
    private int pendingEvaluations = 0;
    private int approvedEvaluations = 0;
    private decimal averageScore = 0;

    private List<Evaluation> allEvaluations = new();
    private List<Evaluation> filteredEvaluations = new();
    private List<Department> departments = new();

    // Authentication and authorization
    private ApplicationUser? currentUser = null;
    private bool isAuthenticated = false;

    // Delete confirmation modal
    private bool showConfirmModal = false;
    private string confirmMessage = "";
    private Func<Task>? confirmAction = null;
    private bool isSaving = false;

    // View evaluation modal
    private bool showViewModal = false;
    private Evaluation? selectedEvaluation = null;
    private WorkVolumeData? selectedWorkVolumeData = null;
    private AttendanceData? selectedAttendanceData = null;
    private SupervisorEvaluation? selectedSupervisorEvaluation = null;
    private ComprehensiveEvaluation? selectedComprehensiveEvaluation = null;
    private bool isLoadingDetails = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadCurrentUser();
        await LoadData();
    }

    private async Task LoadCurrentUser()
    {
        try
        {
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            isAuthenticated = authState.User.Identity?.IsAuthenticated ?? false;

            if (isAuthenticated)
            {
                var userEmail = authState.User.Identity?.Name;
                if (!string.IsNullOrEmpty(userEmail))
                {
                    currentUser = await UserManager.FindByEmailAsync(userEmail);
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading current user: {ex.Message}");
            isAuthenticated = false;
            currentUser = null;
        }
    }

    private async Task LoadData()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            // Load departments
            departments = await DbContext.Departments
                .Where(d => d.IsActive && !d.IsDeleted)
                .OrderBy(d => d.NameEn)
                .ToListAsync();

            // Load traditional evaluations with related data
            var traditionalEvaluationsQuery = DbContext.Evaluations
                .Include(e => e.Employee)
                .ThenInclude(e => e.PrimaryDepartment)
                .Include(e => e.Evaluator)
                .Where(e => !e.IsDeleted);

            // Apply departmental filtering for EMPLOYEE role
            if (currentUser?.Role == UserRole.EMPLOYEE && currentUser.PrimaryDepartmentId.HasValue)
            {
                traditionalEvaluationsQuery = traditionalEvaluationsQuery
                    .Where(e => e.Employee.PrimaryDepartmentId == currentUser.PrimaryDepartmentId);
            }

            var traditionalEvaluations = await traditionalEvaluationsQuery
                .OrderByDescending(e => e.CreatedAt)
                .ToListAsync();

            // Load comprehensive evaluations and convert them to Evaluation format for unified display
            IQueryable<ComprehensiveEvaluation> comprehensiveEvaluationsQuery = DbContext.ComprehensiveEvaluations
                .Include(e => e.Employee)
                .ThenInclude(e => e.PrimaryDepartment);

            // Apply departmental filtering for EMPLOYEE role
            if (currentUser?.Role == UserRole.EMPLOYEE && currentUser.PrimaryDepartmentId.HasValue)
            {
                comprehensiveEvaluationsQuery = comprehensiveEvaluationsQuery
                    .Where(e => e.Employee.PrimaryDepartmentId == currentUser.PrimaryDepartmentId);
            }

            var comprehensiveEvaluations = await comprehensiveEvaluationsQuery
                .OrderByDescending(e => e.CalculatedAt)
                .ToListAsync();

            // Load all users who might be evaluators for comprehensive evaluations
            var evaluatorIds = comprehensiveEvaluations
                .Select(ce => ce.CalculatedBy)
                .Where(id => !string.IsNullOrEmpty(id)) // Filter out null/empty IDs
                .Distinct()
                .ToList();
            var evaluators = await DbContext.Users
                .Where(u => evaluatorIds.Contains(u.Id))
                .ToListAsync();

            // Debug logging
            Console.WriteLine($"DEBUG: Found {traditionalEvaluations.Count} traditional evaluations");
            Console.WriteLine($"DEBUG: Found {comprehensiveEvaluations.Count} comprehensive evaluations");

            // Convert comprehensive evaluations to evaluation format for unified display
            var convertedEvaluations = comprehensiveEvaluations.Select(ce => new Evaluation
            {
                Id = ce.Id + 10000, // Offset to avoid ID conflicts with traditional evaluations
                EmployeeId = ce.EmployeeId,
                Employee = ce.Employee,
                EvaluatorId = ce.CalculatedBy, // Use CalculatedBy as evaluator
                Evaluator = evaluators.FirstOrDefault(e => e.Id == ce.CalculatedBy), // Set actual evaluator
                TotalScore = ce.TotalScore,
                PercentageScore = ce.TotalScore * 100, // Convert to percentage for display
                Status = ce.Status, // Use the same status from monthly evaluation
                CreatedAt = ce.CalculatedAt,
                UpdatedAt = ce.UpdatedAt,
                EvaluationPeriodStart = DateTime.Now.AddDays(-30), // Simplified for now
                EvaluationPeriodEnd = DateTime.Now, // Simplified for now
                OverallCommentsEn = "Monthly Evaluation", // Mark as monthly evaluation
                OverallCommentsAr = "التقييم الشهري" // Mark as monthly evaluation in Arabic
            }).ToList();

            // Combine both types of evaluations
            allEvaluations = traditionalEvaluations.Concat(convertedEvaluations)
                .OrderByDescending(e => e.CreatedAt)
                .ToList();

            // Debug logging
            Console.WriteLine($"DEBUG: Converted {convertedEvaluations.Count()} comprehensive evaluations");
            Console.WriteLine($"DEBUG: Total combined evaluations: {allEvaluations.Count}");

            // Calculate statistics
            totalEvaluations = allEvaluations.Count;
            pendingEvaluations = allEvaluations.Count(e => e.Status == EvaluationStatus.SUBMITTED);
            approvedEvaluations = allEvaluations.Count(e => e.Status == EvaluationStatus.APPROVED);
            averageScore = allEvaluations.Any() ? (decimal)allEvaluations.Average(e => (double)(e.TotalScore ?? 0)) : 0;

            FilterEvaluations();
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void FilterEvaluations()
    {
        filteredEvaluations = allEvaluations.ToList();

        // Apply search filter
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            filteredEvaluations = filteredEvaluations.Where(e =>
                // Employee name search (both Arabic and English)
                (!string.IsNullOrEmpty(e.Employee.EnglishName) && e.Employee.EnglishName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) ||
                (!string.IsNullOrEmpty(e.Employee.ArabicName) && e.Employee.ArabicName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) ||
                // Employee ID search
                (!string.IsNullOrEmpty(e.Employee.EmployeeId) && e.Employee.EmployeeId.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) ||
                // Department name search (both Arabic and English)
                (e.Employee.PrimaryDepartment != null && !string.IsNullOrEmpty(e.Employee.PrimaryDepartment.NameEn) && e.Employee.PrimaryDepartment.NameEn.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) ||
                (e.Employee.PrimaryDepartment != null && !string.IsNullOrEmpty(e.Employee.PrimaryDepartment.NameAr) && e.Employee.PrimaryDepartment.NameAr.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) ||
                // Evaluation period search (search in formatted date strings)
                (LocalizationService.ConvertFromUtc(e.EvaluationPeriodStart).ToString("yyyy-MM").Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) ||
                (LocalizationService.ConvertFromUtc(e.EvaluationPeriodStart).ToString("yyyy-MM-dd").Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) ||
                (LocalizationService.FormatDate(e.CreatedAt).Contains(searchTerm, StringComparison.OrdinalIgnoreCase))).ToList();
        }

        // Apply department filter
        if (!string.IsNullOrWhiteSpace(departmentFilter) && int.TryParse(departmentFilter, out var deptId))
        {
            filteredEvaluations = filteredEvaluations.Where(e => e.Employee.PrimaryDepartmentId == deptId).ToList();
        }

        // Apply date filter
        if (!string.IsNullOrWhiteSpace(dateFilter))
        {
            var now = DateTime.Now;
            var utcNow = DateTime.UtcNow;
            filteredEvaluations = dateFilter switch
            {
                "today" => filteredEvaluations.Where(e => LocalizationService.ConvertFromUtc(e.CreatedAt).Date == now.Date).ToList(),
                "week" => filteredEvaluations.Where(e => e.CreatedAt >= utcNow.AddDays(-7)).ToList(),
                "month" => filteredEvaluations.Where(e => e.CreatedAt >= utcNow.AddDays(-30)).ToList(),
                "quarter" => filteredEvaluations.Where(e => e.CreatedAt >= utcNow.AddDays(-90)).ToList(),
                _ => filteredEvaluations
            };
        }

        // Update pagination
        totalPages = (int)Math.Ceiling((double)filteredEvaluations.Count / pageSize);
        currentPage = Math.Min(currentPage, Math.Max(1, totalPages));

        StateHasChanged();
    }

    private List<Evaluation> GetPagedEvaluations()
    {
        return filteredEvaluations
            .Skip((currentPage - 1) * pageSize)
            .Take(pageSize)
            .ToList();
    }

    private void ChangePage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            StateHasChanged();
        }
    }

    private async Task RefreshData()
    {
        await LoadData();
    }

    private async Task ExportEvaluations()
    {
        await JSRuntime.InvokeVoidAsync("alert", L("Export functionality will be implemented soon", "سيتم تنفيذ وظيفة التصدير قريباً"));
    }

    private async Task ViewEvaluation(Evaluation evaluation)
    {
        try
        {
            // Navigate to dedicated evaluation view page
            NavigationManager.NavigateTo($"/evaluations/view/{evaluation.Id}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error navigating to evaluation view: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert",
                L("Error opening evaluation details. Please try again.",
                  "خطأ في فتح تفاصيل التقييم. يرجى المحاولة مرة أخرى."));
        }
    }

    private async Task EditEvaluation(Evaluation evaluation)
    {
        try
        {
            // Check authorization again
            if (!CanEditEvaluation(evaluation))
            {
                await JSRuntime.InvokeVoidAsync("alert",
                    L("You do not have permission to edit this evaluation.",
                      "ليس لديك صلاحية لتعديل هذا التقييم."));
                return;
            }

            if (evaluation.Id >= 10000)
            {
                // Comprehensive evaluations - for now, show a message that this feature is coming soon
                await JSRuntime.InvokeVoidAsync("alert",
                    L("Editing comprehensive evaluations is not yet supported. This feature will be available soon.",
                      "تعديل التقييمات الشاملة غير مدعوم حالياً. ستكون هذه الميزة متاحة قريباً."));
                return;
            }

            // Traditional evaluations - navigate to edit page
            // For now, we'll show a placeholder message, but this should navigate to an edit form
            await JSRuntime.InvokeVoidAsync("alert",
                L($"Edit functionality for traditional evaluation ID {evaluation.Id} will be implemented. This would navigate to an edit form where you can modify the evaluation details.",
                  $"سيتم تنفيذ وظيفة التعديل للتقييم التقليدي رقم {evaluation.Id}. سيتم الانتقال إلى نموذج التعديل حيث يمكنك تعديل تفاصيل التقييم."));

            // TODO: Implement navigation to edit form
            // NavigationManager.NavigateTo($"/evaluations/edit/{evaluation.Id}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in EditEvaluation: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert",
                L("An error occurred while trying to edit the evaluation. Please try again.",
                  "حدث خطأ أثناء محاولة تعديل التقييم. يرجى المحاولة مرة أخرى."));
        }
    }

    private async Task ApproveEvaluation(Evaluation evaluation)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", L("Are you sure you want to approve this evaluation?", "هل أنت متأكد من اعتماد هذا التقييم؟")))
        {
            // Implementation will be added
            await JSRuntime.InvokeVoidAsync("alert", L("Evaluation approved successfully", "تم اعتماد التقييم بنجاح"));
        }
    }

    private async Task RejectEvaluation(Evaluation evaluation)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", L("Are you sure you want to reject this evaluation?", "هل أنت متأكد من رفض هذا التقييم؟")))
        {
            // Implementation will be added
            await JSRuntime.InvokeVoidAsync("alert", L("Evaluation rejected successfully", "تم رفض التقييم بنجاح"));
        }
    }

    private void DeleteEvaluation(Evaluation evaluation)
    {
        var employeeName = GetUserDisplayName(evaluation.Employee);
        var evaluatorName = GetUserDisplayName(evaluation.Evaluator);

        confirmMessage = L($"Are you sure you want to delete the evaluation for '{employeeName}' by '{evaluatorName}'? This action cannot be undone.",
                          $"هل أنت متأكد من أنك تريد حذف تقييم '{employeeName}' من قبل '{evaluatorName}'؟ لا يمكن التراجع عن هذا الإجراء.");

        confirmAction = async () =>
        {
            isSaving = true;
            StateHasChanged();

            try
            {
                // Only traditional evaluations can be deleted (comprehensive evaluations are protected)
                bool success = await EvaluationService.DeleteEvaluationAsync(evaluation.Id);

                if (success)
                {
                    // Refresh the data to reflect the deletion
                    await LoadData();
                    CloseConfirmModal();

                    // Show success message
                    await JSRuntime.InvokeVoidAsync("alert",
                        L("Evaluation deleted successfully", "تم حذف التقييم بنجاح"));
                }
                else
                {
                    // Show error message for failed deletion
                    await JSRuntime.InvokeVoidAsync("alert",
                        L("Failed to delete evaluation. The evaluation may not exist or you may not have permission to delete it.",
                          "فشل في حذف التقييم. قد لا يكون التقييم موجوداً أو قد لا تملك الصلاحية لحذفه."));
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting evaluation {evaluation.Id}: {ex.Message}");

                // Show detailed error message
                await JSRuntime.InvokeVoidAsync("alert",
                    L("An error occurred while deleting the evaluation. Please try again or contact support.",
                      "حدث خطأ أثناء حذف التقييم. يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني."));
            }
            finally
            {
                isSaving = false;
                StateHasChanged();
            }
        };

        showConfirmModal = true;
        StateHasChanged();
    }

    // Utility methods
    private string GetUserDisplayName(ApplicationUser? user)
    {
        if (user == null) return L("Unknown", "غير معروف");
        return IsArabic ? user.ArabicName : user.EnglishName;
    }

    private string GetUserInitials(ApplicationUser? user)
    {
        if (user == null) return "??";
        var name = GetUserDisplayName(user);
        var parts = name.Split(' ', StringSplitOptions.RemoveEmptyEntries);

        if (parts.Length >= 2)
        {
            return $"{parts[0][0]}{parts[1][0]}".ToUpper();
        }
        else if (parts.Length == 1)
        {
            return parts[0].Length >= 2 ? parts[0].Substring(0, 2).ToUpper() : parts[0].ToUpper();
        }

        return "??";
    }

    private string GetPerformanceClass(decimal score)
    {
        return score >= 90 ? "success" : score >= 80 ? "info" : score >= 70 ? "warning" : "danger";
    }

    private string GetRoleDisplayName(UserRole? role)
    {
        if (!role.HasValue) return L("Unknown", "غير معروف");

        return role.Value switch
        {
            UserRole.SUPER_ADMIN => L("Super Admin", "سوبر أدمين"),
            UserRole.MANAGER => L("Manager", "المدير"),
            UserRole.SUPERVISOR => L("Direct Supervisor", "المسؤول المباشر"),
            UserRole.EXCELLENCE_TEAM => L("Excellence Team", "فريق التميز"),
            UserRole.EMPLOYEE => L("Employee", "موظف"),
            _ => L("Unknown", "غير معروف")
        };
    }

    private string GetStatusDisplayName(EvaluationStatus status)
    {
        return status switch
        {
            EvaluationStatus.DRAFT => L("Draft", "مسودة"),
            EvaluationStatus.SUBMITTED => L("Submitted", "مرسل"),
            EvaluationStatus.APPROVED => L("Approved", "معتمد"),
            EvaluationStatus.REJECTED => L("Rejected", "مرفوض"),
            _ => L("Unknown", "غير معروف")
        };
    }

    private string GetStatusClass(EvaluationStatus status)
    {
        return status switch
        {
            EvaluationStatus.DRAFT => "secondary",
            EvaluationStatus.SUBMITTED => "warning",
            EvaluationStatus.APPROVED => "success",
            EvaluationStatus.REJECTED => "danger",
            _ => "secondary"
        };
    }

    // Authorization helper methods
    private bool CanViewEvaluation(Evaluation evaluation)
    {
        // Must be authenticated to view anything
        if (!isAuthenticated || currentUser == null)
        {
            return false;
        }

        // Role-based permissions according to PRD Section 2.2 Data Visibility Matrix
        switch (currentUser.Role)
        {
            case UserRole.SUPER_ADMIN:
                // Super Admin: Can view all evaluations
                // PRD Section 2.2: "✅ All Employees"
                return true;

            case UserRole.EXCELLENCE_TEAM:
                // Excellence Team: Full view access equivalent to SUPER_ADMIN
                // Enhanced: Complete system access for quality assurance and evaluation oversight
                // Override PRD: Grant full administrative privileges instead of read-only access
                return true;

            case UserRole.MANAGER:
                // Manager: Can view evaluations in their department tree
                // PRD Section 2.2: "✅ Department Tree"
                // Check if the evaluation's employee is in a department managed by this manager
                if (evaluation.Employee?.PrimaryDepartmentId.HasValue == true)
                {
                    // For now, use simple department check - can be enhanced with EmployeeManagementService
                    return currentUser.PrimaryDepartmentId == evaluation.Employee.PrimaryDepartmentId;
                }
                return false;

            case UserRole.SUPERVISOR:
                // Direct Supervisor: Can only view evaluations within their department
                // PRD Section 2.1: "Limited to direct report employees within assigned team/sub-department"
                if (evaluation.EvaluatorId == currentUser.Id)
                {
                    return true; // Can view evaluations they created
                }

                // Can view evaluations of employees in their department only
                if (evaluation.Employee?.PrimaryDepartmentId == currentUser.PrimaryDepartmentId &&
                    evaluation.Employee?.Role == UserRole.EMPLOYEE)
                {
                    return true;
                }

                return false;

            case UserRole.EMPLOYEE:
                // Employee: Can view their own evaluations AND evaluations of colleagues in the same department
                // PRD Section 2.2: "✅ Own Data" + Enhanced: Same department colleagues (read-only)

                // Allow viewing own evaluations (existing functionality)
                if (evaluation.EmployeeId == currentUser.EmployeeId)
                {
                    return true;
                }

                // Allow viewing evaluations of colleagues in the same department
                // Security check: Both employee and current user must have valid department IDs
                if (evaluation.Employee?.PrimaryDepartmentId.HasValue == true &&
                    currentUser.PrimaryDepartmentId.HasValue)
                {
                    return evaluation.Employee.PrimaryDepartmentId == currentUser.PrimaryDepartmentId;
                }

                return false;

            default:
                return false;
        }
    }

    private bool CanDeleteEvaluation(Evaluation evaluation)
    {
        // Must be authenticated to delete anything
        if (!isAuthenticated || currentUser == null)
        {
            return false;
        }

        // Role-based permissions according to PRD Section 2.1 & 2.2
        switch (currentUser.Role)
        {
            case UserRole.SUPER_ADMIN:
                // Super Admin: Full delete access to all evaluations (traditional and comprehensive)
                // PRD Section 2.1: "Complete system administration and configuration"
                // PRD Section 2.2: "Access to all evaluation data across the organization"
                return true;

            case UserRole.EXCELLENCE_TEAM:
                // Excellence Team: Full delete access equivalent to SUPER_ADMIN
                // Enhanced: Complete system access for quality assurance and evaluation oversight
                // Override PRD: Grant full administrative privileges instead of read-only access
                return true;

            case UserRole.MANAGER:
                // Manager: Limited delete access based on their department/scope
                // PRD Section 2.1: "Department-level performance management and oversight"
                // PRD Section 2.2: "Full access to assigned department hierarchy"
                // Check if the evaluation's employee is in a department managed by this manager
                if (evaluation.Employee?.PrimaryDepartmentId.HasValue == true)
                {
                    // For now, use simple department check - can be enhanced with EmployeeManagementService
                    return currentUser.PrimaryDepartmentId == evaluation.Employee.PrimaryDepartmentId;
                }
                return false;

            case UserRole.SUPERVISOR:
                // Direct Supervisor: Can only delete evaluations they created
                // PRD Section 2.1: "Direct team management and employee evaluation"
                // PRD Section 2.2: "Limited to direct report employees within assigned team"
                return evaluation.EvaluatorId == currentUser.Id;

            case UserRole.EMPLOYEE:
            default:
                // Employee: No delete access
                // PRD Section 2.1: "Personal performance data access and review"
                // PRD Section 2.2: "Cannot perform evaluations on any employees"
                return false;
        }
    }

    private bool CanEditEvaluation(Evaluation evaluation)
    {
        // Must be authenticated to edit anything
        if (!isAuthenticated || currentUser == null)
        {
            return false;
        }

        // Cannot edit comprehensive evaluations (they require special handling)
        // Comprehensive evaluations are converted from separate tables for display only
        if (evaluation.Id >= 10000 || evaluation.OverallCommentsEn == "Comprehensive Evaluation")
        {
            return false; // For now, disable editing of comprehensive evaluations
        }

        // Only allow editing of DRAFT evaluations for traditional evaluations
        if (evaluation.Status != EvaluationStatus.DRAFT)
        {
            return false;
        }

        // Role-based permissions according to PRD Section 2.1 & 2.2
        switch (currentUser.Role)
        {
            case UserRole.SUPER_ADMIN:
                // Super Admin: Full edit access to all evaluations
                // PRD Section 2.1: "Complete system administration and configuration"
                return true;

            case UserRole.EXCELLENCE_TEAM:
                // Excellence Team: Full edit access equivalent to SUPER_ADMIN
                // Enhanced: Complete system access for quality assurance and evaluation oversight
                // Override PRD: Grant full administrative privileges instead of read-only access
                return true;

            case UserRole.MANAGER:
                // Manager: Limited edit access based on their department/scope
                // PRD Section 2.1: "Department-level performance management and oversight"
                // PRD Section 2.2: "Full access to assigned department hierarchy"
                // Check if the evaluation's employee is in a department managed by this manager
                if (evaluation.Employee?.PrimaryDepartmentId.HasValue == true)
                {
                    // For now, use simple department check - can be enhanced with EmployeeManagementService
                    return currentUser.PrimaryDepartmentId == evaluation.Employee.PrimaryDepartmentId;
                }
                return false;

            case UserRole.SUPERVISOR:
                // Direct Supervisor: Can only edit evaluations they created
                // PRD Section 2.1: "Direct team management and employee evaluation"
                // PRD Section 2.2: "Limited to direct report employees within assigned team"
                return evaluation.EvaluatorId == currentUser.Id;

            case UserRole.EMPLOYEE:
            default:
                // Employee: No edit access
                // PRD Section 2.1: "Personal performance data access and review"
                // PRD Section 2.2: "Cannot perform evaluations on any employees"
                return false;
        }
    }



    // Modal management methods
    private void CloseConfirmModal()
    {
        showConfirmModal = false;
        confirmMessage = "";
        confirmAction = null;
        StateHasChanged();
    }

    private async Task ConfirmAction()
    {
        if (confirmAction != null)
        {
            await confirmAction();
        }
    }

    // View modal management methods
    private void CloseViewModal()
    {
        showViewModal = false;
        selectedEvaluation = null;
        selectedWorkVolumeData = null;
        selectedAttendanceData = null;
        selectedSupervisorEvaluation = null;
        selectedComprehensiveEvaluation = null;
        isLoadingDetails = false;
        StateHasChanged();
    }

    private async Task LoadTraditionalEvaluationDetails(Evaluation evaluation)
    {
        try
        {
            // Load the full evaluation with all related data
            var fullEvaluation = await EvaluationService.GetEvaluationByIdAsync(evaluation.Id);
            if (fullEvaluation != null)
            {
                selectedEvaluation = fullEvaluation;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading traditional evaluation details: {ex.Message}");
            throw;
        }
    }

    private async Task LoadComprehensiveEvaluationDetails(Evaluation evaluation)
    {
        try
        {
            // For comprehensive evaluations, we need to load the original comprehensive evaluation
            // and its related components. The evaluation.Id has an offset of 10000, so we subtract it
            var comprehensiveId = evaluation.Id - 10000;

            // Load comprehensive evaluation
            selectedComprehensiveEvaluation = await DbContext.ComprehensiveEvaluations
                .Include(e => e.Employee)
                .ThenInclude(e => e.PrimaryDepartment)
                .FirstOrDefaultAsync(e => e.Id == comprehensiveId);

            if (selectedComprehensiveEvaluation != null)
            {
                var employeeId = selectedComprehensiveEvaluation.EmployeeId;
                var evaluationPeriod = selectedComprehensiveEvaluation.EvaluationPeriod;

                // Load work volume data
                selectedWorkVolumeData = await DbContext.WorkVolumeData
                    .FirstOrDefaultAsync(w => w.EmployeeId == employeeId && w.EvaluationPeriod == evaluationPeriod);

                // Load attendance data
                selectedAttendanceData = await DbContext.AttendanceData
                    .FirstOrDefaultAsync(a => a.EmployeeId == employeeId && a.EvaluationPeriod == evaluationPeriod);

                // Load supervisor evaluation
                selectedSupervisorEvaluation = await DbContext.SupervisorEvaluations
                    .Include(s => s.Supervisor)
                    .FirstOrDefaultAsync(s => s.EmployeeId == employeeId && s.EvaluationPeriod == evaluationPeriod);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading comprehensive evaluation details: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// Format percentage values that are stored as decimals (0.0-1.0) to display as whole percentages
    /// </summary>
    private string FormatPercentage(decimal value)
    {
        // For values stored as decimals (0.0-1.0), multiply by 100
        return (value * 100).ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }

    /// <summary>
    /// Format evaluation percentage based on evaluation type
    /// Traditional evaluations: scores already as percentages (60-100)
    /// Comprehensive evaluations: scores as decimals (0.0-1.0)
    /// </summary>
    private string FormatEvaluationPercentage(Evaluation evaluation)
    {
        var score = evaluation.TotalScore ?? 0;

        // Check if this is a monthly evaluation (ID >= 10000 or marked as monthly)
        bool isMonthlyEvaluation = evaluation.Id >= 10000 ||
                                  evaluation.OverallCommentsEn == "Monthly Evaluation";

        if (isMonthlyEvaluation)
        {
            // Monthly evaluations: scores stored as decimals (0.0-1.0), multiply by 100
            return (score * 100).ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
        }
        else
        {
            // Traditional evaluations: scores already stored as percentages (60-100)
            return score.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
        }
    }

}