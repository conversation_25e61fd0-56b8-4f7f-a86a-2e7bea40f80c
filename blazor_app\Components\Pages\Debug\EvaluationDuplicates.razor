@page "/debug/evaluation-duplicates"
@rendermode InteractiveServer
@using EmployeeRatingSystem.Blazor.Data
@using EmployeeRatingSystem.Blazor.Models
@using Microsoft.EntityFrameworkCore
@inject ApplicationDbContext DbContext
@inject IJSRuntime JSRuntime

<PageTitle>Evaluation Duplicates Analysis</PageTitle>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-search me-2"></i>
                        Evaluation Duplicates Analysis & Cleanup
                    </h4>
                </div>
                <div class="card-body">
                    @if (isLoading)
                    {
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Analyzing evaluation data...</p>
                        </div>
                    }
                    else
                    {
                        <!-- Summary Statistics -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h3>@totalTraditionalEvaluations</h3>
                                        <p class="mb-0">Traditional Evaluations</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h3>@totalComprehensiveEvaluations</h3>
                                        <p class="mb-0">Comprehensive Evaluations</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-dark">
                                    <div class="card-body text-center">
                                        <h3>@duplicateGroups.Count</h3>
                                        <p class="mb-0">Potential Duplicate Groups</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-danger text-white">
                                    <div class="card-body text-center">
                                        <h3>@totalDuplicateRecords</h3>
                                        <p class="mb-0">Duplicate Records</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <button class="btn btn-primary me-2" @onclick="RefreshAnalysis">
                                    <i class="fas fa-sync-alt me-1"></i>
                                    Refresh Analysis
                                </button>
                                @if (duplicateGroups.Any())
                                {
                                    <button class="btn btn-warning me-2" @onclick="ShowCleanupPreview">
                                        <i class="fas fa-eye me-1"></i>
                                        Preview Cleanup
                                    </button>
                                    <button class="btn btn-danger" @onclick="ConfirmCleanup" disabled="@isProcessing">
                                        <i class="fas fa-trash me-1"></i>
                                        @if (isProcessing)
                                        {
                                            <span class="spinner-border spinner-border-sm me-1"></span>
                                        }
                                        Clean Duplicates
                                    </button>
                                }
                            </div>
                        </div>

                        <!-- Duplicate Groups Analysis -->
                        @if (duplicateGroups.Any())
                        {
                            <div class="alert alert-warning">
                                <h5><i class="fas fa-exclamation-triangle me-2"></i>Duplicate Groups Found</h5>
                                <p>The following groups of evaluations appear to be duplicates based on Employee ID and Evaluation Period:</p>
                            </div>

                            @foreach (var group in duplicateGroups)
                            {
                                <div class="card mb-3 border-warning">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">
                                            <strong>Employee:</strong> @group.Key.EmployeeName (@group.Key.EmployeeId) | 
                                            <strong>Period:</strong> @group.Key.Period | 
                                            <strong>Count:</strong> @group.Value.Count records
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>ID</th>
                                                        <th>Type</th>
                                                        <th>Score</th>
                                                        <th>Status</th>
                                                        <th>Created</th>
                                                        <th>Evaluator</th>
                                                        <th>Action</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach (var eval in group.Value.OrderByDescending(e => e.CreatedAt))
                                                    {
                                                        <tr class="@(group.Value.First() == eval ? "table-success" : "table-danger")">
                                                            <td>@eval.Id</td>
                                                            <td>
                                                                @if (eval.Id >= 10000)
                                                                {
                                                                    <span class="badge bg-info">Comprehensive</span>
                                                                }
                                                                else
                                                                {
                                                                    <span class="badge bg-primary">Traditional</span>
                                                                }
                                                            </td>
                                                            <td>@eval.TotalScore?.ToString("F2")</td>
                                                            <td>
                                                                <span class="badge @GetStatusBadgeClass(eval.Status)">
                                                                    @eval.Status
                                                                </span>
                                                            </td>
                                                            <td>@eval.CreatedAt.ToString("yyyy-MM-dd HH:mm")</td>
                                                            <td>@(eval.Evaluator?.EnglishName ?? "Unknown")</td>
                                                            <td>
                                                                @if (group.Value.First() == eval)
                                                                {
                                                                    <span class="badge bg-success">Keep (Most Recent)</span>
                                                                }
                                                                else
                                                                {
                                                                    <span class="badge bg-danger">Remove</span>
                                                                }
                                                            </td>
                                                        </tr>
                                                    }
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            }
                        }
                        else
                        {
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle me-2"></i>No Duplicates Found</h5>
                                <p>The evaluation data appears to be clean with no duplicate records detected.</p>
                            </div>
                        }

                        <!-- Cleanup Results -->
                        @if (!string.IsNullOrEmpty(cleanupMessage))
                        {
                            <div class="alert @(cleanupSuccess ? "alert-success" : "alert-danger")">
                                <h5><i class="fas @(cleanupSuccess ? "fa-check-circle" : "fa-exclamation-circle") me-2"></i>Cleanup Results</h5>
                                <p>@cleanupMessage</p>
                            </div>
                        }
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private bool isLoading = true;
    private bool isProcessing = false;
    private int totalTraditionalEvaluations = 0;
    private int totalComprehensiveEvaluations = 0;
    private int totalDuplicateRecords = 0;
    private string cleanupMessage = "";
    private bool cleanupSuccess = false;

    private Dictionary<DuplicateKey, List<EvaluationInfo>> duplicateGroups = new();

    protected override async Task OnInitializedAsync()
    {
        await AnalyzeDuplicates();
    }

    private async Task RefreshAnalysis()
    {
        isLoading = true;
        cleanupMessage = "";
        StateHasChanged();
        await AnalyzeDuplicates();
    }

    private async Task AnalyzeDuplicates()
    {
        try
        {
            // Load traditional evaluations
            var traditionalEvaluations = await DbContext.Evaluations
                .Include(e => e.Employee)
                .Include(e => e.Evaluator)
                .Where(e => !e.IsDeleted)
                .Select(e => new EvaluationInfo
                {
                    Id = e.Id,
                    EmployeeId = e.EmployeeId,
                    EmployeeName = e.Employee.EnglishName,
                    EvaluatorName = e.Evaluator != null ? e.Evaluator.EnglishName : "Unknown",
                    TotalScore = e.TotalScore,
                    Status = e.Status,
                    CreatedAt = e.CreatedAt,
                    EvaluationPeriod = e.EvaluationPeriodStart.ToString("yyyy-MM"),
                    Type = "Traditional"
                })
                .ToListAsync();

            // Load comprehensive evaluations
            var comprehensiveEvaluations = await DbContext.ComprehensiveEvaluations
                .Include(e => e.Employee)
                .Select(e => new EvaluationInfo
                {
                    Id = e.Id + 10000, // Offset for display
                    EmployeeId = e.EmployeeId,
                    EmployeeName = e.Employee.EnglishName,
                    EvaluatorName = "System Calculated",
                    TotalScore = e.TotalScore,
                    Status = e.Status,
                    CreatedAt = e.CalculatedAt,
                    EvaluationPeriod = e.EvaluationPeriod,
                    Type = "Comprehensive"
                })
                .ToListAsync();

            totalTraditionalEvaluations = traditionalEvaluations.Count;
            totalComprehensiveEvaluations = comprehensiveEvaluations.Count;

            // Combine and analyze for duplicates
            var allEvaluations = traditionalEvaluations.Concat(comprehensiveEvaluations).ToList();

            // Group by employee and period to find duplicates
            duplicateGroups = allEvaluations
                .GroupBy(e => new DuplicateKey 
                { 
                    EmployeeId = e.EmployeeId, 
                    EmployeeName = e.EmployeeName,
                    Period = e.EvaluationPeriod 
                })
                .Where(g => g.Count() > 1)
                .ToDictionary(g => g.Key, g => g.ToList());

            totalDuplicateRecords = duplicateGroups.Sum(g => g.Value.Count - 1); // Subtract 1 to keep the most recent

        }
        catch (Exception ex)
        {
            cleanupMessage = $"Error analyzing duplicates: {ex.Message}";
            cleanupSuccess = false;
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task ShowCleanupPreview()
    {
        await JSRuntime.InvokeVoidAsync("alert", 
            $"Cleanup Preview:\n\n" +
            $"• {duplicateGroups.Count} duplicate groups found\n" +
            $"• {totalDuplicateRecords} records will be removed\n" +
            $"• Most recent evaluation in each group will be preserved\n\n" +
            $"Click 'Clean Duplicates' to proceed with the cleanup.");
    }

    private async Task ConfirmCleanup()
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", 
            $"Are you sure you want to clean up {totalDuplicateRecords} duplicate evaluation records?\n\n" +
            $"This action cannot be undone. The most recent evaluation in each duplicate group will be preserved.");

        if (confirmed)
        {
            await CleanupDuplicates();
        }
    }

    private async Task CleanupDuplicates()
    {
        isProcessing = true;
        StateHasChanged();

        try
        {
            int removedCount = 0;

            foreach (var group in duplicateGroups)
            {
                var evaluationsToRemove = group.Value
                    .OrderByDescending(e => e.CreatedAt)
                    .Skip(1) // Keep the most recent
                    .ToList();

                foreach (var evalToRemove in evaluationsToRemove)
                {
                    if (evalToRemove.Type == "Traditional")
                    {
                        // Soft delete traditional evaluations
                        var evaluation = await DbContext.Evaluations.FindAsync(evalToRemove.Id);
                        if (evaluation != null)
                        {
                            evaluation.IsDeleted = true;
                            evaluation.DeletedAt = DateTime.UtcNow;
                            removedCount++;
                        }
                    }
                    else if (evalToRemove.Type == "Comprehensive")
                    {
                        // Hard delete comprehensive evaluations (they don't have soft delete)
                        var comprehensiveEval = await DbContext.ComprehensiveEvaluations.FindAsync(evalToRemove.Id - 10000);
                        if (comprehensiveEval != null)
                        {
                            DbContext.ComprehensiveEvaluations.Remove(comprehensiveEval);
                            removedCount++;
                        }
                    }
                }
            }

            await DbContext.SaveChangesAsync();

            cleanupMessage = $"Successfully cleaned up {removedCount} duplicate evaluation records.";
            cleanupSuccess = true;

            // Refresh the analysis
            await AnalyzeDuplicates();
        }
        catch (Exception ex)
        {
            cleanupMessage = $"Error during cleanup: {ex.Message}";
            cleanupSuccess = false;
        }
        finally
        {
            isProcessing = false;
            StateHasChanged();
        }
    }

    private string GetStatusBadgeClass(EvaluationStatus status)
    {
        return status switch
        {
            EvaluationStatus.DRAFT => "bg-secondary",
            EvaluationStatus.SUBMITTED => "bg-primary",
            EvaluationStatus.APPROVED => "bg-success",
            EvaluationStatus.REJECTED => "bg-danger",
            _ => "bg-light text-dark"
        };
    }

    public class DuplicateKey
    {
        public string EmployeeId { get; set; } = "";
        public string EmployeeName { get; set; } = "";
        public string Period { get; set; } = "";

        public override bool Equals(object? obj)
        {
            if (obj is DuplicateKey other)
            {
                return EmployeeId == other.EmployeeId && Period == other.Period;
            }
            return false;
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(EmployeeId, Period);
        }
    }

    public class EvaluationInfo
    {
        public int Id { get; set; }
        public string EmployeeId { get; set; } = "";
        public string EmployeeName { get; set; } = "";
        public string EvaluatorName { get; set; } = "";
        public decimal? TotalScore { get; set; }
        public EvaluationStatus Status { get; set; }
        public DateTime CreatedAt { get; set; }
        public string EvaluationPeriod { get; set; } = "";
        public string Type { get; set; } = "";
        public ApplicationUser? Evaluator { get; set; }
    }
}
