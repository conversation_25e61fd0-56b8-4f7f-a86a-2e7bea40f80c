Stack trace:
Frame         Function      Args
0007FFFFA8F0  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFA8F0, 0007FFFF97F0) msys-2.0.dll+0x1FEBA
0007FFFFA8F0  0002100467F9 (000000000000, 000000000000, 000210059F9C, 0007FFFFABC8) msys-2.0.dll+0x67F9
0007FFFFA8F0  000210046832 (0002102860A8, 0007FFFFA7A8, 0007FFFFA8F0, 0002102684E0) msys-2.0.dll+0x6832
0007FFFFA8F0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFA8F0  00021006918F (0007FFFFA900, 000000000003, 000000000000, 000000000000) msys-2.0.dll+0x2918F
0007FFFFABD0  00021006A49D (000000004000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
0007FFFFABD0  00021006A719 (000000000000, 0007FFFFAC78, 0007FFFFAE54, 0000FFFFFFFF) msys-2.0.dll+0x2A719
0007FFFFABD0  0002101949FB (000000000000, 0007FFFFAC78, 0007FFFFAE54, 0000FFFFFFFF) msys-2.0.dll+0x1549FB
0007FFFFABD0  00010042DE05 (000A00000004, 0007FFFFAE64, 00010040DE62, 000000000000) bash.exe+0x2DE05
0000FFFFFFFF  00010043C7B8 (0000000000C2, 22A700000000, 000A0009A710, 000A00089420) bash.exe+0x3C7B8
000000000070  00010043E97E (00000000000D, 000A00000001, 0002101797A2, 0007FFFFAE60) bash.exe+0x3E97E
000000000070  000100441DC6 (000700000001, 000000000000, 0007FFFFAF50, 000000000000) bash.exe+0x41DC6
000000000070  000100441FF6 (000200000000, 000A00000000, 0007FFFFB02C, 0007FFFFB028) bash.exe+0x41FF6
000000000001  000100443A43 (00020000001F, 000100000000, 0001004694D0, 000000000000) bash.exe+0x43A43
0000FFFFFFFF  000100419DD2 (7FFC4E053422, 000000000000, 000000000000, 000A001C89F0) bash.exe+0x19DD2
000A001C89F0  00010041C68A (000000000000, 000000000000, 000000000000, 000A001C89F0) bash.exe+0x1C68A
0000FFFFFFFF  000100417B07 (000000000000, 000000000000, 0001005240ED, 000A001C89F0) bash.exe+0x17B07
000A00191690  00010041ADAA (00021005AD1F, 000000000000, 000000000000, 000000000000) bash.exe+0x1ADAA
000A00191690  00010041C64F (0002101797A2, 000000000000, 000210268720, 000A00191690) bash.exe+0x1C64F
0000FFFFFFFF  000100417B07 (000A001C8D20, 000000000006, 000210268720, 000A00191690) bash.exe+0x17B07
0000FFFFFFFF  000100417F77 (0002101797A2, 000A00162BE0, 000000000020, 000A00191690) bash.exe+0x17F77
000000000000  00010041BEDE (00010000001F, 000000000390, 000A00191690, 000000000000) bash.exe+0x1BEDE
0000FFFFFFFF  00010041A903 (0007FFFFB9A0, 000A00099400, 0002100ACA38, 000A00191690) bash.exe+0x1A903
000000000001  00010046F6A5 (000100525C8C, 000700000004, 00010040DE62, 000A00098780) bash.exe+0x6F6A5
0000FFFFFFFF  00010043CA2E (0000000000C2, 000A00000000, 000A00098EF0, 000A00089420) bash.exe+0x3CA2E
000000000070  00010043E97E (000A00098E40, 000A00000001, 0002101797A2, 0007FFFFBCB0) bash.exe+0x3E97E
000000000070  000100441DC6 (000700000001, 000000000000, 0007FFFFBDA0, 000000000000) bash.exe+0x41DC6
000000000070  000100441FF6 (000A00000000, 000000000000, 000000000000, 000000000000) bash.exe+0x41FF6
000000000019  000100444C98 (000000000000, 000000000010, 000210268720, 000A00098D50) bash.exe+0x44C98
000000000019  00010043D957 (000000000010, 000000000000, 0002101949FB, 000A00098CD0) bash.exe+0x3D957
00000000000E  00010043DF23 (000A00098CF0, 00000000002D, 000A00097110, 000A00097060) bash.exe+0x3DF23
000100523F60  000100433A26 (000000098B80, 000000000001, 000000000000, 000000000000) bash.exe+0x33A26
End of stack trace (more stack frames may be present)
Loaded modules:
000100400000 bash.exe
7FFC50D90000 ntdll.dll
7FFC4EAE0000 KERNEL32.DLL
7FFC4DFF0000 KERNELBASE.dll
7FFC500D0000 USER32.dll
7FFC4E590000 win32u.dll
000210040000 msys-2.0.dll
7FFC4EFA0000 GDI32.dll
7FFC4E840000 gdi32full.dll
7FFC4E3D0000 msvcp_win.dll
7FFC4E470000 ucrtbase.dll
7FFC4EA20000 advapi32.dll
7FFC50B70000 msvcrt.dll
7FFC50CA0000 sechost.dll
7FFC4E970000 bcrypt.dll
7FFC4EDA0000 RPCRT4.dll
7FFC4D530000 CRYPTBASE.DLL
7FFC4E9A0000 bcryptPrimitives.dll
7FFC4F250000 IMM32.DLL
