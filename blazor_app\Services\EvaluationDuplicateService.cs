using EmployeeRatingSystem.Blazor.Data;
using EmployeeRatingSystem.Blazor.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EmployeeRatingSystem.Blazor.Services
{
    /// <summary>
    /// Service for detecting and handling duplicate evaluations
    /// </summary>
    public interface IEvaluationDuplicateService
    {
        Task<List<Evaluation>> RemoveDuplicatesAsync(List<Evaluation> evaluations);
        Task<List<DuplicateEvaluationGroup>> DetectDuplicatesAsync(List<Evaluation> evaluations);
        Task<int> CleanupDatabaseDuplicatesAsync();
    }

    public class EvaluationDuplicateService : IEvaluationDuplicateService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<EvaluationDuplicateService> _logger;

        public EvaluationDuplicateService(ApplicationDbContext context, ILogger<EvaluationDuplicateService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<List<Evaluation>> RemoveDuplicatesAsync(List<Evaluation> evaluations)
        {
            try
            {
                _logger.LogInformation($"Starting duplicate removal for {evaluations.Count} evaluations");

                var duplicateGroups = await DetectDuplicatesAsync(evaluations);
                var duplicateIds = new HashSet<int>();

                foreach (var group in duplicateGroups)
                {
                    // Keep the most recent evaluation (highest ID or latest date)
                    var keepEvaluation = group.Evaluations
                        .OrderByDescending(e => e.CreatedAt)
                        .ThenByDescending(e => e.Id)
                        .First();

                    // Mark others for removal
                    foreach (var duplicate in group.Evaluations.Where(e => e.Id != keepEvaluation.Id))
                    {
                        duplicateIds.Add(duplicate.Id);
                    }
                }

                var cleanedEvaluations = evaluations
                    .Where(e => !duplicateIds.Contains(e.Id))
                    .ToList();

                _logger.LogInformation($"Removed {duplicateIds.Count} duplicate evaluations, {cleanedEvaluations.Count} remaining");

                return cleanedEvaluations;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing duplicates from evaluation list");
                return evaluations; // Return original list on error
            }
        }

        public async Task<List<DuplicateEvaluationGroup>> DetectDuplicatesAsync(List<Evaluation> evaluations)
        {
            try
            {
                await Task.CompletedTask; // Satisfy async requirement
                var duplicateGroups = new List<DuplicateEvaluationGroup>();

                // Group evaluations by employee and evaluation period
                var groupedEvaluations = evaluations
                    .Where(e => e.Employee != null)
                    .GroupBy(e => new
                    {
                        EmployeeId = e.EmployeeId,
                        Year = GetEvaluationYear(e),
                        Month = GetEvaluationMonth(e),
                        DepartmentId = e.Employee.PrimaryDepartmentId
                    })
                    .Where(g => g.Count() > 1) // Only groups with duplicates
                    .ToList();

                foreach (var group in groupedEvaluations)
                {
                    var duplicateGroup = new DuplicateEvaluationGroup
                    {
                        EmployeeId = group.Key.EmployeeId,
                        EmployeeName = group.First().Employee?.GetDisplayName("en") ?? "Unknown",
                        EvaluationYear = group.Key.Year,
                        EvaluationMonth = group.Key.Month,
                        DepartmentId = group.Key.DepartmentId,
                        Evaluations = group.ToList(),
                        DuplicateCount = group.Count()
                    };

                    duplicateGroups.Add(duplicateGroup);
                }

                _logger.LogInformation($"Detected {duplicateGroups.Count} duplicate groups affecting {duplicateGroups.Sum(g => g.DuplicateCount)} evaluations");

                return duplicateGroups;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error detecting duplicates in evaluation list");
                return new List<DuplicateEvaluationGroup>();
            }
        }

        public async Task<int> CleanupDatabaseDuplicatesAsync()
        {
            try
            {
                _logger.LogInformation("Starting database duplicate cleanup");

                // Load all evaluations from database
                var allEvaluations = await _context.Evaluations
                    .Include(e => e.Employee)
                    .Where(e => !e.IsDeleted)
                    .ToListAsync();

                var duplicateGroups = await DetectDuplicatesAsync(allEvaluations);
                var removedCount = 0;

                foreach (var group in duplicateGroups)
                {
                    // Keep the most recent evaluation
                    var keepEvaluation = group.Evaluations
                        .OrderByDescending(e => e.CreatedAt)
                        .ThenByDescending(e => e.Id)
                        .First();

                    // Soft delete the duplicates
                    foreach (var duplicate in group.Evaluations.Where(e => e.Id != keepEvaluation.Id))
                    {
                        duplicate.IsDeleted = true;
                        duplicate.DeletedAt = DateTime.UtcNow;
                        removedCount++;
                    }
                }

                if (removedCount > 0)
                {
                    await _context.SaveChangesAsync();
                    _logger.LogInformation($"Successfully removed {removedCount} duplicate evaluations from database");
                }

                return removedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up database duplicates");
                throw;
            }
        }

        private int GetEvaluationYear(Evaluation evaluation)
        {
            // Try to get year from evaluation period, fallback to creation date
            if (evaluation.EvaluationPeriodStart != default(DateTime))
                return evaluation.EvaluationPeriodStart.Year;

            return evaluation.CreatedAt.Year;
        }

        private int GetEvaluationMonth(Evaluation evaluation)
        {
            // Try to get month from evaluation period, fallback to creation date
            if (evaluation.EvaluationPeriodStart != default(DateTime))
                return evaluation.EvaluationPeriodStart.Month;

            return evaluation.CreatedAt.Month;
        }
    }

    /// <summary>
    /// Represents a group of duplicate evaluations
    /// </summary>
    public class DuplicateEvaluationGroup
    {
        public string EmployeeId { get; set; } = string.Empty;
        public string EmployeeName { get; set; } = string.Empty;
        public int EvaluationYear { get; set; }
        public int EvaluationMonth { get; set; }
        public int? DepartmentId { get; set; }
        public List<Evaluation> Evaluations { get; set; } = new();
        public int DuplicateCount { get; set; }
    }
}
