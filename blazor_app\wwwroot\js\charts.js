// Chart.js utility functions for Employee Rating System - Updated to fix caching issues
// Global chart instances storage
window.chartInstances = {};

// Test function to verify Chart.js is loaded
window.testChartJs = function() {
    console.log('testChartJs called');
    if (typeof Chart === 'undefined') {
        console.error('Chart.js is NOT loaded!');
        return false;
    } else {
        console.log('Chart.js is loaded successfully. Version:', Chart.version);
        return true;
    }
};





// Show fallback chart when Chart.js fails
window.showFallbackChart = function(canvasId) {
    console.log(`Showing fallback chart for ${canvasId}`);

    // Find the canvas element
    const canvas = document.getElementById(canvasId);
    if (!canvas) {
        console.error(`Canvas element with id '${canvasId}' not found`);
        return;
    }

    // Find the parent container
    const container = canvas.closest('.chart-container');
    if (!container) {
        console.error(`Chart container not found for ${canvasId}`);
        return;
    }

    // Hide the canvas and show the fallback
    const fallback = container.querySelector('.css-chart-fallback');
    if (fallback) {
        canvas.style.display = 'none';
        fallback.style.display = 'block';
        console.log(`Fallback chart shown for ${canvasId}`);
    } else {
        console.error(`Fallback chart element not found for ${canvasId}`);
    }
};

// Enhanced default chart configuration for professional appearance
const defaultChartConfig = {
    responsive: true,
    maintainAspectRatio: false,
    layout: {
        padding: {
            top: 25,
            right: 25,
            bottom: 25,
            left: 25
        }
    },
    plugins: {
        legend: {
            position: 'bottom',
            labels: {
                padding: 20,
                usePointStyle: true,
                pointStyle: 'circle',
                font: {
                    size: 14,
                    family: "'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif",
                    weight: '600'
                },
                color: '#1F2937',
                boxWidth: 14,
                boxHeight: 14
            }
        },
        tooltip: {
            backgroundColor: 'rgba(17, 24, 39, 0.96)',
            titleColor: '#FFFFFF',
            bodyColor: '#F9FAFB',
            borderColor: '#1e3a8a',
            borderWidth: 1,
            cornerRadius: 8,
            displayColors: true,
            padding: 16,
            titleFont: {
                size: 15,
                weight: 'bold',
                family: "'Inter', 'Segoe UI', sans-serif"
            },
            bodyFont: {
                size: 14,
                family: "'Inter', 'Segoe UI', sans-serif"
            },
            callbacks: {
                label: function(context) {
                    let label = context.dataset.label || '';
                    if (label) {
                        label += ': ';
                    }
                    if (context.parsed.y !== null) {
                        label += Math.round(context.parsed.y * 100) / 100 + '%';
                    }
                    return label;
                }
            }
        }
    },
    animation: {
        duration: 1200,
        easing: 'easeInOutCubic'
    }
};

// Create Pie Chart
window.createPieChart = function(canvasId, labels, data, colors, title) {
    const ctx = document.getElementById(canvasId);
    if (!ctx) {
        console.error(`Canvas element with id '${canvasId}' not found`);
        return;
    }

    // Destroy existing chart if it exists
    if (window.chartInstances[canvasId]) {
        window.chartInstances[canvasId].destroy();
    }

    const config = {
        type: 'pie',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors,
                borderWidth: 2,
                borderColor: '#fff',
                hoverBorderWidth: 3,
                hoverBorderColor: '#fff'
            }]
        },
        options: {
            ...defaultChartConfig,
            plugins: {
                ...defaultChartConfig.plugins,
                title: {
                    display: true,
                    text: title,
                    font: {
                        size: 16,
                        weight: 'bold'
                    },
                    padding: 20
                }
            }
        }
    };

    window.chartInstances[canvasId] = new Chart(ctx, config);
};

// Create Enhanced Vertical Bar Chart with Professional Styling
window.createBarChart = function(canvasId, labels, data, title, color = '#1e3a8a') {
    console.log(`createBarChart called for ${canvasId}:`, { labels, data, title, color });

    // Check if Chart.js is available
    if (typeof Chart === 'undefined') {
        console.error('Chart.js is not loaded! Showing fallback chart.');
        window.showFallbackChart(canvasId);
        return;
    }

    const ctx = document.getElementById(canvasId);
    if (!ctx) {
        console.error(`Canvas element with id '${canvasId}' not found. Showing fallback chart.`);
        window.showFallbackChart(canvasId);
        return;
    }

    // Destroy existing chart if it exists
    if (window.chartInstances[canvasId]) {
        window.chartInstances[canvasId].destroy();
    }

    // Corporate Professional color palette with better contrast and accessibility
    const professionalColors = [
        '#1e3a8a', '#374151', '#065f46', '#7c2d12', '#6b7280',
        '#1e40af', '#4b5563', '#047857', '#92400e', '#9ca3af'
    ];

    // Use professional gradient colors for better visual hierarchy
    const backgroundColors = data.length > 1
        ? data.map((_, index) => professionalColors[index % professionalColors.length] + '15')
        : [color + '15'];

    const borderColors = data.length > 1
        ? data.map((_, index) => professionalColors[index % professionalColors.length])
        : [color];

    const config = {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: title,
                data: data,
                backgroundColor: backgroundColors,
                borderColor: borderColors,
                borderWidth: 2.5,
                borderRadius: {
                    topLeft: 6,
                    topRight: 6,
                    bottomLeft: 0,
                    bottomRight: 0
                },
                borderSkipped: false,
                hoverBackgroundColor: backgroundColors.map(c => c.replace('15', '25')),
                hoverBorderColor: borderColors,
                hoverBorderWidth: 3,
                barThickness: 'flex',
                maxBarThickness: 60
            }]
        },
        options: {
            ...defaultChartConfig,
            indexAxis: 'x', // Ensure vertical bars
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    grid: {
                        color: 'rgba(107, 114, 128, 0.15)',
                        lineWidth: 1,
                        drawBorder: false
                    },
                    ticks: {
                        font: {
                            size: 13,
                            family: "'Inter', 'Segoe UI', 'Roboto', sans-serif",
                            weight: '500'
                        },
                        color: '#4B5563',
                        padding: 12,
                        callback: function(value) {
                            return Math.round(value) + '%';
                        }
                    },
                    title: {
                        display: true,
                        text: 'Performance Score (%)',
                        font: {
                            size: 14,
                            weight: 'bold',
                            family: "'Inter', 'Segoe UI', sans-serif"
                        },
                        color: '#1F2937',
                        padding: 15
                    }
                },
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            size: 13,
                            family: "'Inter', 'Segoe UI', 'Roboto', sans-serif",
                            weight: '500'
                        },
                        color: '#4B5563',
                        maxRotation: 35,
                        padding: 12
                    },
                    title: {
                        display: labels.length > 3,
                        text: 'Employees',
                        font: {
                            size: 14,
                            weight: 'bold',
                            family: "'Inter', 'Segoe UI', sans-serif"
                        },
                        color: '#1F2937',
                        padding: 15
                    }
                }
            },
            plugins: {
                ...defaultChartConfig.plugins,
                title: {
                    display: true,
                    text: title,
                    font: {
                        size: 20,
                        weight: 'bold',
                        family: "'Inter', 'Segoe UI', 'Roboto', sans-serif"
                    },
                    color: '#111827',
                    padding: {
                        top: 20,
                        bottom: 30
                    }
                }
            }
        }
    };

    console.log(`Creating enhanced vertical bar chart for ${canvasId}`);

    try {
        window.chartInstances[canvasId] = new Chart(ctx, config);
        console.log(`Enhanced chart ${canvasId} created successfully`);

    } catch (error) {
        console.error(`Error creating chart ${canvasId}:`, error);
        console.log(`Showing fallback chart for ${canvasId}`);
        window.showFallbackChart(canvasId);
    }
};

// Create Line Chart
window.createLineChart = function(canvasId, labels, countData, scoreData, title) {
    const ctx = document.getElementById(canvasId);
    if (!ctx) {
        console.error(`Canvas element with id '${canvasId}' not found`);
        return;
    }

    // Destroy existing chart if it exists
    if (window.chartInstances[canvasId]) {
        window.chartInstances[canvasId].destroy();
    }

    const config = {
        type: 'line',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Number of Evaluations',
                    data: countData,
                    borderColor: '#1e3a8a',
                    backgroundColor: '#1e3a8a80',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4,
                    pointBackgroundColor: '#1e3a8a',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 5,
                    yAxisID: 'y'
                },
                {
                    label: 'Average Score',
                    data: scoreData,
                    borderColor: '#065f46',
                    backgroundColor: '#065f4680',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4,
                    pointBackgroundColor: '#065f46',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 5,
                    yAxisID: 'y1'
                }
            ]
        },
        options: {
            ...defaultChartConfig,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                x: {
                    display: true,
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            size: 11
                        }
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        font: {
                            size: 11
                        }
                    },
                    title: {
                        display: true,
                        text: 'Number of Evaluations'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    beginAtZero: true,
                    max: 100,
                    grid: {
                        drawOnChartArea: false,
                    },
                    ticks: {
                        font: {
                            size: 11
                        }
                    },
                    title: {
                        display: true,
                        text: 'Average Score (%)'
                    }
                }
            },
            plugins: {
                ...defaultChartConfig.plugins,
                title: {
                    display: true,
                    text: title,
                    font: {
                        size: 16,
                        weight: 'bold'
                    },
                    padding: 20
                }
            }
        }
    };

    window.chartInstances[canvasId] = new Chart(ctx, config);
};

// Create Doughnut Chart
window.createDoughnutChart = function(canvasId, labels, data, colors, title) {
    const ctx = document.getElementById(canvasId);
    if (!ctx) {
        console.error(`Canvas element with id '${canvasId}' not found`);
        return;
    }

    // Destroy existing chart if it exists
    if (window.chartInstances[canvasId]) {
        window.chartInstances[canvasId].destroy();
    }

    const config = {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors,
                borderWidth: 2,
                borderColor: '#fff',
                hoverBorderWidth: 3,
                hoverBorderColor: '#fff',
                cutout: '60%'
            }]
        },
        options: {
            ...defaultChartConfig,
            plugins: {
                ...defaultChartConfig.plugins,
                title: {
                    display: true,
                    text: title,
                    font: {
                        size: 16,
                        weight: 'bold'
                    },
                    padding: 20
                }
            }
        }
    };

    window.chartInstances[canvasId] = new Chart(ctx, config);
};

// Update all charts (called when filters change)
window.updateAllCharts = function() {
    // This function will be called from Blazor when data changes
    // Individual chart update functions will be called from Blazor
    console.log('Updating all charts...');
};

// Destroy all charts (cleanup function)
window.destroyAllCharts = function() {
    Object.keys(window.chartInstances).forEach(key => {
        if (window.chartInstances[key]) {
            window.chartInstances[key].destroy();
            delete window.chartInstances[key];
        }
    });
};

// Resize all charts (useful for responsive design)
window.resizeAllCharts = function() {
    Object.keys(window.chartInstances).forEach(key => {
        if (window.chartInstances[key]) {
            window.chartInstances[key].resize();
        }
    });
};

// Create Single Line Chart
window.createSingleLineChart = function(canvasId, labels, data, title, color = '#1e3a8a', fill = false, tension = 0.4) {
    const ctx = document.getElementById(canvasId);
    if (!ctx) {
        console.error(`Canvas element with id '${canvasId}' not found`);
        return;
    }

    // Destroy existing chart if it exists
    if (window.chartInstances[canvasId]) {
        window.chartInstances[canvasId].destroy();
    }

    const config = {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: title,
                data: data,
                borderColor: color,
                backgroundColor: fill ? color + '40' : 'transparent',
                borderWidth: 3,
                fill: fill,
                tension: tension,
                pointBackgroundColor: color,
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 5
            }]
        },
        options: {
            ...defaultChartConfig,
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        font: {
                            size: 11
                        }
                    }
                },
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            size: 11
                        }
                    }
                }
            },
            plugins: {
                ...defaultChartConfig.plugins,
                title: {
                    display: true,
                    text: title,
                    font: {
                        size: 16,
                        weight: 'bold'
                    },
                    padding: 20
                }
            }
        }
    };

    window.chartInstances[canvasId] = new Chart(ctx, config);
};

// Create Horizontal Bar Chart
window.createHorizontalBarChart = function(canvasId, labels, data, title, color = '#374151', xAxisLabel = '', yAxisLabel = '') {
    const ctx = document.getElementById(canvasId);
    if (!ctx) {
        console.error(`Canvas element with id '${canvasId}' not found`);
        return;
    }

    // Destroy existing chart if it exists
    if (window.chartInstances[canvasId]) {
        window.chartInstances[canvasId].destroy();
    }

    const config = {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: title,
                data: data,
                backgroundColor: color + '80',
                borderColor: color,
                borderWidth: 2,
                borderRadius: 4,
                borderSkipped: false,
            }]
        },
        options: {
            ...defaultChartConfig,
            indexAxis: 'y',
            scales: {
                x: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        font: {
                            size: 11
                        }
                    },
                    title: {
                        display: !!xAxisLabel,
                        text: xAxisLabel
                    }
                },
                y: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            size: 11
                        }
                    },
                    title: {
                        display: !!yAxisLabel,
                        text: yAxisLabel
                    }
                }
            },
            plugins: {
                ...defaultChartConfig.plugins,
                title: {
                    display: true,
                    text: title,
                    font: {
                        size: 16,
                        weight: 'bold'
                    },
                    padding: 20
                }
            }
        }
    };

    window.chartInstances[canvasId] = new Chart(ctx, config);
};

// Destroy single chart
window.destroyChart = function(canvasId) {
    if (window.chartInstances[canvasId]) {
        window.chartInstances[canvasId].destroy();
        delete window.chartInstances[canvasId];
    }
};

// Resize single chart
window.resizeChart = function(canvasId) {
    if (window.chartInstances[canvasId]) {
        window.chartInstances[canvasId].resize();
    }
};

// Export charts as images
window.exportChartsAsImages = function(filename = 'charts') {
    const chartCanvases = document.querySelectorAll('canvas[id*="Chart"]');
    const zip = new JSZip();

    chartCanvases.forEach((canvas, index) => {
        try {
            const dataURL = canvas.toDataURL('image/png');
            const base64Data = dataURL.split(',')[1];
            const chartName = canvas.id.replace('Chart', '');
            zip.file(`${chartName}_chart.png`, base64Data, { base64: true });
        } catch (error) {
            console.error(`Error exporting chart ${canvas.id}:`, error);
        }
    });

    zip.generateAsync({ type: 'blob' }).then(function(content) {
        const link = document.createElement('a');
        link.href = URL.createObjectURL(content);
        link.download = `${filename}.zip`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }).catch(function(error) {
        console.error('Error creating zip file:', error);
        // Fallback: export first chart only
        if (chartCanvases.length > 0) {
            const link = document.createElement('a');
            link.href = chartCanvases[0].toDataURL('image/png');
            link.download = `${filename}.png`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    });
};

// Print charts
window.printCharts = function() {
    const printWindow = window.open('', '_blank');
    const chartContainers = document.querySelectorAll('.chart-container');

    let printContent = `
        <html>
        <head>
            <title>Evaluation Reports Charts</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .chart-container { margin-bottom: 30px; page-break-inside: avoid; }
                .chart-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; text-align: center; }
                canvas { max-width: 100%; height: auto; }
                @media print {
                    .chart-container { page-break-inside: avoid; }
                }
            </style>
        </head>
        <body>
    `;

    chartContainers.forEach((container, index) => {
        const canvas = container.querySelector('canvas');
        const title = container.closest('.card').querySelector('.card-title')?.textContent || `Chart ${index + 1}`;

        if (canvas) {
            printContent += `
                <div class="chart-container">
                    <div class="chart-title">${title}</div>
                    <img src="${canvas.toDataURL('image/png')}" style="max-width: 100%; height: auto;" />
                </div>
            `;
        }
    });

    printContent += '</body></html>';

    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
};

// Toggle chart fullscreen
window.toggleChartFullscreen = function(chartId) {
    const chartContainer = document.getElementById(chartId).closest('.card');

    if (chartContainer.classList.contains('chart-fullscreen')) {
        // Exit fullscreen
        chartContainer.classList.remove('chart-fullscreen');
        document.body.style.overflow = '';

        // Find the original parent and move back
        const originalParent = document.querySelector(`[data-chart-id="${chartId}"]`);
        if (originalParent) {
            originalParent.appendChild(chartContainer);
        }
    } else {
        // Enter fullscreen
        chartContainer.setAttribute('data-chart-id', chartId);
        chartContainer.classList.add('chart-fullscreen');
        document.body.style.overflow = 'hidden';
        document.body.appendChild(chartContainer);

        // Add close button
        const closeBtn = document.createElement('button');
        closeBtn.innerHTML = '<i class="fas fa-times"></i> Close';
        closeBtn.className = 'btn btn-secondary position-absolute';
        closeBtn.style.top = '10px';
        closeBtn.style.right = '10px';
        closeBtn.style.zIndex = '10000';
        closeBtn.onclick = () => window.toggleChartFullscreen(chartId);
        chartContainer.appendChild(closeBtn);
    }

    // Resize chart after transition
    setTimeout(() => {
        if (window.chartInstances[chartId]) {
            window.chartInstances[chartId].resize();
        }
    }, 300);
};

// Handle window resize
window.addEventListener('resize', function() {
    setTimeout(() => {
        window.resizeAllCharts();
    }, 100);
});

// Handle escape key for fullscreen exit
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const fullscreenChart = document.querySelector('.chart-fullscreen');
        if (fullscreenChart) {
            const chartId = fullscreenChart.querySelector('canvas').id;
            window.toggleChartFullscreen(chartId);
        }
    }
});
