﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EmployeeRatingSystem.Blazor.Migrations
{
    /// <inheritdoc />
    public partial class AddEmployeeAuthenticationSystem : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "EmployeePreConfigurations",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    EmployeeId = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    AssignedRole = table.Column<string>(type: "TEXT", nullable: false),
                    PrimaryDepartmentId = table.Column<int>(type: "INTEGER", nullable: true),
                    IsDepartmentManager = table.Column<bool>(type: "INTEGER", nullable: false),
                    IsDepartmentSupervisor = table.Column<bool>(type: "INTEGER", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "TEXT", nullable: false),
                    UpdatedByUserId = table.Column<string>(type: "TEXT", nullable: true),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    EffectiveDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    ExpiryDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    IsDeleted = table.Column<bool>(type: "INTEGER", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EmployeePreConfigurations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_EmployeePreConfigurations_AspNetUsers_CreatedByUserId",
                        column: x => x.CreatedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_EmployeePreConfigurations_AspNetUsers_UpdatedByUserId",
                        column: x => x.UpdatedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_EmployeePreConfigurations_Departments_PrimaryDepartmentId",
                        column: x => x.PrimaryDepartmentId,
                        principalTable: "Departments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "EmployeePreConfigurationDepartments",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    EmployeePreConfigurationId = table.Column<int>(type: "INTEGER", nullable: false),
                    DepartmentId = table.Column<int>(type: "INTEGER", nullable: false),
                    RoleInDepartment = table.Column<string>(type: "TEXT", nullable: false),
                    IsManagementRole = table.Column<bool>(type: "INTEGER", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    IsDeleted = table.Column<bool>(type: "INTEGER", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EmployeePreConfigurationDepartments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_EmployeePreConfigurationDepartments_Departments_DepartmentId",
                        column: x => x.DepartmentId,
                        principalTable: "Departments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_EmployeePreConfigurationDepartments_EmployeePreConfigurations_EmployeePreConfigurationId",
                        column: x => x.EmployeePreConfigurationId,
                        principalTable: "EmployeePreConfigurations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_EmployeePreConfigurationDepartments_DepartmentId",
                table: "EmployeePreConfigurationDepartments",
                column: "DepartmentId");

            migrationBuilder.CreateIndex(
                name: "IX_EmployeePreConfigurationDepartments_EmployeePreConfigurationId_DepartmentId",
                table: "EmployeePreConfigurationDepartments",
                columns: new[] { "EmployeePreConfigurationId", "DepartmentId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_EmployeePreConfigurations_CreatedByUserId",
                table: "EmployeePreConfigurations",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_EmployeePreConfigurations_EmployeeId",
                table: "EmployeePreConfigurations",
                column: "EmployeeId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_EmployeePreConfigurations_PrimaryDepartmentId",
                table: "EmployeePreConfigurations",
                column: "PrimaryDepartmentId");

            migrationBuilder.CreateIndex(
                name: "IX_EmployeePreConfigurations_UpdatedByUserId",
                table: "EmployeePreConfigurations",
                column: "UpdatedByUserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "EmployeePreConfigurationDepartments");

            migrationBuilder.DropTable(
                name: "EmployeePreConfigurations");
        }
    }
}
