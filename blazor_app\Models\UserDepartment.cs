using System.ComponentModel.DataAnnotations;

namespace EmployeeRatingSystem.Blazor.Models
{
    /// <summary>
    /// Department roles enum for user-department relationships.
    /// </summary>
    public enum DepartmentRole
    {
        [Display(Name = "Employee")]
        EMPLOYEE,

        [Display(Name = "Team Lead")]
        TEAM_LEAD,

        [Display(Name = "Supervisor")]
        SUPERVISOR,

        [Display(Name = "Manager")]
        MANAGER,

        [Display(Name = "Department Head")]
        DEPARTMENT_HEAD
    }

    /// <summary>
    /// User-Department relationship model.
    /// Represents the many-to-many relationship between users and departments.
    /// Equivalent to Django's UserDepartment model from PRD.
    /// </summary>
    public class UserDepartment : BaseModel
    {
        /// <summary>
        /// User ID
        /// </summary>
        [Required]
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// User navigation property
        /// </summary>
        public virtual ApplicationUser User { get; set; } = null!;

        /// <summary>
        /// Department ID
        /// </summary>
        [Required]
        public int DepartmentId { get; set; }

        /// <summary>
        /// Department navigation property
        /// </summary>
        public virtual Department Department { get; set; } = null!;

        /// <summary>
        /// Whether this is the user's primary department
        /// </summary>
        [Display(Name = "Is Primary")]
        public bool IsPrimary { get; set; } = false;

        /// <summary>
        /// User's role in this specific department
        /// </summary>
        [Required]
        [Display(Name = "Role in Department")]
        public DepartmentRole RoleInDepartment { get; set; } = DepartmentRole.EMPLOYEE;

        /// <summary>
        /// Date when user was assigned to this department
        /// </summary>
        [Display(Name = "Assigned Date")]
        public DateTime AssignedDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Whether this assignment is currently active
        /// </summary>
        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Date when assignment was deactivated
        /// </summary>
        [Display(Name = "Deactivated Date")]
        public DateTime? DeactivatedDate { get; set; }

        /// <summary>
        /// Reason for deactivation
        /// </summary>
        [StringLength(500)]
        [Display(Name = "Deactivation Reason")]
        public string? DeactivationReason { get; set; }

        /// <summary>
        /// Deactivate this user-department relationship
        /// </summary>
        public void Deactivate(string? reason = null)
        {
            IsActive = false;
            DeactivatedDate = DateTime.UtcNow;
            DeactivationReason = reason;
        }

        /// <summary>
        /// Reactivate this user-department relationship
        /// </summary>
        public void Reactivate()
        {
            IsActive = true;
            DeactivatedDate = null;
            DeactivationReason = null;
        }

        /// <summary>
        /// Check if user has management role in this department
        /// </summary>
        public bool HasManagementRole()
        {
            return RoleInDepartment == DepartmentRole.MANAGER || 
                   RoleInDepartment == DepartmentRole.DEPARTMENT_HEAD ||
                   RoleInDepartment == DepartmentRole.SUPERVISOR;
        }

        /// <summary>
        /// Check if user can manage other users in this department
        /// </summary>
        public bool CanManageUsers()
        {
            return HasManagementRole() && IsActive;
        }

        public override string ToString()
        {
            return $"{User?.EnglishName} - {Department?.NameEn} ({RoleInDepartment})";
        }
    }
}
