@page "/test-login"

<h3>Test Login API</h3>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Test Login API Endpoint</h5>
                    
                    <form method="post" action="/api/auth/login">
                        <div class="mb-3">
                            <label for="employeeId" class="form-label">Employee ID:</label>
                            <input type="text" name="employeeId" class="form-control" id="employeeId" value="EMP001" required />
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">Password:</label>
                            <input type="password" name="password" class="form-control" id="password" value="Password123!" required />
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" name="rememberMe" class="form-check-input" id="rememberMe" value="true" />
                            <input type="hidden" name="rememberMe" value="false" />
                            <label class="form-check-label" for="rememberMe">Remember me</label>
                        </div>
                        
                        <input type="hidden" name="returnUrl" value="/dashboard" />
                        
                        <button type="submit" class="btn btn-primary">Test Login</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
