@page "/evaluations/view/{evaluationId:int}"
@rendermode InteractiveServer
@using EmployeeRatingSystem.Blazor.Components.Shared
@using EmployeeRatingSystem.Blazor.Services
@using EmployeeRatingSystem.Blazor.Models
@using EmployeeRatingSystem.Blazor.Data
@using Microsoft.EntityFrameworkCore
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Identity
@using System.Globalization
@inherits LocalizedComponentBase
@inject ApplicationDbContext DbContext
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserManager<ApplicationUser> UserManager
@inject IEvaluationService EvaluationService
@inject IEmployeeManagementService EmployeeManagementService
@inject NavigationManager NavigationManager

<PageTitle>@L("Evaluation Details", "تفاصيل التقييم")</PageTitle>

<!-- Page Header -->
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <!-- Breadcrumb Navigation -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="/dashboard" class="text-decoration-none">
                            <i class="fas fa-home @GetMarginEnd(1)"></i>
                            @L("Home", "الرئيسية")
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="/evaluations" class="text-decoration-none">
                            @L("Evaluations", "التقييمات")
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        @L("View Details", "عرض التفاصيل")
                    </li>
                </ol>
            </nav>

            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-start flex-wrap mb-4">
                <div class="flex-grow-1">
                    <h1 class="h2 mb-2">
                        <i class="fas fa-eye @GetMarginEnd() text-primary"></i>
                        @L("Evaluation Details", "تفاصيل التقييم")
                    </h1>
                    @if (selectedEvaluation != null)
                    {
                        <p class="lead mb-0">
                            @L("Detailed view of evaluation for", "عرض تفصيلي للتقييم الخاص بـ") 
                            <strong>@GetUserDisplayName(selectedEvaluation.Employee)</strong>
                        </p>
                    }
                </div>
                <div class="d-flex gap-2 flex-wrap">
                    <button class="btn btn-outline-secondary" @onclick="GoBack">
                        <i class="fas fa-arrow-left @GetMarginEnd(1)"></i>
                        @L("Back to Evaluations", "العودة للتقييمات")
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading State -->
@if (isLoading)
{
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-12 col-md-6 text-center py-5">
                <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                    <span class="visually-hidden">@L("Loading...", "جاري التحميل...")</span>
                </div>
                <p class="text-muted">@L("Loading evaluation details...", "جاري تحميل تفاصيل التقييم...")</p>
            </div>
        </div>
    </div>
}

<!-- Error State -->
@if (!string.IsNullOrEmpty(errorMessage))
{
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-12 col-md-8">
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle @GetMarginEnd(1)"></i>
                    @errorMessage
                </div>
                <div class="text-center">
                    <button class="btn btn-primary" @onclick="GoBack">
                        <i class="fas fa-arrow-left @GetMarginEnd(1)"></i>
                        @L("Back to Evaluations", "العودة للتقييمات")
                    </button>
                </div>
            </div>
        </div>
    </div>
}

<!-- Evaluation Content -->
@if (!isLoading && string.IsNullOrEmpty(errorMessage) && selectedEvaluation != null)
{
    <div class="container-fluid">
        <!-- Evaluation Type Detection and Display -->
        @if (isComprehensiveEvaluation)
        {
            <!-- Comprehensive Evaluation View -->
            @if (selectedComprehensiveEvaluation != null)
            {
                <div class="row">
                    <div class="col-12">
                        <div class="alert alert-info mb-4">
                            <i class="fas fa-info-circle @GetMarginEnd(1)"></i>
                            @L("This is a comprehensive evaluation with detailed work volume, attendance, and supervisor assessment data.", 
                               "هذا تقييم شهري يحتوي على بيانات تفصيلية لحجم العمل والحضور وتقييم المسؤول.")
                        </div>
                    </div>
                </div>
                
                <!-- Monthly Evaluation Header Card -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-line @GetMarginEnd(1)"></i>
                            @L("Monthly Evaluation Summary", "ملخص التقييم الشهري")
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">@L("Employee", "الموظف")</label>
                                    <div class="d-flex align-items-center">
                                        <div class="profile-avatar profile-avatar-sm @GetMarginEnd(2)">
                                            @GetUserInitials(selectedComprehensiveEvaluation.Employee)
                                        </div>
                                        <div>
                                            <div class="fw-bold">@GetUserDisplayName(selectedComprehensiveEvaluation.Employee)</div>
                                            <small class="text-muted">@selectedComprehensiveEvaluation.Employee?.EmployeeId</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">@L("Department", "القسم")</label>
                                    <div>@GetDepartmentDisplayName(selectedComprehensiveEvaluation.Employee?.PrimaryDepartment)</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">@L("Evaluation Period", "فترة التقييم")</label>
                                    <div>@selectedComprehensiveEvaluation.EvaluationPeriod</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">@L("Status", "الحالة")</label>
                                    <div>
                                        <span class="badge bg-success">
                                            @L("Completed", "مكتمل")
                                        </span>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">@L("Total Score", "الدرجة الإجمالية")</label>
                                    <div class="fs-4 fw-bold text-primary">
                                        @FormatComprehensiveScore(selectedComprehensiveEvaluation.TotalScore)%
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">@L("Department Rank", "ترتيب القسم")</label>
                                    <div class="fs-5 fw-bold text-warning">
                                        #@(selectedComprehensiveEvaluation.DepartmentRank ?? 0)
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Score Breakdown Cards -->
                <div class="row mb-4">
                    <!-- Work Volume Score -->
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-primary">
                            <div class="card-header bg-primary text-white text-center">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-briefcase @GetMarginEnd(1)"></i>
                                    @L("Work Volume (60%)", "حجم العمل (60%)")
                                </h6>
                            </div>
                            <div class="card-body text-center">
                                <div class="display-6 fw-bold text-primary mb-2">
                                    @FormatComprehensiveScore(selectedComprehensiveEvaluation.WorkVolumeScore)%
                                </div>
                                <small class="text-muted">
                                    @L("Weighted Score", "الدرجة المرجحة"): @FormatComprehensiveScore(selectedComprehensiveEvaluation.WorkVolumeScore * 0.6m)%
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Attendance Score -->
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-warning">
                            <div class="card-header bg-warning text-dark text-center">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-calendar-check @GetMarginEnd(1)"></i>
                                    @L("Attendance (20%)", "الحضور (20%)")
                                </h6>
                            </div>
                            <div class="card-body text-center">
                                <div class="display-6 fw-bold text-warning mb-2">
                                    @FormatComprehensiveScoreAsPercentage(selectedComprehensiveEvaluation.AttendanceScore)%
                                </div>
                                <small class="text-muted">
                                    @L("Weighted Score", "الدرجة المرجحة"): @FormatComprehensiveScoreAsPercentage(selectedComprehensiveEvaluation.AttendanceScore * 0.2m)%
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Supervisor Score -->
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-success">
                            <div class="card-header bg-success text-white text-center">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-user-tie @GetMarginEnd(1)"></i>
                                    @L("Supervisor (20%)", "المسؤول (20%)")
                                </h6>
                            </div>
                            <div class="card-body text-center">
                                <div class="display-6 fw-bold text-success mb-2">
                                    @FormatComprehensiveScore(selectedComprehensiveEvaluation.SupervisorScore)%
                                </div>
                                <small class="text-muted">
                                    @L("Weighted Score", "الدرجة المرجحة"): @FormatComprehensiveScore(selectedComprehensiveEvaluation.SupervisorScore * 0.2m)%
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Work Volume Details -->
                @if (selectedWorkVolumeData != null)
                {
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-briefcase @GetMarginEnd(1)"></i>
                                @L("Work Volume Details", "تفاصيل حجم العمل")
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 text-center">
                                    <div class="border rounded p-3 mb-2">
                                        <div class="h4 fw-bold text-primary">@selectedWorkVolumeData.QualityProgramWork</div>
                                        <small class="text-muted">@L("Quality Program", "برنامج الجودة")</small>
                                    </div>
                                </div>
                                <div class="col-md-3 text-center">
                                    <div class="border rounded p-3 mb-2">
                                        <div class="h4 fw-bold text-primary">@selectedWorkVolumeData.OracleWork</div>
                                        <small class="text-muted">@L("Oracle", "الأوراكل")</small>
                                    </div>
                                </div>
                                <div class="col-md-3 text-center">
                                    <div class="border rounded p-3 mb-2">
                                        <div class="h4 fw-bold text-primary">@selectedWorkVolumeData.DocumentedWork</div>
                                        <small class="text-muted">@L("Documented Work", "العمل الموثق")</small>
                                    </div>
                                </div>
                                <div class="col-md-3 text-center">
                                    <div class="border rounded p-3 mb-2 bg-light">
                                        <div class="h4 fw-bold text-success">@GetWorkVolumeTotal(selectedWorkVolumeData)</div>
                                        <small class="text-muted">@L("Total", "المجموع")</small>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle @GetMarginEnd(1)"></i>
                                        @L("Work Volume Percentage", "النسبة المئوية لحجم العمل"):
                                        <strong>@FormatComprehensiveScore(selectedComprehensiveEvaluation.WorkVolumePercentage)%</strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                }

                <!-- Attendance Details -->
                @if (selectedAttendanceData != null)
                {
                    <div class="card mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-calendar-check @GetMarginEnd(1)"></i>
                                @L("Attendance Details", "تفاصيل الحضور")
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 text-center">
                                    <div class="border rounded p-3 mb-2">
                                        <div class="h4 fw-bold text-warning">@selectedAttendanceData.TotalWorkingDays</div>
                                        <small class="text-muted">@L("Working Days", "أيام العمل")</small>
                                    </div>
                                </div>
                                <div class="col-md-4 text-center">
                                    <div class="border rounded p-3 mb-2">
                                        <div class="h4 fw-bold text-warning">@selectedAttendanceData.AttendanceDays</div>
                                        <small class="text-muted">@L("Attendance Days", "أيام الحضور")</small>
                                    </div>
                                </div>
                                <div class="col-md-4 text-center">
                                    <div class="border rounded p-3 mb-2 bg-light">
                                        <div class="h4 fw-bold text-success">@GetAttendancePercentageFormatted(selectedAttendanceData)%</div>
                                        <small class="text-muted">@L("Attendance Rate", "معدل الحضور")</small>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle @GetMarginEnd(1)"></i>
                                        @L("Attendance Percentage", "النسبة المئوية للحضور"):
                                        <strong>@FormatComprehensiveScoreAsPercentage(selectedComprehensiveEvaluation.AttendancePercentage)%</strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                }

                <!-- Supervisor Evaluation Details -->
                @if (selectedSupervisorEvaluation != null)
                {
                    <div class="card mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user-tie @GetMarginEnd(1)"></i>
                                @L("Supervisor Evaluation Details", "تفاصيل تقييم المسؤول")
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="border rounded p-3 text-center">
                                        <div class="h5 fw-bold text-success">@selectedSupervisorEvaluation.EfficiencyAndQualityScore</div>
                                        <small class="text-muted">@L("Efficiency & Quality", "الكفاءة والجودة")</small>
                                    </div>
                                </div>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="border rounded p-3 text-center">
                                        <div class="h5 fw-bold text-success">@selectedSupervisorEvaluation.LeadershipAbilityScore</div>
                                        <small class="text-muted">@L("Leadership Ability", "القدرة على القيادة")</small>
                                    </div>
                                </div>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="border rounded p-3 text-center">
                                        <div class="h5 fw-bold text-success">@selectedSupervisorEvaluation.PlanningAndInnovationScore</div>
                                        <small class="text-muted">@L("Planning & Innovation", "التخطيط والابتكار")</small>
                                    </div>
                                </div>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="border rounded p-3 text-center">
                                        <div class="h5 fw-bold text-success">@selectedSupervisorEvaluation.TeamworkParticipationScore</div>
                                        <small class="text-muted">@L("Teamwork Participation", "المشاركة في العمل الجماعي")</small>
                                    </div>
                                </div>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="border rounded p-3 text-center">
                                        <div class="h5 fw-bold text-success">@selectedSupervisorEvaluation.ResponsibilityAndPressureScore</div>
                                        <small class="text-muted">@L("Responsibility & Pressure", "تحمل المسؤولية والضغط")</small>
                                    </div>
                                </div>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="border rounded p-3 text-center">
                                        <div class="h5 fw-bold text-success">@selectedSupervisorEvaluation.EmergencyHandlingScore</div>
                                        <small class="text-muted">@L("Emergency Handling", "التعامل مع الطوارئ")</small>
                                    </div>
                                </div>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="border rounded p-3 text-center">
                                        <div class="h5 fw-bold text-success">@selectedSupervisorEvaluation.GeneralBehaviorScore</div>
                                        <small class="text-muted">@L("General Behavior", "السلوك العام")</small>
                                    </div>
                                </div>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="border rounded p-3 text-center">
                                        <div class="h5 fw-bold text-success">@selectedSupervisorEvaluation.RelationshipWithSuperiorsScore</div>
                                        <small class="text-muted">@L("Relations with Superiors", "العلاقة مع الرؤساء")</small>
                                    </div>
                                </div>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="border rounded p-3 text-center">
                                        <div class="h5 fw-bold text-success">@selectedSupervisorEvaluation.DisciplineAndCommitmentScore</div>
                                        <small class="text-muted">@L("Discipline & Commitment", "الانضباط والالتزام")</small>
                                    </div>
                                </div>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="border rounded p-3 text-center">
                                        <div class="h5 fw-bold text-success">@selectedSupervisorEvaluation.WorkDevelopmentScore</div>
                                        <small class="text-muted">@L("Work Development", "تطوير العمل")</small>
                                    </div>
                                </div>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="border rounded p-3 text-center bg-light">
                                        <div class="h4 fw-bold text-primary">@GetSupervisorTotalScore(selectedSupervisorEvaluation)</div>
                                        <small class="text-muted">@L("Total (50)", "المجموع (50)")</small>
                                    </div>
                                </div>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="border rounded p-3 text-center bg-success text-white">
                                        <div class="h4 fw-bold">@FormatComprehensiveScore(selectedSupervisorEvaluation.WeightedScore)%</div>
                                        <small>@L("20% Weighted Score", "الدرجة المرجحة 20%")</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Exceptional Work Section -->
                            @if (!string.IsNullOrEmpty(selectedSupervisorEvaluation.ExceptionalWork))
                            {
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="card border-warning">
                                            <div class="card-header bg-warning text-dark">
                                                <h6 class="card-title mb-0">
                                                    <i class="fas fa-star @GetMarginEnd(1)"></i>
                                                    @L("Exceptional Work", "العمل المميز")
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="border rounded p-3 bg-light">
                                                    @selectedSupervisorEvaluation.ExceptionalWork
                                                </div>

                                                <!-- Display Attachments for Exceptional Work -->
                                                <div class="mt-3">
                                                    <h6 class="fw-bold mb-2">
                                                        <i class="fas fa-paperclip @GetMarginEnd(1)"></i>
                                                        @L("Supporting Documents", "المستندات الداعمة")
                                                    </h6>
                                                    <EvaluationAttachmentsList SupervisorEvaluationId="@selectedSupervisorEvaluation.Id"
                                                                             AttachmentType="@EvaluationAttachmentTypes.ExceptionalWork"
                                                                             CanDeleteAttachment="false" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                }
            }
        }
        else
        {
            <!-- Traditional Evaluation View -->
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-info mb-4">
                        <i class="fas fa-info-circle @GetMarginEnd(1)"></i>
                        @L("This is a traditional evaluation with question-based assessment.", 
                           "هذا تقييم تقليدي يعتمد على الأسئلة والتقييم.")
                    </div>
                </div>
            </div>
            
            <!-- Traditional Evaluation Details -->
            @if (selectedEvaluation.Responses?.Any() == true)
            {
                <!-- Evaluation Header Card -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-clipboard-list @GetMarginEnd(1)"></i>
                            @L("Evaluation Information", "معلومات التقييم")
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">@L("Employee", "الموظف")</label>
                                    <div class="d-flex align-items-center">
                                        <div class="profile-avatar profile-avatar-sm @GetMarginEnd(2)">
                                            @GetUserInitials(selectedEvaluation.Employee)
                                        </div>
                                        <div>
                                            <div class="fw-bold">@GetUserDisplayName(selectedEvaluation.Employee)</div>
                                            <small class="text-muted">@selectedEvaluation.Employee?.EmployeeId</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">@L("Department", "القسم")</label>
                                    <div>@GetDepartmentDisplayName(selectedEvaluation.Employee?.PrimaryDepartment)</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">@L("Evaluator", "المقيم")</label>
                                    <div>@GetUserDisplayName(selectedEvaluation.Evaluator)</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">@L("Status", "الحالة")</label>
                                    <div>
                                        <span class="badge @GetStatusBadgeClass(selectedEvaluation.Status)">
                                            @GetStatusDisplayName(selectedEvaluation.Status)
                                        </span>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">@L("Total Score", "الدرجة الإجمالية")</label>
                                    <div class="fs-4 fw-bold text-primary">
                                        @FormatScore(selectedEvaluation.TotalScore)%
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if (selectedEvaluation.EvaluationPeriodStart != default && selectedEvaluation.EvaluationPeriodEnd != default)
                        {
                            <div class="row">
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">@L("Evaluation Period", "فترة التقييم")</label>
                                        <div>
                                            @LocalizationService.FormatDate(selectedEvaluation.EvaluationPeriodStart) -
                                            @LocalizationService.FormatDate(selectedEvaluation.EvaluationPeriodEnd)
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>

                <!-- Evaluation Questions and Responses -->
                @foreach (var categoryGroup in selectedEvaluation.Responses
                    .Where(r => r.Question?.Category != null)
                    .GroupBy(r => r.Question.Category)
                    .OrderBy(g => g.Key.Order))
                {
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-folder @GetMarginEnd(1) text-secondary"></i>
                                @GetCategoryDisplayName(categoryGroup.Key)
                            </h5>
                        </div>
                        <div class="card-body">
                            @foreach (var response in categoryGroup.OrderBy(r => r.Question.Order))
                            {
                                <div class="row mb-3 @(categoryGroup.Last() == response ? "" : "border-bottom pb-3")">
                                    <div class="col-md-8">
                                        <label class="form-label fw-bold">
                                            @GetQuestionDisplayText(response.Question)
                                        </label>
                                        @if (!string.IsNullOrEmpty(response.CommentsEn) || !string.IsNullOrEmpty(response.CommentsAr))
                                        {
                                            <div class="mt-2">
                                                <small class="text-muted">@L("Comments", "التعليقات"):</small>
                                                <div class="border rounded p-2 bg-light">
                                                    @response.GetComments(CultureInfo.CurrentCulture.Name.StartsWith("ar") ? "ar" : "en")
                                                </div>
                                            </div>
                                        }
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <div class="d-flex align-items-center justify-content-end">
                                            <span class="badge bg-primary fs-6 px-3 py-2">
                                                @FormatScore(response.Score) / @response.Question.MaxScore
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                }

                <!-- Overall Comments -->
                @if (!string.IsNullOrEmpty(selectedEvaluation.OverallCommentsEn) || !string.IsNullOrEmpty(selectedEvaluation.OverallCommentsAr))
                {
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-comment @GetMarginEnd(1)"></i>
                                @L("Overall Comments", "التعليقات العامة")
                            </h5>
                        </div>
                        <div class="card-body">
                            @if (!string.IsNullOrEmpty(selectedEvaluation.OverallCommentsEn))
                            {
                                <div class="mb-3">
                                    <label class="form-label fw-bold">@L("English Comments", "التعليقات بالإنجليزية")</label>
                                    <div class="border rounded p-3 bg-light">
                                        @selectedEvaluation.OverallCommentsEn
                                    </div>
                                </div>
                            }
                            @if (!string.IsNullOrEmpty(selectedEvaluation.OverallCommentsAr))
                            {
                                <div class="mb-3">
                                    <label class="form-label fw-bold">@L("Arabic Comments", "التعليقات بالعربية")</label>
                                    <div class="border rounded p-3 bg-light" dir="rtl">
                                        @selectedEvaluation.OverallCommentsAr
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                }
            }
            else
            {
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle @GetMarginEnd(1)"></i>
                    @L("No evaluation responses found for this evaluation.", "لم يتم العثور على إجابات تقييم لهذا التقييم.")
                </div>
            }
        }
    </div>
}

@code {
    [Parameter] public int EvaluationId { get; set; }

    // State management
    private bool isLoading = true;
    private string errorMessage = string.Empty;
    private bool isAuthenticated = false;
    private ApplicationUser? currentUser = null;

    // Evaluation data
    private Evaluation? selectedEvaluation = null;
    private ComprehensiveEvaluation? selectedComprehensiveEvaluation = null;
    private WorkVolumeData? selectedWorkVolumeData = null;
    private AttendanceData? selectedAttendanceData = null;
    private SupervisorEvaluation? selectedSupervisorEvaluation = null;
    private bool isComprehensiveEvaluation = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadCurrentUser();
        await LoadEvaluationDetails();
    }

    private async Task LoadCurrentUser()
    {
        try
        {
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            isAuthenticated = authState.User.Identity?.IsAuthenticated == true;

            if (isAuthenticated)
            {
                var userId = authState.User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
                if (!string.IsNullOrEmpty(userId))
                {
                    currentUser = await UserManager.FindByIdAsync(userId);
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading current user: {ex.Message}");
        }
    }

    private async Task LoadEvaluationDetails()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            StateHasChanged();

            // Check if user is authenticated
            if (!isAuthenticated || currentUser == null)
            {
                errorMessage = L("You must be logged in to view evaluation details.", "يجب تسجيل الدخول لعرض تفاصيل التقييم.");
                return;
            }

            // Determine evaluation type and load data
            if (EvaluationId >= 10000)
            {
                // This is a comprehensive evaluation
                isComprehensiveEvaluation = true;
                await LoadComprehensiveEvaluationDetails();
            }
            else
            {
                // This is a traditional evaluation
                isComprehensiveEvaluation = false;
                await LoadTraditionalEvaluationDetails();
            }

            // Check permissions after loading
            if (selectedEvaluation != null && !CanViewEvaluation(selectedEvaluation))
            {
                errorMessage = L("You do not have permission to view this evaluation.", "ليس لديك صلاحية لعرض هذا التقييم.");
                selectedEvaluation = null;
                selectedComprehensiveEvaluation = null;
                return;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading evaluation details: {ex.Message}");
            errorMessage = L("Error loading evaluation details. Please try again.", "خطأ في تحميل تفاصيل التقييم. يرجى المحاولة مرة أخرى.");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadTraditionalEvaluationDetails()
    {
        try
        {
            // Load the full evaluation with all related data
            selectedEvaluation = await EvaluationService.GetEvaluationByIdAsync(EvaluationId);
            
            if (selectedEvaluation == null)
            {
                errorMessage = L("Evaluation not found.", "التقييم غير موجود.");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading traditional evaluation details: {ex.Message}");
            throw;
        }
    }

    private async Task LoadComprehensiveEvaluationDetails()
    {
        try
        {
            // For comprehensive evaluations, the ID has an offset of 10000
            var comprehensiveId = EvaluationId - 10000;

            // Load comprehensive evaluation
            selectedComprehensiveEvaluation = await DbContext.ComprehensiveEvaluations
                .Include(e => e.Employee)
                .ThenInclude(e => e.PrimaryDepartment)
                .FirstOrDefaultAsync(e => e.Id == comprehensiveId);

            if (selectedComprehensiveEvaluation == null)
            {
                errorMessage = L("Comprehensive evaluation not found.", "التقييم الشهري غير موجود.");
                return;
            }

            // Create a virtual Evaluation object for permission checking
            selectedEvaluation = new Evaluation
            {
                Id = EvaluationId,
                EmployeeId = selectedComprehensiveEvaluation.EmployeeId,
                Employee = selectedComprehensiveEvaluation.Employee,
                EvaluatorId = selectedComprehensiveEvaluation.CalculatedBy,
                Status = selectedComprehensiveEvaluation.Status,
                CreatedAt = selectedComprehensiveEvaluation.CreatedAt,
                UpdatedAt = selectedComprehensiveEvaluation.UpdatedAt
            };

            // Load related data
            await LoadComprehensiveRelatedData(selectedComprehensiveEvaluation);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading comprehensive evaluation details: {ex.Message}");
            throw;
        }
    }

    private async Task LoadComprehensiveRelatedData(ComprehensiveEvaluation comprehensiveEval)
    {
        try
        {
            // Load work volume data
            selectedWorkVolumeData = await DbContext.WorkVolumeData
                .FirstOrDefaultAsync(w => w.EmployeeId == comprehensiveEval.EmployeeId && 
                                         w.EvaluationPeriod == comprehensiveEval.EvaluationPeriod);

            // Load attendance data
            selectedAttendanceData = await DbContext.AttendanceData
                .FirstOrDefaultAsync(a => a.EmployeeId == comprehensiveEval.EmployeeId && 
                                         a.EvaluationPeriod == comprehensiveEval.EvaluationPeriod);

            // Load supervisor evaluation
            selectedSupervisorEvaluation = await DbContext.SupervisorEvaluations
                .FirstOrDefaultAsync(s => s.EmployeeId == comprehensiveEval.EmployeeId && 
                                         s.EvaluationPeriod == comprehensiveEval.EvaluationPeriod);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading comprehensive related data: {ex.Message}");
            // Don't throw here, as the main evaluation data is still valid
        }
    }

    private void GoBack()
    {
        NavigationManager.NavigateTo("/evaluations");
    }

    // Permission checking method (copied from Index.razor)
    private bool CanViewEvaluation(Evaluation evaluation)
    {
        // Must be authenticated to view anything
        if (!isAuthenticated || currentUser == null)
        {
            return false;
        }

        // Role-based permissions according to PRD Section 2.2 Data Visibility Matrix
        switch (currentUser.Role)
        {
            case UserRole.SUPER_ADMIN:
                // Super Admin: Can view all evaluations
                return true;

            case UserRole.EXCELLENCE_TEAM:
                // Excellence Team: Full view access equivalent to SUPER_ADMIN
                return true;

            case UserRole.MANAGER:
                // Manager: Can view evaluations for employees in their department
                return evaluation.Employee?.PrimaryDepartmentId == currentUser.PrimaryDepartmentId;

            case UserRole.SUPERVISOR:
                // Direct Supervisor: Can only view evaluations they created for employees in their department
                // PRD Section 2.1: "Limited to direct report employees within assigned team/sub-department"
                if (evaluation.EvaluatorId == currentUser.Id)
                {
                    return true; // Can view evaluations they created
                }

                // Can view evaluations of employees in their department (for oversight)
                if (evaluation.Employee?.PrimaryDepartmentId == currentUser.PrimaryDepartmentId &&
                    evaluation.Employee?.Role == UserRole.EMPLOYEE)
                {
                    return true;
                }

                return false;

            case UserRole.EMPLOYEE:
                // Employee: Can view their own evaluations AND evaluations of colleagues in the same department
                
                // Allow viewing own evaluations
                if (evaluation.EmployeeId == currentUser.EmployeeId)
                {
                    return true;
                }

                // Allow viewing evaluations of colleagues in the same department
                if (evaluation.Employee?.PrimaryDepartmentId.HasValue == true &&
                    currentUser.PrimaryDepartmentId.HasValue)
                {
                    return evaluation.Employee.PrimaryDepartmentId == currentUser.PrimaryDepartmentId;
                }

                return false;

            default:
                return false;
        }
    }

    // Helper methods for display
    private string GetUserDisplayName(ApplicationUser? user)
    {
        if (user == null) return L("Unknown User", "مستخدم غير معروف");

        var isRtl = CultureInfo.CurrentCulture.Name.StartsWith("ar");
        return isRtl ?
            (!string.IsNullOrEmpty(user.ArabicName) ? user.ArabicName : user.EnglishName) :
            (!string.IsNullOrEmpty(user.EnglishName) ? user.EnglishName : user.ArabicName);
    }

    private string GetUserInitials(ApplicationUser? user)
    {
        if (user == null) return "??";

        var displayName = GetUserDisplayName(user);
        var parts = displayName.Split(' ', StringSplitOptions.RemoveEmptyEntries);

        if (parts.Length >= 2)
        {
            return $"{parts[0][0]}{parts[1][0]}".ToUpper();
        }
        else if (parts.Length == 1 && parts[0].Length >= 2)
        {
            return parts[0].Substring(0, 2).ToUpper();
        }

        return displayName.Length >= 2 ? displayName.Substring(0, 2).ToUpper() : displayName.ToUpper();
    }

    private string GetDepartmentDisplayName(Department? department)
    {
        if (department == null) return L("No Department", "بدون قسم");

        var isRtl = CultureInfo.CurrentCulture.Name.StartsWith("ar");
        return isRtl ?
            (!string.IsNullOrEmpty(department.NameAr) ? department.NameAr : department.NameEn) :
            (!string.IsNullOrEmpty(department.NameEn) ? department.NameEn : department.NameAr);
    }

    private string GetCategoryDisplayName(EvaluationCategory? category)
    {
        if (category == null) return L("Unknown Category", "فئة غير معروفة");

        var isRtl = CultureInfo.CurrentCulture.Name.StartsWith("ar");
        return isRtl ?
            (!string.IsNullOrEmpty(category.NameAr) ? category.NameAr : category.NameEn) :
            (!string.IsNullOrEmpty(category.NameEn) ? category.NameEn : category.NameAr);
    }

    private string GetQuestionDisplayText(EvaluationQuestion? question)
    {
        if (question == null) return L("Unknown Question", "سؤال غير معروف");

        var isRtl = CultureInfo.CurrentCulture.Name.StartsWith("ar");
        return isRtl ?
            (!string.IsNullOrEmpty(question.TextAr) ? question.TextAr : question.TextEn) :
            (!string.IsNullOrEmpty(question.TextEn) ? question.TextEn : question.TextAr);
    }

    private string GetStatusDisplayName(EvaluationStatus status)
    {
        return status switch
        {
            EvaluationStatus.DRAFT => L("Draft", "مسودة"),
            EvaluationStatus.SUBMITTED => L("Submitted", "مُرسل"),
            EvaluationStatus.APPROVED => L("Approved", "معتمد"),
            EvaluationStatus.REJECTED => L("Rejected", "مرفوض"),
            _ => L("Unknown", "غير معروف")
        };
    }

    private string GetStatusBadgeClass(EvaluationStatus status)
    {
        return status switch
        {
            EvaluationStatus.DRAFT => "bg-secondary",
            EvaluationStatus.SUBMITTED => "bg-warning",
            EvaluationStatus.APPROVED => "bg-success",
            EvaluationStatus.REJECTED => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string FormatScore(decimal? score)
    {
        if (!score.HasValue) return "0";

        // Check if this is a traditional evaluation with scores that need percentage conversion
        if (selectedEvaluation != null && selectedEvaluation.Id < 10000)
        {
            // Traditional evaluations: Check if score is in decimal format (0.0-1.0) or percentage format (0-100)
            if (score.Value <= 1.0m)
            {
                // Decimal format - convert to percentage
                return (score.Value * 100).ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
            }
            else
            {
                // Already in percentage format
                return score.Value.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
            }
        }

        // For comprehensive evaluations or other cases, format as is
        return score.Value.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }

    // Comprehensive evaluation helper methods
    private string FormatComprehensiveScore(decimal? score)
    {
        if (!score.HasValue) return "0";

        // Comprehensive evaluations store scores as decimals (0.0-1.0), convert to percentage
        if (score.Value <= 1.0m)
        {
            return (score.Value * 100).ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
        }
        else
        {
            // Already in percentage format
            return score.Value.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
        }
    }

    private decimal GetWorkVolumeTotal(WorkVolumeData workData)
    {
        return workData.QualityProgramWork + workData.OracleWork + workData.DocumentedWork;
    }

    private decimal GetAttendancePercentage(AttendanceData attendanceData)
    {
        if (attendanceData.TotalWorkingDays <= 0) return 0;
        return Math.Round((attendanceData.AttendanceDays / (decimal)attendanceData.TotalWorkingDays) * 100, 1);
    }

    private string GetAttendancePercentageFormatted(AttendanceData attendanceData)
    {
        var percentage = GetAttendancePercentage(attendanceData);
        return percentage.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }

    private string FormatComprehensiveScoreAsPercentage(decimal? score)
    {
        if (!score.HasValue) return "0";

        // Comprehensive evaluations store scores as decimals (0.0-1.0), convert to percentage
        decimal percentageValue;
        if (score.Value <= 1.0m)
        {
            percentageValue = score.Value * 100;
        }
        else
        {
            // Already in percentage format
            percentageValue = score.Value;
        }

        return percentageValue.ToString("F0", System.Globalization.CultureInfo.InvariantCulture);
    }

    private decimal GetSupervisorTotalScore(SupervisorEvaluation supervisorEval)
    {
        return supervisorEval.EfficiencyAndQualityScore + supervisorEval.LeadershipAbilityScore +
               supervisorEval.PlanningAndInnovationScore + supervisorEval.TeamworkParticipationScore +
               supervisorEval.ResponsibilityAndPressureScore + supervisorEval.EmergencyHandlingScore +
               supervisorEval.GeneralBehaviorScore + supervisorEval.RelationshipWithSuperiorsScore +
               supervisorEval.DisciplineAndCommitmentScore + supervisorEval.WorkDevelopmentScore;
    }
}
