using Microsoft.AspNetCore.Components;
using EmployeeRatingSystem.Blazor.Services;
using System.Globalization;

namespace EmployeeRatingSystem.Blazor.Components.Shared
{
    /// <summary>
    /// Base component that provides localization functionality to all derived components
    /// </summary>
    public abstract class LocalizedComponentBase : ComponentBase
    {
        [Inject] protected ILocalizationService LocalizationService { get; set; } = default!;

        /// <summary>
        /// Gets the current language code (en/ar)
        /// </summary>
        protected string CurrentLanguage => LocalizationService.CurrentLanguage;

        /// <summary>
        /// Checks if the current language is Arabic
        /// </summary>
        protected bool IsArabic => LocalizationService.IsArabic;

        /// <summary>
        /// Checks if the current language is RTL
        /// </summary>
        protected bool IsRTL => LocalizationService.IsRTL;

        /// <summary>
        /// Gets the current culture
        /// </summary>
        protected CultureInfo CurrentCulture => LocalizationService.GetCurrentCulture();

        /// <summary>
        /// Gets the text direction (rtl/ltr)
        /// </summary>
        protected string Direction => LocalizationService.GetDirection();

        /// <summary>
        /// Gets the text alignment (right/left)
        /// </summary>
        protected string TextAlign => LocalizationService.GetTextAlign();

        /// <summary>
        /// Gets localized text based on current language
        /// </summary>
        /// <param name="englishText">English text</param>
        /// <param name="arabicText">Arabic text</param>
        /// <returns>Localized text</returns>
        protected string L(string englishText, string arabicText)
        {
            return LocalizationService.GetLocalizedString(englishText, arabicText);
        }

        /// <summary>
        /// Gets localized text using predefined string tuples
        /// </summary>
        /// <param name="text">Tuple containing English and Arabic text</param>
        /// <returns>Localized text</returns>
        protected string L((string En, string Ar) text)
        {
            return LocalizedStrings.Get(text, IsArabic);
        }

        /// <summary>
        /// Gets localized page title
        /// </summary>
        /// <param name="englishTitle">English title</param>
        /// <param name="arabicTitle">Arabic title</param>
        /// <returns>Formatted page title</returns>
        protected string GetPageTitle(string englishTitle, string arabicTitle)
        {
            return LocalizationService.GetPageTitle(englishTitle, arabicTitle);
        }

        /// <summary>
        /// Formats a date according to current culture
        /// </summary>
        /// <param name="date">Date to format</param>
        /// <returns>Formatted date string</returns>
        protected string FormatDate(DateTime date)
        {
            return LocalizationService.FormatDate(date);
        }

        /// <summary>
        /// Formats a date with time according to current culture
        /// </summary>
        /// <param name="date">Date to format</param>
        /// <returns>Formatted date and time string</returns>
        protected string FormatDateTime(DateTime date)
        {
            return date.ToString("g", LocalizationService.GetCurrentCulture());
        }

        /// <summary>
        /// Formats time only according to current culture
        /// </summary>
        /// <param name="date">Date to extract time from</param>
        /// <returns>Formatted time string</returns>
        protected string FormatTime(DateTime date)
        {
            return date.ToString("t", LocalizationService.GetCurrentCulture());
        }

        /// <summary>
        /// Formats a date for display in tables (short format)
        /// </summary>
        /// <param name="date">Date to format</param>
        /// <returns>Short formatted date string</returns>
        protected string FormatDateShort(DateTime date)
        {
            return date.ToString("d", LocalizationService.GetCurrentCulture());
        }

        /// <summary>
        /// Formats a number according to current culture
        /// </summary>
        /// <param name="number">Number to format</param>
        /// <returns>Formatted number string</returns>
        protected string FormatNumber(decimal number)
        {
            return LocalizationService.FormatNumber(number);
        }

        /// <summary>
        /// Formats a percentage according to current culture
        /// </summary>
        /// <param name="percentage">Percentage to format (as decimal, e.g., 0.85 for 85%)</param>
        /// <returns>Formatted percentage string</returns>
        protected string FormatPercentage(decimal percentage)
        {
            return LocalizationService.FormatPercentage(percentage);
        }

        /// <summary>
        /// Gets the CSS class for RTL layout
        /// </summary>
        /// <returns>CSS class string</returns>
        protected string GetLayoutClass()
        {
            return IsRTL ? "rtl-layout" : "";
        }

        /// <summary>
        /// Gets the appropriate margin class for the current language direction
        /// </summary>
        /// <param name="size">Size (1-5)</param>
        /// <returns>CSS class for margin-end</returns>
        protected string GetMarginEnd(int size = 2)
        {
            return IsRTL ? $"ms-{size}" : $"me-{size}";
        }

        /// <summary>
        /// Gets the appropriate margin class for the current language direction
        /// </summary>
        /// <param name="size">Size (1-5)</param>
        /// <returns>CSS class for margin-start</returns>
        protected string GetMarginStart(int size = 2)
        {
            return IsRTL ? $"me-{size}" : $"ms-{size}";
        }

        /// <summary>
        /// Gets the appropriate padding class for the current language direction
        /// </summary>
        /// <param name="size">Size (1-5)</param>
        /// <returns>CSS class for padding-end</returns>
        protected string GetPaddingEnd(int size = 2)
        {
            return IsRTL ? $"ps-{size}" : $"pe-{size}";
        }

        /// <summary>
        /// Gets the appropriate padding class for the current language direction
        /// </summary>
        /// <param name="size">Size (1-5)</param>
        /// <returns>CSS class for padding-start</returns>
        protected string GetPaddingStart(int size = 2)
        {
            return IsRTL ? $"pe-{size}" : $"ps-{size}";
        }

        /// <summary>
        /// Gets the appropriate text alignment class
        /// </summary>
        /// <returns>CSS class for text alignment</returns>
        protected string GetTextStart()
        {
            return IsRTL ? "text-end" : "text-start";
        }

        /// <summary>
        /// Gets the appropriate text alignment class
        /// </summary>
        /// <returns>CSS class for text alignment</returns>
        protected string GetTextEnd()
        {
            return IsRTL ? "text-start" : "text-end";
        }

        /// <summary>
        /// Gets the appropriate float class
        /// </summary>
        /// <returns>CSS class for float-start</returns>
        protected string GetFloatStart()
        {
            return IsRTL ? "float-end" : "float-start";
        }

        /// <summary>
        /// Gets the appropriate float class
        /// </summary>
        /// <returns>CSS class for float-end</returns>
        protected string GetFloatEnd()
        {
            return IsRTL ? "float-start" : "float-end";
        }

        /// <summary>
        /// Gets the appropriate icon for the current language direction
        /// </summary>
        /// <param name="leftIcon">Icon for LTR languages</param>
        /// <param name="rightIcon">Icon for RTL languages</param>
        /// <returns>Appropriate icon class</returns>
        protected string GetDirectionalIcon(string leftIcon, string rightIcon)
        {
            return IsRTL ? rightIcon : leftIcon;
        }

        /// <summary>
        /// Gets common directional icons
        /// </summary>
        protected string GetArrowNext() => GetDirectionalIcon("fas fa-arrow-right", "fas fa-arrow-left");
        protected string GetArrowPrevious() => GetDirectionalIcon("fas fa-arrow-left", "fas fa-arrow-right");
        protected string GetChevronNext() => GetDirectionalIcon("fas fa-chevron-right", "fas fa-chevron-left");
        protected string GetChevronPrevious() => GetDirectionalIcon("fas fa-chevron-left", "fas fa-chevron-right");

        /// <summary>
        /// Switches the application language
        /// </summary>
        /// <param name="culture">Culture code (en/ar)</param>
        protected async Task SwitchLanguageAsync(string culture)
        {
            await LocalizationService.SetLanguageAsync(culture);
            // Reload the page to apply the new culture
            await InvokeAsync(() => StateHasChanged());
        }
    }
}
