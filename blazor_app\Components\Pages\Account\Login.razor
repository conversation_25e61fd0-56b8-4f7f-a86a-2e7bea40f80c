@page "/login"
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.Components.Authorization
@using EmployeeRatingSystem.Blazor.Models
@using EmployeeRatingSystem.Blazor.Components.Shared
@using EmployeeRatingSystem.Blazor.Services
@using System.ComponentModel.DataAnnotations

@inherits LocalizedComponentBase
@inject IEmployeeAuthenticationService EmployeeAuthService
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider

<PageTitle>@GetPageTitle("Login", "تسجيل الدخول")</PageTitle>

<div class="login-page">
    <div class="container-fluid h-100">
        <div class="row h-100">
            <!-- Left Side - Branding -->
            <div class="col-lg-6 d-none d-lg-flex align-items-center justify-content-center bg-primary">
                <div class="text-center text-white p-5">
                    <div class="mb-4">
                        <i class="fas fa-chart-line fa-5x mb-4"></i>
                        <h1 class="display-4 fw-bold">
                            @L("Employee Rating System", "نظام تقييم الموظفين")
                        </h1>
                        <p class="lead">
                            @L("Comprehensive performance evaluation platform", "منصة شاملة لتقييم الأداء")
                        </p>
                    </div>
                    <div class="row text-center">
                        <div class="col-4">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <h6>@L("User Management", "إدارة المستخدمين")</h6>
                        </div>
                        <div class="col-4">
                            <i class="fas fa-building fa-2x mb-2"></i>
                            <h6>@L("Departments", "الأقسام")</h6>
                        </div>
                        <div class="col-4">
                            <i class="fas fa-star fa-2x mb-2"></i>
                            <h6>@L("Evaluations", "التقييمات")</h6>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Login Form -->
            <div class="col-lg-6 d-flex align-items-center justify-content-center">
                <div class="login-form-container w-100" style="max-width: 400px;">
                    <div class="text-center mb-4">
                        <div class="d-lg-none mb-3">
                            <i class="fas fa-chart-line fa-3x text-primary"></i>
                        </div>
                        <h2 class="fw-bold">@L("Welcome Back", "مرحباً بعودتك")</h2>
                        <p class="text-muted">@L("Sign in to your account", "سجل دخولك إلى حسابك")</p>
                    </div>

                    @if (!string.IsNullOrEmpty(errorMessage))
                    {
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle @GetMarginEnd(1)"></i>
                            @errorMessage
                        </div>
                    }

                    <form method="post" action="/api/auth/login">
                        @if (!string.IsNullOrEmpty(returnUrl))
                        {
                            <input type="hidden" name="returnUrl" value="@returnUrl" />
                        }

                        <div class="mb-3">
                            <label for="employeeId" class="form-label">
                                <i class="fas fa-id-card @GetMarginEnd(1)"></i>
                                @L("Employee ID", "رقم الموظف")
                            </label>
                            <input type="text" name="employeeId" class="form-control form-control-lg"
                                   id="employeeId" placeholder="@L("Enter your Employee ID", "أدخل رقم الموظف")"
                                   value="@employeeId" required />
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock @GetMarginEnd(1)"></i>
                                @L("Password", "كلمة المرور")
                            </label>
                            <div class="input-group">
                                <input type="password" name="password" class="form-control form-control-lg"
                                       id="password" placeholder="@L("Enter your password", "أدخل كلمة المرور")" required />
                                <button type="button" class="btn btn-outline-secondary" id="togglePassword">
                                    <i class="fas fa-eye" id="toggleIcon"></i>
                                </button>
                            </div>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" name="rememberMe" class="form-check-input" id="rememberMe" value="true" />
                            <input type="hidden" name="rememberMe" value="false" />
                            <label class="form-check-label" for="rememberMe">
                                @L("Remember me", "تذكرني")
                            </label>
                        </div>

                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt @GetMarginEnd(1)"></i>
                                @L("Sign In", "تسجيل الدخول")
                            </button>
                        </div>
                    </form>

                    <div class="text-center">
                        <div class="mb-3">
                            <a href="/forgot-password" class="text-decoration-none">
                                @L("Forgot your password?", "نسيت كلمة المرور؟")
                            </a>
                        </div>

                        <!-- Language Switcher -->
                        <div class="border-top pt-3">
                            <small class="text-muted d-block mb-2">@L("Language", "اللغة")</small>
                            <LanguageSwitcher ShowDropdown="false" Size="sm" />
                        </div>
                    </div>

                    <!-- Demo Credentials Info -->
                    @if (showDemoInfo)
                    {
                        <div class="mt-4 p-3 bg-light rounded">
                            <h6 class="fw-bold mb-2">
                                <i class="fas fa-info-circle @GetMarginEnd(1)"></i>
                                @L("Demo Credentials", "بيانات تجريبية")
                            </h6>
                            <small class="text-muted">
                                @L("Super Admin", "مدير النظام"): EMP001<br />
                                @L("Manager", "المدير"): EMP002<br />
                                @L("Password", "كلمة المرور"): Password123!
                            </small>
                            <button type="button" class="btn btn-sm btn-outline-secondary @GetMarginStart(2)"
                                    @onclick="() => showDemoInfo = false">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    }
                    else
                    {
                        <div class="text-center mt-3">
                            <button type="button" class="btn btn-sm btn-link text-muted"
                                    @onclick="() => showDemoInfo = true">
                                @L("Show demo credentials", "إظهار البيانات التجريبية")
                            </button>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private string errorMessage = string.Empty;
    private string employeeId = string.Empty;
    private string? returnUrl;
    private bool showDemoInfo = false;

    protected override async Task OnInitializedAsync()
    {
        // Check if user is already authenticated
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true)
        {
            Navigation.NavigateTo("/dashboard");
        }

        // Get return URL and error from query parameters
        var uri = new Uri(Navigation.Uri);
        var query = Microsoft.AspNetCore.WebUtilities.QueryHelpers.ParseQuery(uri.Query);

        if (query.TryGetValue("returnUrl", out var returnUrlValue))
        {
            returnUrl = returnUrlValue.FirstOrDefault();
        }

        if (query.TryGetValue("error", out var errorValue))
        {
            var error = errorValue.FirstOrDefault();
            errorMessage = error switch
            {
                "validation" => L("Please fill in all required fields.", "يرجى ملء جميع الحقول المطلوبة."),
                "invalid" => L("Invalid Employee ID or password.", "رقم الموظف أو كلمة المرور غير صحيحة."),
                "lockedout" => L("Account is locked. Please contact administrator.", "الحساب مقفل. يرجى الاتصال بالمدير."),
                "system" => L("System error occurred. Please try again.", "حدث خطأ في النظام. يرجى المحاولة مرة أخرى."),
                _ => string.Empty
            };
        }
    }
}

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const toggleButton = document.getElementById('togglePassword');
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.getElementById('toggleIcon');

        if (toggleButton && passwordInput && toggleIcon) {
            toggleButton.addEventListener('click', function() {
                // Toggle password visibility
                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    toggleIcon.className = 'fas fa-eye-slash';
                } else {
                    passwordInput.type = 'password';
                    toggleIcon.className = 'fas fa-eye';
                }
            });
        }
    });
</script>