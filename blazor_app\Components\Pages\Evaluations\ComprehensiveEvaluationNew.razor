@page "/evaluations/comprehensive-new"
@page "/evaluations/comprehensive"
@rendermode InteractiveServer
@using EmployeeRatingSystem.Blazor.Components.Shared
@using EmployeeRatingSystem.Blazor.Services
@using EmployeeRatingSystem.Blazor.Models
@using EmployeeRatingSystem.Blazor.Data
@using Microsoft.EntityFrameworkCore
@using Microsoft.AspNetCore.Components.Authorization
@inherits LocalizedComponentBase
@inject ApplicationDbContext DbContext
@inject IJSRuntime JSRuntime
@inject IEvaluationCalculationService EvaluationCalculationService
@inject IEvaluationDataSeedingService EvaluationDataSeedingService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IEmployeeManagementService EmployeeManagementService
@inject IEvaluationAttachmentService EvaluationAttachmentService

<PageTitle>@L("Comprehensive Employee Evaluation", "التقييم الشهري للموظفين")</PageTitle>

<!-- Page Header -->
<div class="page-header @GetLayoutClass()">
    <nav aria-label="@L("Breadcrumb", "مسار التنقل")">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="/" class="text-decoration-none">@L("Home", "الرئيسية")</a>
            </li>
            <li class="breadcrumb-item">
                <a href="/evaluations" class="text-decoration-none">@L("Evaluations", "التقييمات")</a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                @L("Comprehensive Evaluation", "التقييم الشهري")
            </li>
        </ol>
    </nav>

    <div class="d-flex justify-content-between align-items-start flex-wrap">
        <div class="flex-grow-1">
            <h1 class="h1 mb-2">
                <i class="fas fa-calculator @GetMarginEnd() text-primary"></i>
                @L("Comprehensive Employee Evaluation", "التقييم الشهري للموظفين")
            </h1>
            <p class="lead mb-0">@L("Complete evaluation system with work volume, attendance, and supervisor assessment", "نظام التقييم الشهري مع حجم العمل والحضور وتقييم المسؤول")</p>
        </div>
    </div>
</div>

<!-- Evaluation Period Selection -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-calendar @GetMarginEnd(1)"></i>
            @L("Evaluation Period", "فترة التقييم")
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label class="form-label">@L("Year", "السنة")</label>
                <select class="form-select" @bind="selectedYear">
                    <option value="">@L("Select Year", "اختر السنة")</option>
                    @for (int year = DateTime.Now.Year; year >= DateTime.Now.Year - 5; year--)
                    {
                        <option value="@year.ToString()">@year</option>
                    }
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">@L("Month", "الشهر")</label>
                <select class="form-select" @bind="selectedMonth">
                    <option value="">@L("Select Month", "اختر الشهر")</option>
                    @for (int month = 1; month <= 12; month++)
                    {
                        <option value="@month.ToString()">@GetMonthName(month)</option>
                    }
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">@L("Department", "القسم")</label>
                @if (currentUser?.Role == UserRole.SUPERVISOR)
                {
                    <!-- Auto-selected department for supervisors (read-only) -->
                    <div class="input-group">
                        <select class="form-select" @bind="selectedDepartmentId" disabled>
                            @foreach (var dept in departments)
                            {
                                <option value="@dept.Id.ToString()" selected="@(dept.Id.ToString() == selectedDepartmentId)">
                                    @GetDepartmentName(dept)
                                </option>
                            }
                        </select>
                        <span class="input-group-text" title="@L("Auto-selected for your role", "محدد تلقائياً لدورك")">
                            <i class="fas fa-lock text-muted"></i>
                        </span>
                    </div>
                    <small class="text-muted">@L("Your department is automatically selected", "تم تحديد قسمك تلقائياً")</small>
                }
                else
                {
                    <!-- Normal department selection for other roles -->
                    <select class="form-select" @bind="selectedDepartmentId">
                        <option value="">@L("Select Department", "اختر القسم")</option>
                        @foreach (var dept in departments)
                        {
                            <option value="@dept.Id.ToString()">@GetDepartmentName(dept)</option>
                        }
                    </select>
                }
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button class="btn btn-primary w-100" @onclick="LoadEmployees" disabled="@IsButtonDisabled()">
                    <i class="fas fa-search @GetMarginEnd(1)"></i>
                    @L("Load Employees", "تحميل الموظفين")
                </button>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-3">
                <label class="form-label">@L("Monthly Working Days", "عدد أيام الشهر")</label>
                <select class="form-select" @bind="selectedMonthlyWorkingDays" @bind:after="UpdateAllMonthlyWorkingDays">
                    <option value="">@L("Select Working Days", "اختر أيام العمل")</option>
                    @for (int days = 5; days <= 31; days++)
                    {
                        <option value="@days.ToString()">@days @L("days", "يوم")</option>
                    }
                </select>
            </div>
            <div class="col-md-9">
                <div class="alert alert-info mb-0" role="alert">
                    <i class="fas fa-info-circle @GetMarginEnd(1)"></i>
                    @L("Selecting monthly working days will automatically update all employees in the attendance table below.", "اختيار أيام العمل الشهرية سيحدث تلقائياً جميع الموظفين في جدول الحضور أدناه.")
                </div>
            </div>
        </div>
    </div>
</div>



@if (employees.Any())
{
    <!-- Employee Search Section -->
    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-search @GetMarginEnd(1)"></i>
                @L("Employee Search", "البحث عن الموظفين")
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" @bind="searchQuery" @bind:event="oninput" @onkeyup="FilterEmployees"
                               placeholder="@L("Search by name or employee ID...", "البحث بالاسم أو رقم الموظف...")" />
                        @if (!string.IsNullOrEmpty(searchQuery))
                        {
                            <button class="btn btn-outline-secondary" type="button" @onclick="ClearSearch">
                                <i class="fas fa-times"></i>
                            </button>
                        }
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-flex align-items-center h-100">
                        <div class="text-muted">
                            <i class="fas fa-users @GetMarginEnd(1)"></i>
                            @L("Showing", "عرض") <strong>@filteredEmployees.Count</strong> @L("of", "من") <strong>@employees.Count</strong> @L("employees", "موظف")
                        </div>
                    </div>
                </div>
            </div>
            @if (!string.IsNullOrEmpty(searchQuery) && !filteredEmployees.Any())
            {
                <div class="alert alert-warning mt-3 mb-0">
                    <i class="fas fa-exclamation-triangle @GetMarginEnd(1)"></i>
                    @L("No employees found matching your search criteria.", "لم يتم العثور على موظفين يطابقون معايير البحث.")
                </div>
            }
        </div>
    </div>

    <!-- Section 1: Work Volume Input Table -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-briefcase @GetMarginEnd(1)"></i>
                @L("Section 1: Work Volume Metrics", "القسم الأول: مقاييس حجم العمل")
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-bordered mb-0" dir="@Direction">
                    <thead class="table-light">
                        <tr>
                            <th rowspan="2" class="text-center align-middle">@L("Employee", "الموظف")</th>
                            <th colspan="3" class="text-center">@L("Work Volume", "حجم العمل")</th>
                            <th rowspan="2" class="text-center align-middle">@L("Total", "المجموع")</th>
                            <th rowspan="2" class="text-center align-middle">@L("Employee Work Volume Percentage within Department", "النسبة المئوية لحجم عمل الموظف داخل القسم")</th>
                            <th rowspan="2" class="text-center align-middle">@L("60% Score", "درجة 60%")</th>
                        </tr>
                        <tr>
                            <th class="text-center">@L("Quality Program", "برنامج الجودة")</th>
                            <th class="text-center">@L("Oracle", "الأوراكل")</th>
                            <th class="text-center">@L("Documented Work", "العمل الموثق")</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var employee in filteredEmployees)
                        {
                            var workData = GetOrCreateWorkVolumeData(employee.Id);
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="profile-avatar profile-avatar-sm @GetMarginEnd(2)">
                                            @GetUserInitials(employee)
                                        </div>
                                        <div>
                                            <div class="fw-bold">@GetUserDisplayName(employee)</div>
                                            <small class="text-muted">@employee.EmployeeId</small>
                                        </div>
                                    </div>
                                </td>
                                <td class="text-center">
                                    <input type="number" @bind="workData.QualityProgramWork" @bind:event="oninput"
                                           class="form-control form-control-sm text-center" step="0.01" min="0" />
                                </td>
                                <td class="text-center">
                                    <input type="number" @bind="workData.OracleWork" @bind:event="oninput"
                                           class="form-control form-control-sm text-center" step="0.01" min="0" />
                                </td>
                                <td class="text-center">
                                    <input type="number" @bind="workData.DocumentedWork" @bind:event="oninput"
                                           class="form-control form-control-sm text-center" step="0.01" min="0" />
                                </td>
                                <td class="text-center">
                                    <strong>@workData.TotalEmployeeWork.ToString("F2")</strong>
                                </td>
                                <td class="text-center bg-light">
                                    <strong>@GetWorkVolumePercentageFormatted(workData)</strong>
                                </td>
                                <td class="text-center bg-info bg-opacity-25">
                                    <strong>@GetWorkVolumeScoreFormatted(workData)</strong>
                                </td>
                            </tr>
                        }
                        <!-- Department Totals Row -->
                        <tr class="table-warning">
                            <td><strong>@L("Department Totals", "مجاميع القسم")</strong></td>
                            <td class="text-center"><strong>@GetDepartmentQualityTotal().ToString("F2")</strong></td>
                            <td class="text-center"><strong>@GetDepartmentOracleTotal().ToString("F2")</strong></td>
                            <td class="text-center"><strong>@GetDepartmentDocumentedTotal().ToString("F2")</strong></td>
                            <td class="text-center"><strong>@GetDepartmentGrandTotal().ToString("F2")</strong></td>
                            <td class="text-center"><strong>100%</strong></td>
                            <td class="text-center">-</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Work Volume Comments Section -->
            <div class="card-footer">
                <div class="row">
                    <div class="col-12">
                        <h6 class="mb-3">
                            <button class="btn btn-link p-0 text-decoration-none fw-bold" type="button"
                                    data-bs-toggle="collapse" data-bs-target="#workVolumeComments"
                                    aria-expanded="false" aria-controls="workVolumeComments">
                                <i class="fas fa-comment @GetMarginEnd(1)"></i>
                                @L("Work Volume Comments & Notes", "تعليقات وملاحظات حجم العمل")
                                <i class="fas fa-chevron-down @GetMarginStart(1)"></i>
                            </button>
                        </h6>
                        <div class="collapse" id="workVolumeComments">
                        @foreach (var employee in filteredEmployees)
                        {
                            var workData = GetOrCreateWorkVolumeData(employee.Id);
                            <div class="mb-3">
                                <label class="form-label fw-bold">
                                    @GetUserDisplayName(employee) (@employee.EmployeeId)
                                </label>
                                <textarea @bind="workData.Comments" @bind:event="oninput"
                                          class="form-control" rows="2" maxlength="500"
                                          placeholder="@L("Add comments or notes about work volume metrics...", "أضف تعليقات أو ملاحظات حول مقاييس حجم العمل...")"></textarea>
                                <div class="d-flex justify-content-between">
                                    <small class="text-muted">@L("Maximum 500 characters", "الحد الأقصى 500 حرف")</small>
                                    <small class="text-muted">@((workData.Comments?.Length ?? 0))/500</small>
                                </div>
                            </div>
                        }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section 2: Attendance Data -->
    <div class="card mb-4">
        <div class="card-header bg-warning text-dark">
            <h5 class="card-title mb-0">
                <i class="fas fa-calendar-check @GetMarginEnd(1)"></i>
                @L("Attendance Metrics", "مقاييس الحضور")
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-bordered mb-0" dir="@Direction">
                    <thead class="table-light">
                        <tr>
                            <th class="text-center">@L("Employee", "الموظف")</th>
                            <th class="text-center">@L("Monthly Working Days", "أيام العمل الشهرية")</th>
                            <th class="text-center">@L("Employee Attendance Days", "أيام حضور الموظف")</th>
                            <th class="text-center">@L("Attendance Percentage", "نسبة الحضور")</th>
                            <th class="text-center">@L("20% Attendance Score", "درجة الحضور 20%")</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var employee in filteredEmployees)
                        {
                            var attendanceData = GetOrCreateAttendanceData(employee.Id);
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="profile-avatar profile-avatar-sm @GetMarginEnd(2)">
                                            @GetUserInitials(employee)
                                        </div>
                                        <div>
                                            <div class="fw-bold">@GetUserDisplayName(employee)</div>
                                            <small class="text-muted">@employee.EmployeeId</small>
                                        </div>
                                    </div>
                                </td>
                                <td class="text-center">
                                    <input type="number" @bind="attendanceData.TotalWorkingDays" @bind:event="oninput"
                                           class="form-control form-control-sm text-center" min="1" max="31" />
                                </td>
                                <td class="text-center">
                                    <input type="number" @bind="attendanceData.AttendanceDays" @bind:event="oninput"
                                           class="form-control form-control-sm text-center" min="0" max="31" />
                                </td>
                                <td class="text-center">
                                    <strong>@GetAttendancePercentageFormatted(attendanceData)</strong>
                                </td>
                                <td class="text-center bg-warning bg-opacity-25">
                                    <strong>@GetAttendanceScoreFormatted(attendanceData)</strong>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            <!-- Attendance Comments Section -->
            <div class="card-footer">
                <div class="row">
                    <div class="col-12">
                        <h6 class="mb-3">
                            <button class="btn btn-link p-0 text-decoration-none fw-bold" type="button"
                                    data-bs-toggle="collapse" data-bs-target="#attendanceComments"
                                    aria-expanded="false" aria-controls="attendanceComments">
                                <i class="fas fa-comment @GetMarginEnd(1)"></i>
                                @L("Attendance Comments & Notes", "تعليقات وملاحظات الحضور")
                                <i class="fas fa-chevron-down @GetMarginStart(1)"></i>
                            </button>
                        </h6>
                        <div class="collapse" id="attendanceComments">
                        @foreach (var employee in filteredEmployees)
                        {
                            var attendanceData = GetOrCreateAttendanceData(employee.Id);
                            <div class="mb-3">
                                <label class="form-label fw-bold">
                                    @GetUserDisplayName(employee) (@employee.EmployeeId)
                                </label>
                                <textarea @bind="attendanceData.Comments" @bind:event="oninput"
                                          class="form-control" rows="2" maxlength="500"
                                          placeholder="@L("Add comments or notes about attendance metrics...", "أضف تعليقات أو ملاحظات حول مقاييس الحضور...")"></textarea>
                                <div class="d-flex justify-content-between">
                                    <small class="text-muted">@L("Maximum 500 characters", "الحد الأقصى 500 حرف")</small>
                                    <small class="text-muted">@((attendanceData.Comments?.Length ?? 0))/500</small>
                                </div>
                            </div>
                        }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section 3: Supervisor Evaluation Criteria -->
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-user-tie @GetMarginEnd(1)"></i>
                @L("Section 2: Supervisor Evaluation Criteria", "القسم الثاني: معايير تقييم المسؤول")
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-bordered mb-0" dir="@Direction">
                    <thead class="table-light">
                        <tr>
                            <th class="text-center">@L("Employee", "الموظف")</th>
                            <th class="text-center">@L("Work Efficiency & Quality", "كفاءة العمل وجودته")</th>
                            <th class="text-center">@L("Leadership Ability", "القدرة على القيادة")</th>
                            <th class="text-center">@L("Planning & Development", "التخطيط والتطوير")</th>
                            <th class="text-center">@L("Teamwork", "العمل الجماعي")</th>
                            <th class="text-center">@L("Responsibility", "تحمل المسؤولية")</th>
                            <th class="text-center">@L("Emergency Management", "إدارة الطوارئ")</th>
                            <th class="text-center">@L("General Behavior", "السلوك العام")</th>
                            <th class="text-center">@L("Supervisor Relations", "العلاقة مع الرؤساء")</th>
                            <th class="text-center">@L("Discipline", "الانضباط")</th>
                            <th class="text-center">@L("Work Development", "تطوير العمل")</th>
                            <th class="text-center">@L("Total (50)", "المجموع (50)")</th>
                            <th class="text-center">@L("20% Supervisor Score", "درجة المسؤول 20%")</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var employee in filteredEmployees)
                        {
                            var supervisorEval = GetOrCreateSupervisorEvaluation(employee.Id);
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="profile-avatar profile-avatar-sm @GetMarginEnd(2)">
                                            @GetUserInitials(employee)
                                        </div>
                                        <div>
                                            <div class="fw-bold">@GetUserDisplayName(employee)</div>
                                            <small class="text-muted">@employee.EmployeeId</small>
                                        </div>
                                    </div>
                                </td>
                                <td class="text-center">
                                    <select class="form-select form-select-sm" @bind="supervisorEval.EfficiencyAndQualityScore">
                                        @for (int i = 0; i <= 5; i++)
                                        {
                                            <option value="@i">@i</option>
                                        }
                                    </select>
                                </td>
                                <td class="text-center">
                                    <select class="form-select form-select-sm" @bind="supervisorEval.LeadershipAbilityScore">
                                        @for (int i = 0; i <= 5; i++)
                                        {
                                            <option value="@i">@i</option>
                                        }
                                    </select>
                                </td>
                                <td class="text-center">
                                    <select class="form-select form-select-sm" @bind="supervisorEval.PlanningAndInnovationScore">
                                        @for (int i = 0; i <= 5; i++)
                                        {
                                            <option value="@i">@i</option>
                                        }
                                    </select>
                                </td>
                                <td class="text-center">
                                    <select class="form-select form-select-sm" @bind="supervisorEval.TeamworkParticipationScore">
                                        @for (int i = 0; i <= 5; i++)
                                        {
                                            <option value="@i">@i</option>
                                        }
                                    </select>
                                </td>
                                <td class="text-center">
                                    <select class="form-select form-select-sm" @bind="supervisorEval.ResponsibilityAndPressureScore">
                                        @for (int i = 0; i <= 5; i++)
                                        {
                                            <option value="@i">@i</option>
                                        }
                                    </select>
                                </td>
                                <td class="text-center">
                                    <select class="form-select form-select-sm" @bind="supervisorEval.EmergencyHandlingScore">
                                        @for (int i = 0; i <= 5; i++)
                                        {
                                            <option value="@i">@i</option>
                                        }
                                    </select>
                                </td>
                                <td class="text-center">
                                    <select class="form-select form-select-sm" @bind="supervisorEval.GeneralBehaviorScore">
                                        @for (int i = 0; i <= 5; i++)
                                        {
                                            <option value="@i">@i</option>
                                        }
                                    </select>
                                </td>
                                <td class="text-center">
                                    <select class="form-select form-select-sm" @bind="supervisorEval.RelationshipWithSuperiorsScore">
                                        @for (int i = 0; i <= 5; i++)
                                        {
                                            <option value="@i">@i</option>
                                        }
                                    </select>
                                </td>
                                <td class="text-center">
                                    <select class="form-select form-select-sm" @bind="supervisorEval.DisciplineAndCommitmentScore">
                                        @for (int i = 0; i <= 5; i++)
                                        {
                                            <option value="@i">@i</option>
                                        }
                                    </select>
                                </td>
                                <td class="text-center">
                                    <select class="form-select form-select-sm" @bind="supervisorEval.WorkDevelopmentScore">
                                        @for (int i = 0; i <= 5; i++)
                                        {
                                            <option value="@i">@i</option>
                                        }
                                    </select>
                                </td>
                                <td class="text-center">
                                    <strong>@((int)supervisorEval.TotalScore)</strong>
                                </td>
                                <td class="text-center bg-success bg-opacity-25">
                                    <strong>@GetSupervisorScoreFormatted(supervisorEval.WeightedScore)</strong>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            <!-- Supervisor Evaluation Comments Section -->
            <div class="card-footer">
                <div class="row">
                    <div class="col-12">
                        <h6 class="mb-3">
                            <button class="btn btn-link p-0 text-decoration-none fw-bold" type="button"
                                    data-bs-toggle="collapse" data-bs-target="#supervisorComments"
                                    aria-expanded="false" aria-controls="supervisorComments">
                                <i class="fas fa-comment @GetMarginEnd(1)"></i>
                                @L("Supervisor Evaluation Comments & Feedback", "تعليقات وملاحظات تقييم المسؤول")
                                <i class="fas fa-chevron-down @GetMarginStart(1)"></i>
                            </button>
                        </h6>
                        <div class="collapse" id="supervisorComments">
                        @foreach (var employee in filteredEmployees)
                        {
                            var supervisorEval = GetOrCreateSupervisorEvaluation(employee.Id);
                            <div class="border rounded p-3 mb-4">
                                <h6 class="fw-bold mb-3">
                                    @GetUserDisplayName(employee) (@employee.EmployeeId)
                                </h6>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">
                                            @L("General Comments", "التعليقات العامة")
                                        </label>
                                        <textarea @bind="supervisorEval.Comments" @bind:event="oninput"
                                                  class="form-control" rows="3" maxlength="1000"
                                                  placeholder="@L("General comments about performance...", "تعليقات عامة حول الأداء...")"></textarea>
                                        <div class="d-flex justify-content-between">
                                            <small class="text-muted">@L("Maximum 1000 characters", "الحد الأقصى 1000 حرف")</small>
                                            <small class="text-muted">@((supervisorEval.Comments?.Length ?? 0))/1000</small>
                                        </div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">
                                            @L("Strengths", "نقاط القوة")
                                        </label>
                                        <textarea @bind="supervisorEval.Strengths" @bind:event="oninput"
                                                  class="form-control" rows="3" maxlength="1000"
                                                  placeholder="@L("Employee strengths and positive aspects...", "نقاط قوة الموظف والجوانب الإيجابية...")"></textarea>
                                        <div class="d-flex justify-content-between">
                                            <small class="text-muted">@L("Maximum 1000 characters", "الحد الأقصى 1000 حرف")</small>
                                            <small class="text-muted">@((supervisorEval.Strengths?.Length ?? 0))/1000</small>
                                        </div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">
                                            @L("Areas for Improvement", "مجالات التحسين")
                                        </label>
                                        <textarea @bind="supervisorEval.AreasForImprovement" @bind:event="oninput"
                                                  class="form-control" rows="3" maxlength="1000"
                                                  placeholder="@L("Areas that need improvement...", "المجالات التي تحتاج إلى تحسين...")"></textarea>
                                        <div class="d-flex justify-content-between">
                                            <small class="text-muted">@L("Maximum 1000 characters", "الحد الأقصى 1000 حرف")</small>
                                            <small class="text-muted">@((supervisorEval.AreasForImprovement?.Length ?? 0))/1000</small>
                                        </div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">
                                            @L("Next Period Goals", "أهداف الفترة القادمة")
                                        </label>
                                        <textarea @bind="supervisorEval.NextPeriodGoals" @bind:event="oninput"
                                                  class="form-control" rows="3" maxlength="1000"
                                                  placeholder="@L("Goals and objectives for next period...", "الأهداف والغايات للفترة القادمة...")"></textarea>
                                        <div class="d-flex justify-content-between">
                                            <small class="text-muted">@L("Maximum 1000 characters", "الحد الأقصى 1000 حرف")</small>
                                            <small class="text-muted">@((supervisorEval.NextPeriodGoals?.Length ?? 0))/1000</small>
                                        </div>
                                    </div>

                                    <!-- Exceptional Work Section -->
                                    <div class="col-12 mb-3">
                                        <label class="form-label fw-bold">
                                            <i class="fas fa-star text-warning @GetMarginEnd(1)"></i>
                                            @L("Exceptional Work", "العمل المميز")
                                        </label>
                                        <textarea @bind="supervisorEval.ExceptionalWork" @bind:event="oninput"
                                                  class="form-control" rows="4" maxlength="2000"
                                                  placeholder="@L("Document any exceptional work, achievements, or outstanding contributions by the employee...", "وثق أي عمل مميز أو إنجازات أو مساهمات متميزة للموظف...")"></textarea>
                                        <div class="d-flex justify-content-between">
                                            <small class="text-muted">@L("Maximum 2000 characters", "الحد الأقصى 2000 حرف")</small>
                                            <small class="text-muted">@((supervisorEval.ExceptionalWork?.Length ?? 0))/2000</small>
                                        </div>

                                        <!-- File Attachment Section for Exceptional Work -->
                                        <div class="mt-3 p-3 border rounded bg-light">
                                            <h6 class="fw-bold mb-3">
                                                <i class="fas fa-paperclip @GetMarginEnd(1)"></i>
                                                @L("Supporting Documents", "المستندات الداعمة")
                                            </h6>
                                            <p class="text-muted small mb-3">
                                                @L("Attach files to support and document the exceptional work described above (certificates, project documents, photos, etc.)",
                                                   "أرفق ملفات لدعم وتوثيق العمل المميز المذكور أعلاه (شهادات، وثائق مشاريع، صور، إلخ)")
                                            </p>

                                            <!-- Upload Button -->
                                            <button type="button" class="btn btn-outline-primary btn-sm mb-3"
                                                    @onclick="@(() => ShowExceptionalWorkUploadModal(supervisorEval))">
                                                <i class="fas fa-upload @GetMarginEnd(1)"></i>
                                                @L("Upload File", "رفع ملف")
                                            </button>

                                            <!-- Display Existing Attachments -->
                                            <div id="<EMAIL>">
                                                <EvaluationAttachmentsList SupervisorEvaluationId="@supervisorEval.Id"
                                                                         AttachmentType="@EvaluationAttachmentTypes.ExceptionalWork"
                                                                         CanDeleteAttachment="true" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Final Results Summary -->
    <div class="card mb-4">
        <div class="card-header bg-dark text-white">
            <h5 class="card-title mb-0 text-white">
                <i class="fas fa-trophy @GetMarginEnd(1) text-white"></i>
                <span class="text-white">@L("Final Evaluation Results", "النتائج النهائية للتقييم")</span>
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-bordered mb-0" dir="@Direction">
                    <thead class="table-light">
                        <tr>
                            <th class="text-center">@L("Employee", "الموظف")</th>
                            <th class="text-center">@L("Work Volume Score (60%)", "درجة حجم العمل (60%)")</th>
                            <th class="text-center">@L("Attendance Score (20%)", "درجة الحضور (20%)")</th>
                            <th class="text-center">@L("Supervisor Score (20%)", "درجة المسؤول (20%)")</th>
                            <th class="text-center">@L("Total Score (100%)", "الدرجة الإجمالية (100%)")</th>
                            <th class="text-center">@L("Rank", "الترتيب")</th>
                        </tr>
                    </thead>
                    <tbody>
                        @{
                            var rankedEmployees = filteredEmployees
                                .Select(emp => new {
                                    Employee = emp,
                                    WorkScore = GetWorkVolumeScore(GetOrCreateWorkVolumeData(emp.Id)),
                                    AttendanceScore = GetAttendanceScore(GetOrCreateAttendanceData(emp.Id)),
                                    SupervisorScore = GetOrCreateSupervisorEvaluation(emp.Id).WeightedScore,
                                    TotalScore = GetWorkVolumeScore(GetOrCreateWorkVolumeData(emp.Id)) +
                                               GetAttendanceScore(GetOrCreateAttendanceData(emp.Id)) +
                                               GetOrCreateSupervisorEvaluation(emp.Id).WeightedScore
                                })
                                .OrderByDescending(x => x.TotalScore)
                                .Select((x, index) => new { x.Employee, x.WorkScore, x.AttendanceScore, x.SupervisorScore, x.TotalScore, Rank = index + 1 })
                                .ToList();
                        }
                        @foreach (var result in rankedEmployees)
                        {
                            <tr class="@(result.Rank <= 3 ? "table-success" : "")">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="profile-avatar profile-avatar-sm @GetMarginEnd(2)">
                                            @GetUserInitials(result.Employee)
                                        </div>
                                        <div>
                                            <div class="fw-bold">@GetUserDisplayName(result.Employee)</div>
                                            <small class="text-muted">@result.Employee.EmployeeId</small>
                                        </div>
                                    </div>
                                </td>
                                <td class="text-center">@GetTotalScoreFormatted(result.WorkScore)</td>
                                <td class="text-center">@GetTotalScoreFormatted(result.AttendanceScore)</td>
                                <td class="text-center">@GetTotalScoreFormatted(result.SupervisorScore)</td>
                                <td class="text-center">
                                    <strong class="@(result.Rank <= 3 ? "text-success" : "")">
                                        @GetTotalScoreFormatted(result.TotalScore)
                                    </strong>
                                </td>
                                <td class="text-center">
                                    <span class="badge @(result.Rank == 1 ? "bg-warning" : result.Rank <= 3 ? "bg-success" : "bg-secondary")">
                                        @result.Rank
                                    </span>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="d-flex justify-content-center mb-4 gap-3 flex-wrap">
        <button class="btn btn-primary btn-lg" @onclick="SaveEvaluationData" disabled="@(!employees.Any() || isSaving)">
            @if (isSaving)
            {
                <i class="fas fa-spinner fa-spin @GetMarginEnd(1)"></i>
                @L("Saving...", "جاري الحفظ...")
            }
            else
            {
                <i class="fas fa-save @GetMarginEnd(1)"></i>
                @L("Save Evaluation Data", "حفظ بيانات التقييم")
            }
        </button>
        <button class="btn btn-secondary btn-lg" @onclick="SeedSampleData" disabled="@(string.IsNullOrEmpty(selectedDepartmentId))">
            <i class="fas fa-database @GetMarginEnd(1)"></i>
            @L("Seed Sample Data", "إنشاء بيانات تجريبية")
        </button>
        @if (CanClearData())
        {
            <button class="btn btn-warning btn-lg" @onclick="ShowClearDataConfirmation" disabled="@(!employees.Any())">
                <i class="fas fa-eraser @GetMarginEnd(1)"></i>
                @L("Clear All Data", "مسح جميع البيانات")
            </button>
        }
    </div>
}

<!-- Clear Data Confirmation Modal -->
<div class="modal fade" id="clearDataModal" tabindex="-1" aria-labelledby="clearDataModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="clearDataModalLabel">
                    <i class="fas fa-exclamation-triangle @GetMarginEnd(1)"></i>
                    @L("Confirm Clear All Data", "تأكيد مسح جميع البيانات")
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-exclamation-triangle @GetMarginEnd(1)"></i>
                    <strong>@L("Warning", "تحذير"):</strong>
                    @L("This action will clear all form data and cannot be undone.", "هذا الإجراء سيمسح جميع بيانات النموذج ولا يمكن التراجع عنه.")
                </div>

                <p class="mb-3">
                    @L("The following data will be cleared:", "سيتم مسح البيانات التالية:")
                </p>

                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-briefcase text-primary @GetMarginEnd(1)"></i>
                        @L("All work volume data (Quality Program, Oracle, Documented Work)", "جميع بيانات حجم العمل (برنامج الجودة، الأوراكل، العمل الموثق)")
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-calendar-check text-warning @GetMarginEnd(1)"></i>
                        @L("All attendance data (Working Days, Attendance Days)", "جميع بيانات الحضور (أيام العمل، أيام الحضور)")
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-user-tie text-success @GetMarginEnd(1)"></i>
                        @L("All supervisor evaluation scores", "جميع درجات تقييم المسؤول")
                    </li>
                </ul>

                <div class="alert alert-info" role="alert">
                    <i class="fas fa-info-circle @GetMarginEnd(1)"></i>
                    @L("Note: This only clears the current form data. Previously saved evaluations in the database will not be affected.", "ملاحظة: هذا يمسح فقط بيانات النموذج الحالي. التقييمات المحفوظة مسبقاً في قاعدة البيانات لن تتأثر.")
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times @GetMarginEnd(1)"></i>
                    @L("Cancel", "إلغاء")
                </button>
                <button type="button" class="btn btn-warning" @onclick="ClearAllData" data-bs-dismiss="modal">
                    <i class="fas fa-eraser @GetMarginEnd(1)"></i>
                    @L("Clear All Data", "مسح جميع البيانات")
                </button>
            </div>
        </div>
    </div>
</div>

@code {
    private string selectedYear = DateTime.Now.Year.ToString();
    private string selectedMonth = DateTime.Now.Month.ToString();
    private string selectedDepartmentId = "";
    private string selectedMonthlyWorkingDays = "";
    
    private List<Department> departments = new();
    private List<ApplicationUser> employees = new();
    private List<WorkVolumeData> workVolumeDataList = new();
    private List<AttendanceData> attendanceDataList = new();
    private List<SupervisorEvaluation> supervisorEvaluations = new();

    // Search functionality
    private string searchQuery = "";
    private List<ApplicationUser> filteredEmployees = new();

    // Authorization and state management
    private ApplicationUser? currentUser = null;
    private bool isSaving = false;

    protected override async Task OnInitializedAsync()
    {
        // Set default values for year and month to current date for better UX
        // Users can still manually change these selections if needed
        selectedYear = DateTime.Now.Year.ToString();
        selectedMonth = DateTime.Now.Month.ToString();

        await LoadCurrentUser();
        await LoadDepartments();
    }

    private bool IsButtonDisabled()
    {
        var disabled = string.IsNullOrEmpty(selectedYear) || string.IsNullOrEmpty(selectedMonth) || string.IsNullOrEmpty(selectedDepartmentId);
        Console.WriteLine($"Button disabled check - Year: '{selectedYear}', Month: '{selectedMonth}', Dept: '{selectedDepartmentId}', Disabled: {disabled}");
        return disabled;
    }

    private async Task LoadCurrentUser()
    {
        try
        {
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                var userEmail = authState.User.Identity.Name;
                if (!string.IsNullOrEmpty(userEmail))
                {
                    currentUser = await DbContext.Users
                        .FirstOrDefaultAsync(u => u.Email == userEmail && !u.IsDeleted);
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading current user: {ex.Message}");
        }
    }

    private bool CanClearData()
    {
        // Allow roles that can create/manage evaluations to clear data
        // EMPLOYEE role is excluded as they have read-only access
        return currentUser != null &&
               (currentUser.Role == UserRole.SUPER_ADMIN ||
                currentUser.Role == UserRole.EXCELLENCE_TEAM ||
                currentUser.Role == UserRole.MANAGER ||
                currentUser.Role == UserRole.SUPERVISOR);
    }

    private async Task ShowClearDataConfirmation()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("new bootstrap.Modal", "#clearDataModal").AsTask();
            await JSRuntime.InvokeVoidAsync("document.getElementById('clearDataModal').querySelector('.modal').classList.add('show')");
            await JSRuntime.InvokeVoidAsync("eval", "new bootstrap.Modal(document.getElementById('clearDataModal')).show()");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error showing clear data modal: {ex.Message}");
            // Fallback to browser confirm dialog
            var confirmed = await JSRuntime.InvokeAsync<bool>("confirm",
                L("Are you sure you want to clear all form data? This action cannot be undone.",
                  "هل أنت متأكد من أنك تريد مسح جميع بيانات النموذج؟ لا يمكن التراجع عن هذا الإجراء."));
            if (confirmed)
            {
                await ClearAllData();
            }
        }
    }

    private async Task ClearAllData()
    {
        try
        {
            Console.WriteLine("Starting to clear all evaluation form data...");

            // Clear work volume data
            foreach (var workData in workVolumeDataList)
            {
                workData.QualityProgramWork = 0;
                workData.OracleWork = 0;
                workData.DocumentedWork = 0;
            }
            Console.WriteLine($"Cleared work volume data for {workVolumeDataList.Count} employees");

            // Clear attendance data
            foreach (var attendanceData in attendanceDataList)
            {
                attendanceData.AttendanceDays = 0;
                // Keep TotalWorkingDays as it's calculated based on the month
            }
            Console.WriteLine($"Cleared attendance data for {attendanceDataList.Count} employees");

            // Clear supervisor evaluation scores
            foreach (var supervisorEval in supervisorEvaluations)
            {
                supervisorEval.EfficiencyAndQualityScore = 0;
                supervisorEval.LeadershipAbilityScore = 0;
                supervisorEval.PlanningAndInnovationScore = 0;
                supervisorEval.TeamworkParticipationScore = 0;
                supervisorEval.ResponsibilityAndPressureScore = 0;
                supervisorEval.EmergencyHandlingScore = 0;
                supervisorEval.GeneralBehaviorScore = 0;
                supervisorEval.RelationshipWithSuperiorsScore = 0;
                supervisorEval.DisciplineAndCommitmentScore = 0;
                supervisorEval.WorkDevelopmentScore = 0;
            }
            Console.WriteLine($"Cleared supervisor evaluation data for {supervisorEvaluations.Count} employees");

            // Clear filter selections
            selectedMonthlyWorkingDays = "";
            Console.WriteLine("Cleared monthly working days selection");

            // Trigger UI update
            StateHasChanged();

            // Show success message
            await JSRuntime.InvokeVoidAsync("alert",
                L("All form data has been cleared successfully. You can now enter new evaluation data.",
                  "تم مسح جميع بيانات النموذج بنجاح. يمكنك الآن إدخال بيانات تقييم جديدة."));

            Console.WriteLine("Clear all data operation completed successfully");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error clearing data: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert",
                L("An error occurred while clearing the data. Please try again.",
                  "حدث خطأ أثناء مسح البيانات. يرجى المحاولة مرة أخرى."));
        }
    }

    private async Task LoadDepartments()
    {
        try
        {
            if (currentUser != null)
            {
                // Use EmployeeManagementService to get accessible departments based on role
                var accessibleDepartments = await EmployeeManagementService.GetAccessibleDepartmentsAsync(currentUser.Id);
                departments = accessibleDepartments
                    .OrderBy(d => d.NameEn)
                    .ToList();

                Console.WriteLine($"Loaded {departments.Count} accessible departments for user role: {currentUser.Role}");

                // Auto-select department for supervisors (they only have access to one department)
                if (currentUser.Role == UserRole.SUPERVISOR && departments.Count == 1)
                {
                    selectedDepartmentId = departments.First().Id.ToString();
                    Console.WriteLine($"Auto-selected department for supervisor: {selectedDepartmentId} ({departments.First().NameEn})");

                    // Automatically load employees after department selection
                    await LoadEmployees();
                }
            }
            else
            {
                Console.WriteLine("Current user is null, cannot load departments");
                departments = new List<Department>();
            }
        }
        catch (Exception ex)
        {
            // Log error without JavaScript interop during initialization
            Console.WriteLine($"Error loading departments: {ex.Message}");
            departments = new List<Department>();
        }
    }

    private async Task LoadEmployees()
    {
        try
        {
            Console.WriteLine($"LoadEmployees called - Year: '{selectedYear}', Month: '{selectedMonth}', Dept: '{selectedDepartmentId}'");

            if (string.IsNullOrEmpty(selectedDepartmentId))
            {
                Console.WriteLine("Department ID is empty, returning");
                return;
            }

            var departmentId = int.Parse(selectedDepartmentId);
            Console.WriteLine($"Parsed department ID: {departmentId}");

            // Use EmployeeManagementService to get evaluatable users based on role permissions
            if (currentUser != null)
            {
                var evaluatableUsers = await EmployeeManagementService.GetEvaluatableUsersAsync(currentUser.Id);

                // Filter by selected department if specified
                employees = evaluatableUsers
                    .Where(u => u.PrimaryDepartmentId == departmentId)
                    .OrderBy(u => u.EnglishName)
                    .ToList();

                Console.WriteLine($"Found {employees.Count} evaluatable employees for current user role: {currentUser.Role}");
            }
            else
            {
                Console.WriteLine("Current user is null, cannot load employees");
                employees = new List<ApplicationUser>();
            }

        // Initialize data collections
        workVolumeDataList.Clear();
        attendanceDataList.Clear();
        supervisorEvaluations.Clear();

        var evaluationPeriod = $"{selectedYear}-{selectedMonth:D2}";

        // Load existing data or create new entries
        foreach (var employee in employees)
        {
            // Work Volume Data
            var workData = await DbContext.WorkVolumeData
                .FirstOrDefaultAsync(w => w.EmployeeId == employee.Id && w.EvaluationPeriod == evaluationPeriod);
            if (workData == null)
            {
                workData = new WorkVolumeData
                {
                    EmployeeId = employee.Id,
                    DepartmentId = departmentId,
                    EvaluationPeriod = evaluationPeriod,
                    RecordedBy = "System" // Should be current user
                };
            }
            workVolumeDataList.Add(workData);

            // Attendance Data
            var attendanceData = await DbContext.AttendanceData
                .FirstOrDefaultAsync(a => a.EmployeeId == employee.Id && a.EvaluationPeriod == evaluationPeriod);
            if (attendanceData == null)
            {
                // Use selected monthly working days if available, otherwise calculate from calendar
                int totalWorkingDays = !string.IsNullOrEmpty(selectedMonthlyWorkingDays) && int.TryParse(selectedMonthlyWorkingDays, out int selectedDays)
                    ? selectedDays
                    : GetWorkingDaysInMonth(int.Parse(selectedYear), int.Parse(selectedMonth));

                attendanceData = new AttendanceData
                {
                    EmployeeId = employee.Id,
                    DepartmentId = departmentId,
                    EvaluationPeriod = evaluationPeriod,
                    TotalWorkingDays = totalWorkingDays,
                    RecordedBy = "System" // Should be current user
                };
            }
            else if (!string.IsNullOrEmpty(selectedMonthlyWorkingDays) && int.TryParse(selectedMonthlyWorkingDays, out int selectedDays))
            {
                // Update existing attendance data with selected working days
                attendanceData.TotalWorkingDays = selectedDays;
            }
            attendanceDataList.Add(attendanceData);

            // Supervisor Evaluation
            var supervisorEval = await DbContext.SupervisorEvaluations
                .FirstOrDefaultAsync(s => s.EmployeeId == employee.Id && s.EvaluationPeriod == evaluationPeriod);
            if (supervisorEval == null)
            {
                // Get a valid supervisor (first manager or admin)
                var supervisor = await DbContext.Users
                    .FirstOrDefaultAsync(u => (u.Role == UserRole.MANAGER || u.Role == UserRole.SUPER_ADMIN) && u.IsActive);

                if (supervisor != null)
                {
                    supervisorEval = new SupervisorEvaluation
                    {
                        EmployeeId = employee.Id,
                        DepartmentId = departmentId,
                        EvaluationPeriod = evaluationPeriod,
                        SupervisorId = supervisor.Id
                    };
                }
            }
            if (supervisorEval != null)
            {
                supervisorEvaluations.Add(supervisorEval);
            }
        }

        Console.WriteLine($"LoadEmployees completed. Final employee count: {employees.Count}");

        // Initialize filtered employees list
        InitializeFilteredEmployees();

        StateHasChanged();
        }
        catch (Exception ex)
        {
            // Log error and could show a user-friendly error message here
            Console.WriteLine($"Error loading employees: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }

    private void UpdateAllMonthlyWorkingDays()
    {
        try
        {
            if (string.IsNullOrEmpty(selectedMonthlyWorkingDays) || !int.TryParse(selectedMonthlyWorkingDays, out int workingDays))
            {
                Console.WriteLine("Invalid monthly working days selection");
                return;
            }

            Console.WriteLine($"Updating all monthly working days to: {workingDays}");

            // Update all existing attendance data with the selected monthly working days
            foreach (var attendanceData in attendanceDataList)
            {
                attendanceData.TotalWorkingDays = workingDays;
            }

            Console.WriteLine($"Updated {attendanceDataList.Count} attendance records with {workingDays} working days");

            // Force UI update to ensure table reflects the changes immediately
            // This is especially important when employees are already loaded
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error updating monthly working days: {ex.Message}");
        }
    }

    // Helper methods for data access
    private WorkVolumeData GetOrCreateWorkVolumeData(string employeeId)
    {
        return workVolumeDataList.FirstOrDefault(w => w.EmployeeId == employeeId) ?? new WorkVolumeData();
    }

    private AttendanceData GetOrCreateAttendanceData(string employeeId)
    {
        var existingData = attendanceDataList.FirstOrDefault(a => a.EmployeeId == employeeId);
        if (existingData != null)
        {
            return existingData;
        }

        // Create new attendance data with selected working days if available
        int totalWorkingDays = 0;
        if (!string.IsNullOrEmpty(selectedMonthlyWorkingDays) && int.TryParse(selectedMonthlyWorkingDays, out int selectedDays))
        {
            totalWorkingDays = selectedDays;
        }
        else if (!string.IsNullOrEmpty(selectedYear) && !string.IsNullOrEmpty(selectedMonth))
        {
            totalWorkingDays = GetWorkingDaysInMonth(int.Parse(selectedYear), int.Parse(selectedMonth));
        }

        return new AttendanceData
        {
            EmployeeId = employeeId,
            TotalWorkingDays = totalWorkingDays
        };
    }

    private SupervisorEvaluation GetOrCreateSupervisorEvaluation(string employeeId)
    {
        return supervisorEvaluations.FirstOrDefault(s => s.EmployeeId == employeeId) ?? new SupervisorEvaluation();
    }

    // Calculation methods
    private decimal GetWorkVolumePercentage(WorkVolumeData workData)
    {
        var departmentTotal = GetDepartmentGrandTotal();
        return departmentTotal > 0 ? workData.TotalEmployeeWork / departmentTotal : 0;
    }

    private string GetWorkVolumePercentageFormatted(WorkVolumeData workData)
    {
        var percentage = GetWorkVolumePercentage(workData);
        var percentageValue = percentage * 100;
        return $"{Math.Round(percentageValue, 0):F0}%";
    }

    private string GetAttendancePercentageFormatted(AttendanceData attendanceData)
    {
        var percentageValue = attendanceData.AttendancePercentage * 100;
        return Math.Round(percentageValue, 0).ToString("F0", System.Globalization.CultureInfo.InvariantCulture) + "%";
    }

    private string GetWorkVolumeScoreFormatted(WorkVolumeData workData)
    {
        var score = GetWorkVolumeScore(workData);
        var percentageValue = score * 100;
        return Math.Round(percentageValue, 0).ToString("F0", System.Globalization.CultureInfo.InvariantCulture) + "%";
    }

    private string GetAttendanceScoreFormatted(AttendanceData attendanceData)
    {
        var score = GetAttendanceScore(attendanceData);
        var percentageValue = score * 100;
        return Math.Round(percentageValue, 0).ToString("F0", System.Globalization.CultureInfo.InvariantCulture) + "%";
    }

    private string GetSupervisorScoreFormatted(decimal weightedScore)
    {
        var percentageValue = weightedScore * 100;
        return Math.Round(percentageValue, 0).ToString("F0", System.Globalization.CultureInfo.InvariantCulture) + "%";
    }

    private string GetTotalScoreFormatted(decimal totalScore)
    {
        var percentageValue = totalScore * 100;
        return $"{Math.Round(percentageValue, 0):F0}%";
    }

    private decimal GetWorkVolumeScore(WorkVolumeData workData)
    {
        var percentage = GetWorkVolumePercentage(workData);
        var highestPercentage = GetHighestWorkVolumePercentage();
        return highestPercentage > 0 ? (percentage / highestPercentage) * 0.6m : 0;
    }

    private decimal GetHighestWorkVolumePercentage()
    {
        return workVolumeDataList.Any() ? workVolumeDataList.Max(w => GetWorkVolumePercentage(w)) : 0;
    }

    private decimal GetAttendanceScore(AttendanceData attendanceData)
    {
        var highestAttendanceRate = GetHighestAttendanceRate();
        return highestAttendanceRate > 0 ? (attendanceData.AttendancePercentage / highestAttendanceRate) * 0.2m : 0;
    }

    private decimal GetHighestAttendanceRate()
    {
        return attendanceDataList.Any() ? attendanceDataList.Max(a => a.AttendancePercentage) : 0;
    }

    // Department totals
    private decimal GetDepartmentQualityTotal() => workVolumeDataList.Sum(w => w.QualityProgramWork);
    private decimal GetDepartmentOracleTotal() => workVolumeDataList.Sum(w => w.OracleWork);
    private decimal GetDepartmentDocumentedTotal() => workVolumeDataList.Sum(w => w.DocumentedWork);
    private decimal GetDepartmentGrandTotal() => GetDepartmentQualityTotal() + GetDepartmentOracleTotal() + GetDepartmentDocumentedTotal();

    // Event handlers
    private void CalculateWorkVolumeScores()
    {
        StateHasChanged();
    }

    private void CalculateAttendanceScores()
    {
        StateHasChanged();
    }

    private void CalculateSupervisorScores()
    {
        StateHasChanged();
    }

    private async Task SaveEvaluationData()
    {
        try
        {
            isSaving = true;
            StateHasChanged();

            Console.WriteLine("Starting to save evaluation data...");

            // Validation checks
            if (string.IsNullOrEmpty(selectedYear) || string.IsNullOrEmpty(selectedMonth) || string.IsNullOrEmpty(selectedDepartmentId))
            {
                throw new InvalidOperationException("Year, Month, and Department must be selected before saving.");
            }

            if (!employees.Any())
            {
                throw new InvalidOperationException("No employees loaded. Please load employees before saving evaluation data.");
            }

            var evaluationPeriod = $"{selectedYear}-{selectedMonth:D2}";
            Console.WriteLine($"Saving evaluation data for period: {evaluationPeriod}, Department: {selectedDepartmentId}");

            // Add a small delay to show the loading state (optional)
            await Task.Delay(100);
            // Save Work Volume Data
            Console.WriteLine($"Saving work volume data for {workVolumeDataList.Count} employees...");
            foreach (var workData in workVolumeDataList)
            {
                if (string.IsNullOrEmpty(workData.EmployeeId))
                {
                    Console.WriteLine("Warning: Work volume data has empty EmployeeId, skipping...");
                    continue;
                }

                if (string.IsNullOrEmpty(workData.EvaluationPeriod))
                {
                    workData.EvaluationPeriod = evaluationPeriod;
                }

                Console.WriteLine($"Processing work volume data for employee: {workData.EmployeeId}");

                var existing = await DbContext.WorkVolumeData
                    .FirstOrDefaultAsync(w => w.EmployeeId == workData.EmployeeId && w.EvaluationPeriod == workData.EvaluationPeriod);

                if (existing != null)
                {
                    Console.WriteLine($"Updating existing work volume data for employee: {workData.EmployeeId}");
                    existing.QualityProgramWork = workData.QualityProgramWork;
                    existing.OracleWork = workData.OracleWork;
                    existing.DocumentedWork = workData.DocumentedWork;
                    existing.Comments = workData.Comments;
                    existing.UpdatedAt = DateTime.UtcNow;
                }
                else
                {
                    Console.WriteLine($"Creating new work volume data for employee: {workData.EmployeeId}");
                    workData.CreatedAt = DateTime.UtcNow;
                    workData.UpdatedAt = DateTime.UtcNow;
                    DbContext.WorkVolumeData.Add(workData);
                }
            }

            // Save Attendance Data
            Console.WriteLine($"Saving attendance data for {attendanceDataList.Count} employees...");
            foreach (var attendanceData in attendanceDataList)
            {
                if (string.IsNullOrEmpty(attendanceData.EmployeeId))
                {
                    Console.WriteLine("Warning: Attendance data has empty EmployeeId, skipping...");
                    continue;
                }

                if (string.IsNullOrEmpty(attendanceData.EvaluationPeriod))
                {
                    attendanceData.EvaluationPeriod = evaluationPeriod;
                }

                Console.WriteLine($"Processing attendance data for employee: {attendanceData.EmployeeId}");

                var existing = await DbContext.AttendanceData
                    .FirstOrDefaultAsync(a => a.EmployeeId == attendanceData.EmployeeId && a.EvaluationPeriod == attendanceData.EvaluationPeriod);

                if (existing != null)
                {
                    Console.WriteLine($"Updating existing attendance data for employee: {attendanceData.EmployeeId}");
                    existing.TotalWorkingDays = attendanceData.TotalWorkingDays;
                    existing.AttendanceDays = attendanceData.AttendanceDays;
                    existing.Comments = attendanceData.Comments;
                    existing.UpdatedAt = DateTime.UtcNow;
                }
                else
                {
                    Console.WriteLine($"Creating new attendance data for employee: {attendanceData.EmployeeId}");
                    attendanceData.CreatedAt = DateTime.UtcNow;
                    attendanceData.UpdatedAt = DateTime.UtcNow;
                    DbContext.AttendanceData.Add(attendanceData);
                }
            }

            // Save Supervisor Evaluations
            Console.WriteLine($"Saving supervisor evaluations for {supervisorEvaluations.Count} employees...");
            foreach (var supervisorEval in supervisorEvaluations)
            {
                if (string.IsNullOrEmpty(supervisorEval.EmployeeId))
                {
                    Console.WriteLine("Warning: Supervisor evaluation has empty EmployeeId, skipping...");
                    continue;
                }

                if (string.IsNullOrEmpty(supervisorEval.EvaluationPeriod))
                {
                    supervisorEval.EvaluationPeriod = evaluationPeriod;
                }

                Console.WriteLine($"Processing supervisor evaluation for employee: {supervisorEval.EmployeeId}");

                var existing = await DbContext.SupervisorEvaluations
                    .FirstOrDefaultAsync(s => s.EmployeeId == supervisorEval.EmployeeId && s.EvaluationPeriod == supervisorEval.EvaluationPeriod);

                if (existing != null)
                {
                    Console.WriteLine($"Updating existing supervisor evaluation for employee: {supervisorEval.EmployeeId}");
                    existing.EfficiencyAndQualityScore = supervisorEval.EfficiencyAndQualityScore;
                    existing.LeadershipAbilityScore = supervisorEval.LeadershipAbilityScore;
                    existing.PlanningAndInnovationScore = supervisorEval.PlanningAndInnovationScore;
                    existing.TeamworkParticipationScore = supervisorEval.TeamworkParticipationScore;
                    existing.ResponsibilityAndPressureScore = supervisorEval.ResponsibilityAndPressureScore;
                    existing.EmergencyHandlingScore = supervisorEval.EmergencyHandlingScore;
                    existing.GeneralBehaviorScore = supervisorEval.GeneralBehaviorScore;
                    existing.RelationshipWithSuperiorsScore = supervisorEval.RelationshipWithSuperiorsScore;
                    existing.DisciplineAndCommitmentScore = supervisorEval.DisciplineAndCommitmentScore;
                    existing.WorkDevelopmentScore = supervisorEval.WorkDevelopmentScore;
                    existing.Comments = supervisorEval.Comments;
                    existing.Strengths = supervisorEval.Strengths;
                    existing.AreasForImprovement = supervisorEval.AreasForImprovement;
                    existing.NextPeriodGoals = supervisorEval.NextPeriodGoals;
                    existing.ExceptionalWork = supervisorEval.ExceptionalWork;
                    existing.UpdatedAt = DateTime.UtcNow;
                }
                else
                {
                    Console.WriteLine($"Creating new supervisor evaluation for employee: {supervisorEval.EmployeeId}");
                    supervisorEval.CreatedAt = DateTime.UtcNow;
                    supervisorEval.UpdatedAt = DateTime.UtcNow;
                    DbContext.SupervisorEvaluations.Add(supervisorEval);
                }
            }

            Console.WriteLine("Saving all changes to database...");
            await DbContext.SaveChangesAsync();
            Console.WriteLine("Database save completed successfully!");

            // Calculate comprehensive evaluations
            Console.WriteLine($"Starting comprehensive evaluation calculations for department {selectedDepartmentId}, period {evaluationPeriod}...");
            await EvaluationCalculationService.CalculateDepartmentEvaluationsAsync(int.Parse(selectedDepartmentId), evaluationPeriod);
            Console.WriteLine("Comprehensive evaluation calculations completed successfully!");

            // Show success message to user
            Console.WriteLine("Evaluation data saved successfully!");
            await JSRuntime.InvokeVoidAsync("alert",
                L("Evaluation data has been saved successfully! The comprehensive evaluations have been calculated and are now available.",
                  "تم حفظ بيانات التقييم بنجاح! تم حساب التقييمات الشاملة وهي متاحة الآن."));
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error saving evaluation data: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");

            // Provide more specific error messages based on exception type
            string errorMessage;
            string errorMessageAr;

            if (ex is InvalidOperationException)
            {
                errorMessage = $"Validation Error: {ex.Message}";
                errorMessageAr = $"خطأ في التحقق: {ex.Message}";
            }
            else if (ex.Message.Contains("foreign key", StringComparison.OrdinalIgnoreCase) ||
                     ex.Message.Contains("constraint", StringComparison.OrdinalIgnoreCase))
            {
                errorMessage = "Database constraint error. Please ensure all required fields are filled and employees are properly selected.";
                errorMessageAr = "خطأ في قيود قاعدة البيانات. يرجى التأكد من ملء جميع الحقول المطلوبة واختيار الموظفين بشكل صحيح.";
            }
            else if (ex.Message.Contains("null", StringComparison.OrdinalIgnoreCase))
            {
                errorMessage = "Missing required data. Please ensure all evaluation forms are properly filled out.";
                errorMessageAr = "بيانات مطلوبة مفقودة. يرجى التأكد من ملء جميع نماذج التقييم بشكل صحيح.";
            }
            else
            {
                errorMessage = $"An unexpected error occurred: {ex.Message}. Please check the console for more details.";
                errorMessageAr = $"حدث خطأ غير متوقع: {ex.Message}. يرجى التحقق من وحدة التحكم لمزيد من التفاصيل.";
            }

            // Show specific error message to user
            await JSRuntime.InvokeVoidAsync("alert", L(errorMessage, errorMessageAr));
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    private async Task SeedSampleData()
    {
        try
        {
            var evaluationPeriod = $"{selectedYear}-{selectedMonth:D2}";
            await EvaluationDataSeedingService.SeedSampleDataAsync(evaluationPeriod, int.Parse(selectedDepartmentId));

            // Reload the data
            await LoadEmployees();

            Console.WriteLine("Sample data seeded successfully!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error seeding sample data: {ex.Message}");
        }
    }

    // File Upload for Exceptional Work
    private SupervisorEvaluation? currentSupervisorEvaluation = null;
    private IBrowserFile? selectedFile = null;
    private string fileUploadErrorMessage = string.Empty;
    private string fileUploadSuccessMessage = string.Empty;
    private bool isUploading = false;
    private string fileInputKey = Guid.NewGuid().ToString();

    private async Task ShowExceptionalWorkUploadModal(SupervisorEvaluation supervisorEval)
    {
        try
        {
            currentSupervisorEvaluation = supervisorEval;
            selectedFile = null;
            fileUploadErrorMessage = string.Empty;
            fileUploadSuccessMessage = string.Empty;

            // Reset file input
            fileInputKey = Guid.NewGuid().ToString();

            // Show modal using Bootstrap JavaScript
            await JSRuntime.InvokeVoidAsync("showExceptionalWorkUploadModal");

            StateHasChanged();
        }
        catch (Exception ex)
        {
            fileUploadErrorMessage = $"Error opening upload modal: {ex.Message}";
            StateHasChanged();
        }
    }

    private async Task OnExceptionalWorkFileSelected(InputFileChangeEventArgs e)
    {
        try
        {
            fileUploadErrorMessage = string.Empty;
            fileUploadSuccessMessage = string.Empty;

            if (e == null || e.File == null)
            {
                selectedFile = null;
                StateHasChanged();
                return;
            }

            // Validate file
            if (e.File.Size > 10 * 1024 * 1024) // 10MB
            {
                fileUploadErrorMessage = L("File size exceeds maximum limit of 10MB", "حجم الملف يتجاوز الحد الأقصى 10 ميجابايت");
                selectedFile = null;
                StateHasChanged();
                return;
            }

            var allowedExtensions = new[] { ".pdf", ".doc", ".docx", ".jpg", ".jpeg", ".png", ".txt", ".xlsx", ".xls" };
            var fileExtension = Path.GetExtension(e.File.Name).ToLowerInvariant();
            if (!allowedExtensions.Contains(fileExtension))
            {
                fileUploadErrorMessage = L($"File type {fileExtension} is not allowed", $"نوع الملف {fileExtension} غير مسموح");
                selectedFile = null;
                StateHasChanged();
                return;
            }

            selectedFile = e.File;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            fileUploadErrorMessage = $"Error selecting file: {ex.Message}";
            selectedFile = null;
            StateHasChanged();
        }
    }

    private async Task UploadExceptionalWorkFile()
    {
        if (currentSupervisorEvaluation == null || selectedFile == null)
        {
            fileUploadErrorMessage = L("Please select a file to upload", "يرجى اختيار ملف للرفع");
            return;
        }

        try
        {
            isUploading = true;
            fileUploadErrorMessage = string.Empty;
            fileUploadSuccessMessage = string.Empty;
            StateHasChanged();

            // First, save the supervisor evaluation if it doesn't exist in database yet
            await EnsureSupervisorEvaluationSaved(currentSupervisorEvaluation);

            // Upload the file
            var attachment = await EvaluationAttachmentService.UploadSupervisorEvaluationAttachmentAsync(
                currentSupervisorEvaluation.Id,
                selectedFile,
                EvaluationAttachmentTypes.ExceptionalWork,
                $"Exceptional work documentation for {GetUserDisplayName(currentSupervisorEvaluation.Employee)}",
                $"توثيق العمل المميز لـ {GetUserDisplayName(currentSupervisorEvaluation.Employee)}",
                currentUser?.Id ?? "System"
            );

            fileUploadSuccessMessage = L("File uploaded successfully", "تم رفع الملف بنجاح");
            selectedFile = null;
            fileInputKey = Guid.NewGuid().ToString();

            // Close modal after successful upload
            await JSRuntime.InvokeVoidAsync("hideExceptionalWorkUploadModal");

            StateHasChanged();
        }
        catch (Exception ex)
        {
            fileUploadErrorMessage = $"Error uploading file: {ex.Message}";
        }
        finally
        {
            isUploading = false;
            StateHasChanged();
        }
    }

    private async Task EnsureSupervisorEvaluationSaved(SupervisorEvaluation supervisorEval)
    {
        // Check if this supervisor evaluation exists in database
        var existing = await DbContext.SupervisorEvaluations
            .FirstOrDefaultAsync(s => s.EmployeeId == supervisorEval.EmployeeId &&
                                     s.EvaluationPeriod == supervisorEval.EvaluationPeriod);

        if (existing == null)
        {
            // Save the supervisor evaluation first
            supervisorEval.CreatedAt = DateTime.UtcNow;
            supervisorEval.UpdatedAt = DateTime.UtcNow;
            DbContext.SupervisorEvaluations.Add(supervisorEval);
            await DbContext.SaveChangesAsync();
        }
        else
        {
            // Update the existing evaluation
            supervisorEval.Id = existing.Id;
        }
    }

    // Helper methods
    private string GetMonthName(int month)
    {
        var monthNames = IsArabic 
            ? new[] { "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر" }
            : new[] { "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December" };
        return monthNames[month - 1];
    }

    private string GetDepartmentName(Department dept) => IsArabic ? dept.NameAr : dept.NameEn;
    private string GetUserDisplayName(ApplicationUser user) => IsArabic ? user.ArabicName : user.EnglishName;
    private string GetUserInitials(ApplicationUser user) => string.Join("", user.EnglishName.Split(' ').Select(n => n.FirstOrDefault()));

    private int GetWorkingDaysInMonth(int year, int month)
    {
        var daysInMonth = DateTime.DaysInMonth(year, month);
        var workingDays = 0;
        for (int day = 1; day <= daysInMonth; day++)
        {
            var date = new DateTime(year, month, day);
            if (date.DayOfWeek != DayOfWeek.Friday && date.DayOfWeek != DayOfWeek.Saturday)
                workingDays++;
        }
        return workingDays;
    }

    // Search functionality methods
    private void FilterEmployees()
    {
        if (string.IsNullOrWhiteSpace(searchQuery))
        {
            filteredEmployees = employees.ToList();
        }
        else
        {
            var query = searchQuery.Trim().ToLowerInvariant();
            filteredEmployees = employees.Where(emp =>
                (!string.IsNullOrEmpty(emp.EnglishName) && emp.EnglishName.ToLowerInvariant().Contains(query)) ||
                (!string.IsNullOrEmpty(emp.ArabicName) && emp.ArabicName.ToLowerInvariant().Contains(query)) ||
                (!string.IsNullOrEmpty(emp.EmployeeId) && emp.EmployeeId.ToLowerInvariant().Contains(query))
            ).ToList();
        }
        StateHasChanged();
    }

    private void ClearSearch()
    {
        searchQuery = "";
        FilterEmployees();
    }

    private void InitializeFilteredEmployees()
    {
        filteredEmployees = employees.ToList();
    }
}

<!-- File Upload Modal for Exceptional Work -->
<div class="modal fade" id="exceptionalWorkUploadModal" tabindex="-1" aria-labelledby="exceptionalWorkUploadModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exceptionalWorkUploadModalLabel">
                    <i class="fas fa-upload @GetMarginEnd(1)"></i>
                    @L("Upload Supporting Document", "رفع مستند داعم")
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                @if (currentSupervisorEvaluation != null)
                {
                    <div class="mb-3">
                        <strong>@L("Employee", "الموظف"):</strong> @GetUserDisplayName(currentSupervisorEvaluation.Employee)
                    </div>
                }

                @if (!string.IsNullOrEmpty(fileUploadErrorMessage))
                {
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle @GetMarginEnd(1)"></i>
                        @fileUploadErrorMessage
                        <button type="button" class="btn-close" @onclick="@(() => fileUploadErrorMessage = string.Empty)"></button>
                    </div>
                }

                @if (!string.IsNullOrEmpty(fileUploadSuccessMessage))
                {
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle @GetMarginEnd(1)"></i>
                        @fileUploadSuccessMessage
                        <button type="button" class="btn-close" @onclick="@(() => fileUploadSuccessMessage = string.Empty)"></button>
                    </div>
                }

                <div class="mb-3">
                    <label class="form-label fw-bold">
                        @L("Select File", "اختر ملف") *
                    </label>
                    <InputFile class="form-control"
                               OnChange="OnExceptionalWorkFileSelected"
                               accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt,.xlsx,.xls"
                               @key="@fileInputKey" />
                    <div class="form-text">
                        @L("Maximum file size: 10MB. Allowed types: PDF, DOC, DOCX, JPG, PNG, TXT, XLSX, XLS",
                           "الحد الأقصى لحجم الملف: 10 ميجابايت. الأنواع المسموحة: PDF, DOC, DOCX, JPG, PNG, TXT, XLSX, XLS")
                    </div>
                </div>

                @if (selectedFile != null)
                {
                    <div class="alert alert-info">
                        <i class="fas fa-file @GetMarginEnd(1)"></i>
                        <strong>@L("Selected File", "الملف المحدد"):</strong> @selectedFile.Name
                        <br />
                        <small>@L("Size", "الحجم"): @((selectedFile.Size / 1024.0 / 1024.0).ToString("F2")) MB</small>
                    </div>
                }
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" disabled="@isUploading">
                    @L("Cancel", "إلغاء")
                </button>
                <button type="button" class="btn btn-primary" @onclick="UploadExceptionalWorkFile"
                        disabled="@(selectedFile == null || isUploading)">
                    @if (isUploading)
                    {
                        <span class="spinner-border spinner-border-sm @GetMarginEnd(1)" role="status" aria-hidden="true"></span>
                        @L("Uploading...", "جاري الرفع...")
                    }
                    else
                    {
                        <i class="fas fa-upload @GetMarginEnd(1)"></i>
                        @L("Upload", "رفع")
                    }
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    window.showExceptionalWorkUploadModal = function() {
        var modal = new bootstrap.Modal(document.getElementById('exceptionalWorkUploadModal'));
        modal.show();
    };

    window.hideExceptionalWorkUploadModal = function() {
        var modal = bootstrap.Modal.getInstance(document.getElementById('exceptionalWorkUploadModal'));
        if (modal) {
            modal.hide();
        }
    };
</script>
