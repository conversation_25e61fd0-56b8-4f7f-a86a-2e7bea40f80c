@page "/debug/current-user"
@using Microsoft.AspNetCore.Identity
@using Microsoft.EntityFrameworkCore
@using System.ComponentModel.DataAnnotations
@using EmployeeRatingSystem.Blazor.Data
@using EmployeeRatingSystem.Blazor.Models
@using Microsoft.AspNetCore.Components.Authorization
@inject ApplicationDbContext DbContext
@inject UserManager<ApplicationUser> UserManager
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider

<PageTitle>Current User Debug</PageTitle>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-user-cog me-2"></i>
                        Current User Debug Information
                    </h4>
                </div>
                <div class="card-body">
                    
                    <!-- Authentication State -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-shield-alt me-2"></i>Authentication State</h5>
                        </div>
                        <div class="card-body">
                            @if (authState != null)
                            {
                                <p><strong>Is Authenticated:</strong> @authState.User.Identity?.IsAuthenticated</p>
                                <p><strong>Identity Name:</strong> @authState.User.Identity?.Name</p>
                                <p><strong>Authentication Type:</strong> @authState.User.Identity?.AuthenticationType</p>
                                
                                <h6 class="mt-3">Claims:</h6>
                                <ul>
                                    @foreach (var claim in authState.User.Claims)
                                    {
                                        <li><strong>@claim.Type:</strong> @claim.Value</li>
                                    }
                                </ul>
                            }
                            else
                            {
                                <p class="text-warning">Authentication state not loaded</p>
                            }
                        </div>
                    </div>

                    <!-- Current User Info -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-user me-2"></i>Current User Information</h5>
                        </div>
                        <div class="card-body">
                            @if (currentUser != null)
                            {
                                <p><strong>ID:</strong> @currentUser.Id</p>
                                <p><strong>Employee ID:</strong> @currentUser.EmployeeId</p>
                                <p><strong>Email:</strong> @currentUser.Email</p>
                                <p><strong>Username:</strong> @currentUser.UserName</p>
                                <p><strong>English Name:</strong> @currentUser.EnglishName</p>
                                <p><strong>Arabic Name:</strong> @currentUser.ArabicName</p>
                                <p><strong>Role:</strong> @currentUser.Role (@GetRoleDisplayName(currentUser.Role))</p>
                                <p><strong>Is Active:</strong> @currentUser.IsActive</p>
                                <p><strong>Is Deleted:</strong> @currentUser.IsDeleted</p>
                                <p><strong>Created At:</strong> @currentUser.CreatedAt</p>
                                <p><strong>Updated At:</strong> @currentUser.UpdatedAt</p>
                                
                                <div class="mt-3">
                                    <strong>Can Create Users:</strong>
                                    @if (CanCreateUsers())
                                    {
                                        <span class="badge bg-success">✓ Yes</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-danger">✗ No</span>
                                    }
                                </div>
                            }
                            else
                            {
                                <p class="text-warning">Current user not found or not loaded</p>
                            }
                        </div>
                    </div>

                    <!-- All Users List -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-users me-2"></i>All Users in Database</h5>
                        </div>
                        <div class="card-body">
                            @if (allUsers != null && allUsers.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Employee ID</th>
                                                <th>Email</th>
                                                <th>English Name</th>
                                                <th>Arabic Name</th>
                                                <th>Role</th>
                                                <th>Active</th>
                                                <th>Deleted</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var user in allUsers)
                                            {
                                                <tr class="@(user.Id == currentUser?.Id ? "table-primary" : "")">
                                                    <td>@user.EmployeeId</td>
                                                    <td>@user.Email</td>
                                                    <td>@user.EnglishName</td>
                                                    <td>@user.ArabicName</td>
                                                    <td>@GetRoleDisplayName(user.Role)</td>
                                                    <td>
                                                        @if (user.IsActive)
                                                        {
                                                            <span class="badge bg-success">Active</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="badge bg-secondary">Inactive</span>
                                                        }
                                                    </td>
                                                    <td>
                                                        @if (user.IsDeleted)
                                                        {
                                                            <span class="badge bg-danger">Deleted</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="badge bg-success">Active</span>
                                                        }
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <p class="text-warning">No users found in database</p>
                            }
                        </div>
                    </div>

                    <!-- Test Actions -->
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-tools me-2"></i>Test Actions</h5>
                        </div>
                        <div class="card-body">
                            <button class="btn btn-primary me-2" @onclick="RefreshData">
                                <i class="fas fa-refresh me-2"></i>
                                Refresh Data
                            </button>
                            <button class="btn btn-info me-2" @onclick="TestUserLookup">
                                <i class="fas fa-search me-2"></i>
                                Test User Lookup
                            </button>
                        </div>
                    </div>

                    <!-- Test Results -->
                    @if (!string.IsNullOrEmpty(testResult))
                    {
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5><i class="fas fa-clipboard-check me-2"></i>Test Results</h5>
                            </div>
                            <div class="card-body">
                                <pre class="bg-light p-3 rounded">@testResult</pre>
                            </div>
                        </div>
                    }

                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private AuthenticationState? authState = null;
    private ApplicationUser? currentUser = null;
    private List<ApplicationUser> allUsers = new();
    private string testResult = "";

    protected override async Task OnInitializedAsync()
    {
        await RefreshData();
    }

    private async Task RefreshData()
    {
        try
        {
            // Load authentication state
            authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            
            // Load current user
            await LoadCurrentUser();
            
            // Load all users
            allUsers = await DbContext.Users
                .OrderBy(u => u.EmployeeId)
                .ToListAsync();
                
            StateHasChanged();
        }
        catch (Exception ex)
        {
            testResult = $"Error refreshing data: {ex.Message}";
        }
    }

    private async Task LoadCurrentUser()
    {
        try
        {
            if (authState?.User.Identity?.IsAuthenticated == true)
            {
                var userEmail = authState.User.Identity.Name;
                if (!string.IsNullOrEmpty(userEmail))
                {
                    currentUser = await UserManager.FindByEmailAsync(userEmail);
                    testResult += $"Found current user by email '{userEmail}': {currentUser?.EmployeeId}\n";
                }
                else
                {
                    testResult += "No email found in authentication state\n";
                }
            }
            else
            {
                testResult += "User is not authenticated\n";
            }
        }
        catch (Exception ex)
        {
            testResult += $"Error loading current user: {ex.Message}\n";
        }
    }

    private async Task TestUserLookup()
    {
        testResult = "Testing user lookup...\n";
        
        try
        {
            // Test finding users by different methods
            var allUsersCount = await DbContext.Users.CountAsync();
            testResult += $"Total users in database: {allUsersCount}\n";
            
            var activeUsersCount = await DbContext.Users.CountAsync(u => u.IsActive && !u.IsDeleted);
            testResult += $"Active users: {activeUsersCount}\n";
            
            if (authState?.User.Identity?.IsAuthenticated == true)
            {
                var userEmail = authState.User.Identity.Name;
                testResult += $"Looking for user with email: '{userEmail}'\n";
                
                var userByEmail = await UserManager.FindByEmailAsync(userEmail ?? "");
                testResult += $"Found by UserManager: {userByEmail?.EmployeeId ?? "Not found"}\n";
                
                var userByDbContext = await DbContext.Users
                    .FirstOrDefaultAsync(u => u.Email == userEmail);
                testResult += $"Found by DbContext: {userByDbContext?.EmployeeId ?? "Not found"}\n";
            }
            
            StateHasChanged();
        }
        catch (Exception ex)
        {
            testResult += $"Error in test: {ex.Message}\n";
        }
    }

    private bool CanCreateUsers()
    {
        return currentUser?.Role == UserRole.SUPER_ADMIN || 
               currentUser?.Role == UserRole.EXCELLENCE_TEAM;
    }

    private string GetRoleDisplayName(UserRole role)
    {
        return role switch
        {
            UserRole.SUPER_ADMIN => "Super Admin (سوبر أدمين)",
            UserRole.MANAGER => "Manager (المدير)",
            UserRole.SUPERVISOR => "Direct Supervisor (المسؤول المباشر)",
            UserRole.EXCELLENCE_TEAM => "Excellence Team (فريق التميز)",
            UserRole.EMPLOYEE => "Employee (موظف)",
            _ => role.ToString()
        };
    }
}
