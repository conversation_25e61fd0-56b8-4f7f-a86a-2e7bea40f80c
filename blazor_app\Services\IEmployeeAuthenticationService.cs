using EmployeeRatingSystem.Blazor.Models;
using Microsoft.AspNetCore.Identity;

namespace EmployeeRatingSystem.Blazor.Services
{
    /// <summary>
    /// Service interface for Employee ID-based authentication and registration.
    /// Handles authentication using Employee ID instead of email.
    /// </summary>
    public interface IEmployeeAuthenticationService
    {
        /// <summary>
        /// Authenticate user using Employee ID and password
        /// </summary>
        Task<SignInResult> SignInWithEmployeeIdAsync(string employeeId, string password, bool rememberMe = false, bool lockoutOnFailure = true);

        /// <summary>
        /// Register new user using Employee ID
        /// </summary>
        Task<(IdentityResult result, ApplicationUser? user)> RegisterWithEmployeeIdAsync(
            string employeeId, 
            string password, 
            string englishName, 
            string arabicName, 
            string email, 
            string preferredLanguage = "en");

        /// <summary>
        /// Check if Employee ID is available for registration
        /// </summary>
        Task<bool> IsEmployeeIdAvailableAsync(string employeeId);

        /// <summary>
        /// Get user by Employee ID
        /// </summary>
        Task<ApplicationUser?> FindByEmployeeIdAsync(string employeeId);

        /// <summary>
        /// Validate Employee ID format
        /// </summary>
        bool IsValidEmployeeIdFormat(string employeeId);

        /// <summary>
        /// Sign out current user
        /// </summary>
        Task SignOutAsync();

        /// <summary>
        /// Perform actual sign-in (for use in API endpoints)
        /// </summary>
        Task<SignInResult> PerformActualSignInAsync(string employeeId, string password, bool rememberMe = false);

        /// <summary>
        /// Change user password
        /// </summary>
        Task<IdentityResult> ChangePasswordAsync(ApplicationUser user, string currentPassword, string newPassword);

        /// <summary>
        /// Reset password for user (Admin function)
        /// </summary>
        Task<IdentityResult> ResetPasswordAsync(ApplicationUser user, string newPassword);

        /// <summary>
        /// Lock user account
        /// </summary>
        Task<IdentityResult> LockUserAsync(ApplicationUser user, DateTimeOffset? lockoutEnd = null);

        /// <summary>
        /// Unlock user account by Employee ID
        /// </summary>
        Task<bool> UnlockUserAsync(string employeeId);

        /// <summary>
        /// Check if user is locked out
        /// </summary>
        Task<bool> IsLockedOutAsync(ApplicationUser user);

        /// <summary>
        /// Get lockout end date for user
        /// </summary>
        Task<DateTimeOffset?> GetLockoutEndDateAsync(ApplicationUser user);
    }
}
