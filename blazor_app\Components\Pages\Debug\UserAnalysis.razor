@page "/debug/user-analysis"
@using Microsoft.AspNetCore.Identity
@using Microsoft.EntityFrameworkCore
@using EmployeeRatingSystem.Blazor.Data
@using EmployeeRatingSystem.Blazor.Models
@inject ApplicationDbContext DbContext
@inject UserManager<ApplicationUser> UserManager
@inject IJSRuntime JSRuntime

<PageTitle>User Analysis</PageTitle>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-users me-2"></i>User Database Analysis</h3>
                    <p class="mb-0">Analyze existing users and identify potential duplicates</p>
                </div>
                <div class="card-body">
                    @if (isLoading)
                    {
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Loading user data...</p>
                        </div>
                    }
                    else
                    {
                        <div class="row">
                            <div class="col-md-6">
                                <h5>All Users in Database</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Employee ID</th>
                                                <th>Email</th>
                                                <th>Username</th>
                                                <th>Normalized Email</th>
                                                <th>Normalized Username</th>
                                                <th>Active</th>
                                                <th>Deleted</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var user in allUsers)
                                            {
                                                <tr class="@(user.IsDeleted ? "table-danger" : user.IsActive ? "table-success" : "table-warning")">
                                                    <td>@user.EmployeeId</td>
                                                    <td>@user.Email</td>
                                                    <td>@user.UserName</td>
                                                    <td>@user.NormalizedEmail</td>
                                                    <td>@user.NormalizedUserName</td>
                                                    <td>@(user.IsActive ? "Yes" : "No")</td>
                                                    <td>@(user.IsDeleted ? "Yes" : "No")</td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <h5>Duplicate Analysis</h5>
                                
                                @if (duplicateEmails.Any())
                                {
                                    <div class="alert alert-warning">
                                        <h6>Duplicate Emails Found:</h6>
                                        @foreach (var email in duplicateEmails)
                                        {
                                            <div>@email</div>
                                        }
                                    </div>
                                }

                                @if (duplicateEmployeeIds.Any())
                                {
                                    <div class="alert alert-warning">
                                        <h6>Duplicate Employee IDs Found:</h6>
                                        @foreach (var empId in duplicateEmployeeIds)
                                        {
                                            <div>@empId</div>
                                        }
                                    </div>
                                }

                                @if (duplicateNormalizedUsernames.Any())
                                {
                                    <div class="alert alert-danger">
                                        <h6>Duplicate Normalized Usernames Found:</h6>
                                        @foreach (var username in duplicateNormalizedUsernames)
                                        {
                                            <div>@username</div>
                                        }
                                    </div>
                                }

                                @if (!duplicateEmails.Any() && !duplicateEmployeeIds.Any() && !duplicateNormalizedUsernames.Any())
                                {
                                    <div class="alert alert-success">
                                        <h6>No Duplicates Found</h6>
                                        <p>All users have unique emails, employee IDs, and normalized usernames.</p>
                                    </div>
                                }

                                <h6 class="mt-4">Statistics</h6>
                                <ul class="list-group">
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Total Users:</span>
                                        <span class="badge bg-primary">@allUsers.Count</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Active Users:</span>
                                        <span class="badge bg-success">@allUsers.Count(u => u.IsActive && !u.IsDeleted)</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Inactive Users:</span>
                                        <span class="badge bg-warning">@allUsers.Count(u => !u.IsActive && !u.IsDeleted)</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Deleted Users:</span>
                                        <span class="badge bg-danger">@allUsers.Count(u => u.IsDeleted)</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Users with Password Hash:</span>
                                        <span class="badge bg-info">@allUsers.Count(u => !string.IsNullOrEmpty(u.PasswordHash))</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Users without Password Hash:</span>
                                        <span class="badge bg-danger">@allUsers.Count(u => string.IsNullOrEmpty(u.PasswordHash))</span>
                                    </li>
                                </ul>

                                <div class="mt-4">
                                    <button class="btn btn-primary" @onclick="RefreshData">
                                        <i class="fas fa-refresh me-2"></i>Refresh Data
                                    </button>
                                    <button class="btn btn-warning" @onclick="CleanupDuplicates">
                                        <i class="fas fa-broom me-2"></i>Cleanup Duplicates
                                    </button>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private bool isLoading = true;
    private List<ApplicationUser> allUsers = new();
    private List<string> duplicateEmails = new();
    private List<string> duplicateEmployeeIds = new();
    private List<string> duplicateNormalizedUsernames = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
        isLoading = false;
    }

    private async Task LoadData()
    {
        try
        {
            // Load all users including deleted ones
            allUsers = await DbContext.Users
                .OrderBy(u => u.EmployeeId)
                .ToListAsync();

            // Find duplicates
            duplicateEmails = allUsers
                .Where(u => !string.IsNullOrEmpty(u.Email))
                .GroupBy(u => u.Email.ToLower())
                .Where(g => g.Count() > 1)
                .Select(g => g.Key)
                .ToList();

            duplicateEmployeeIds = allUsers
                .Where(u => !string.IsNullOrEmpty(u.EmployeeId))
                .GroupBy(u => u.EmployeeId)
                .Where(g => g.Count() > 1)
                .Select(g => g.Key)
                .ToList();

            duplicateNormalizedUsernames = allUsers
                .Where(u => !string.IsNullOrEmpty(u.NormalizedUserName))
                .GroupBy(u => u.NormalizedUserName)
                .Where(g => g.Count() > 1)
                .Select(g => g.Key)
                .ToList();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error loading data: {ex.Message}");
        }
    }

    private async Task RefreshData()
    {
        isLoading = true;
        StateHasChanged();
        await LoadData();
        isLoading = false;
        StateHasChanged();
    }

    private async Task CleanupDuplicates()
    {
        try
        {
            var confirm = await JSRuntime.InvokeAsync<bool>("confirm", 
                "This will mark duplicate users as deleted (soft delete). Are you sure?");
            
            if (!confirm) return;

            int deletedCount = 0;

            // Handle duplicate normalized usernames (most critical)
            foreach (var normalizedUsername in duplicateNormalizedUsernames)
            {
                var duplicateUsers = allUsers
                    .Where(u => u.NormalizedUserName == normalizedUsername && !u.IsDeleted)
                    .OrderBy(u => u.CreatedAt)
                    .ToList();

                // Keep the first one, mark others as deleted
                for (int i = 1; i < duplicateUsers.Count; i++)
                {
                    duplicateUsers[i].IsDeleted = true;
                    duplicateUsers[i].DeletedAt = DateTime.UtcNow;
                    deletedCount++;
                }
            }

            // Handle duplicate employee IDs
            foreach (var employeeId in duplicateEmployeeIds)
            {
                var duplicateUsers = allUsers
                    .Where(u => u.EmployeeId == employeeId && !u.IsDeleted)
                    .OrderBy(u => u.CreatedAt)
                    .ToList();

                // Keep the first one, mark others as deleted
                for (int i = 1; i < duplicateUsers.Count; i++)
                {
                    if (!duplicateUsers[i].IsDeleted) // Don't double-delete
                    {
                        duplicateUsers[i].IsDeleted = true;
                        duplicateUsers[i].DeletedAt = DateTime.UtcNow;
                        deletedCount++;
                    }
                }
            }

            await DbContext.SaveChangesAsync();
            await JSRuntime.InvokeVoidAsync("alert", $"Cleanup completed. {deletedCount} duplicate users marked as deleted.");
            await RefreshData();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error during cleanup: {ex.Message}");
        }
    }
}
