using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EmployeeRatingSystem.Blazor.Models
{
    /// <summary>
    /// Represents an Employee of the Month award record
    /// </summary>
    public class EmployeeOfTheMonth : TimeStampedModel
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// Whether this record is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Whether this record is soft deleted
        /// </summary>
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// Employee ID who won the award
        /// </summary>
        [Required]
        [StringLength(450)]
        public string EmployeeId { get; set; } = string.Empty;

        /// <summary>
        /// Reference to the employee
        /// </summary>
        [ForeignKey(nameof(EmployeeId))]
        public virtual ApplicationUser Employee { get; set; } = null!;

        /// <summary>
        /// Department ID where the employee won
        /// </summary>
        [Required]
        public int DepartmentId { get; set; }

        /// <summary>
        /// Reference to the department
        /// </summary>
        [ForeignKey(nameof(DepartmentId))]
        public virtual Department Department { get; set; } = null!;

        /// <summary>
        /// Month and year of the award (YYYY-MM format)
        /// </summary>
        [Required]
        [StringLength(7)]
        public string MonthYear { get; set; } = string.Empty;

        /// <summary>
        /// Employee's attendance percentage for the month
        /// </summary>
        [Column(TypeName = "decimal(5,4)")]
        public decimal AttendancePercentage { get; set; }

        /// <summary>
        /// Employee's work volume score
        /// </summary>
        [Column(TypeName = "decimal(10,2)")]
        public decimal WorkVolume { get; set; }

        /// <summary>
        /// Supervisor evaluation score
        /// </summary>
        [Column(TypeName = "decimal(5,4)")]
        public decimal SupervisorScore { get; set; }

        /// <summary>
        /// Total final score that led to the award
        /// </summary>
        [Column(TypeName = "decimal(5,4)")]
        public decimal TotalScore { get; set; }

        /// <summary>
        /// Justification for the award in English
        /// </summary>
        [StringLength(2000)]
        public string? JustificationEn { get; set; }

        /// <summary>
        /// Justification for the award in Arabic
        /// </summary>
        [StringLength(2000)]
        public string? JustificationAr { get; set; }

        /// <summary>
        /// Who selected this employee for the award
        /// </summary>
        [Required]
        [StringLength(450)]
        public string SelectedBy { get; set; } = string.Empty;

        /// <summary>
        /// Reference to the user who selected this employee
        /// </summary>
        [ForeignKey(nameof(SelectedBy))]
        public virtual ApplicationUser Selector { get; set; } = null!;

        /// <summary>
        /// When the selection was made
        /// </summary>
        public DateTime SelectedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Collection of attachments for exceptional work documentation
        /// </summary>
        public virtual ICollection<EmployeeOfTheMonthAttachment> Attachments { get; set; } = new List<EmployeeOfTheMonthAttachment>();
    }

    /// <summary>
    /// Represents file attachments for Employee of the Month documentation
    /// </summary>
    public class EmployeeOfTheMonthAttachment : TimeStampedModel
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// Reference to the Employee of the Month record
        /// </summary>
        [Required]
        public int EmployeeOfTheMonthId { get; set; }

        /// <summary>
        /// Navigation property to Employee of the Month
        /// </summary>
        [ForeignKey(nameof(EmployeeOfTheMonthId))]
        public virtual EmployeeOfTheMonth EmployeeOfTheMonth { get; set; } = null!;

        /// <summary>
        /// Original filename
        /// </summary>
        [Required]
        [StringLength(255)]
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// File path on server
        /// </summary>
        [Required]
        [StringLength(500)]
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// File size in bytes
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// MIME type of the file
        /// </summary>
        [StringLength(100)]
        public string? ContentType { get; set; }

        /// <summary>
        /// Description of the attachment in English
        /// </summary>
        [StringLength(500)]
        public string? DescriptionEn { get; set; }

        /// <summary>
        /// Description of the attachment in Arabic
        /// </summary>
        [StringLength(500)]
        public string? DescriptionAr { get; set; }

        /// <summary>
        /// Who uploaded this attachment
        /// </summary>
        [Required]
        [StringLength(450)]
        public string UploadedBy { get; set; } = string.Empty;

        /// <summary>
        /// Reference to the user who uploaded the attachment
        /// </summary>
        [ForeignKey(nameof(UploadedBy))]
        public virtual ApplicationUser Uploader { get; set; } = null!;
    }
}
