@page "/employee-of-the-month"
@rendermode InteractiveServer
@using EmployeeRatingSystem.Blazor.Components.Shared
@using EmployeeRatingSystem.Blazor.Services
@using EmployeeRatingSystem.Blazor.Models
@using EmployeeRatingSystem.Blazor.Data
@using Microsoft.EntityFrameworkCore
@using Microsoft.AspNetCore.Components.Authorization
@using System.Globalization
@inherits LocalizedComponentBase
@inject ApplicationDbContext DbContext
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IEmployeeOfTheMonthService EmployeeOfTheMonthService
@inject IEmployeeOfTheMonthAttachmentService AttachmentService
@inject IEvaluationCalculationService EvaluationCalculationService
@inject IEvaluationDataSeedingService EvaluationDataSeedingService

<PageTitle>@L("Employee of the Month", "موظف الشهر")</PageTitle>

<style>
    /* Clean progress bar styling */
    .employee-progress {
        background-color: #f8f9fa;
        border: none;
        border-radius: 4px;
        overflow: hidden;
    }

    .employee-progress .progress-bar {
        border-radius: 4px;
        transition: width 0.3s ease;
    }

    /* Remove any unwanted borders from table cells */
    .employee-table td {
        border: none !important;
        vertical-align: middle;
    }

    /* Clean table styling */
    .employee-table {
        border-collapse: separate;
        border-spacing: 0;
    }

    .employee-table th,
    .employee-table td {
        border-bottom: 1px solid #dee2e6;
        padding: 12px 8px;
    }

    .employee-table th {
        border-top: 2px solid #0d6efd;
        background-color: #f8f9fa;
    }
</style>

@* Authorization Check *@
@if (!isAuthenticated || currentUser == null || !CanAccessEmployeeOfTheMonth())
{
    <div class="alert alert-danger" role="alert">
        <i class="fas fa-exclamation-triangle @GetMarginEnd()"></i>
        @L("Access Denied. This page is restricted to Super Admin and Excellence Team only.", 
           "تم رفض الوصول. هذه الصفحة مقتصرة على المدير العام وفريق التميز فقط.")
    </div>
    return;
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">
                        <i class="fas fa-trophy @GetMarginEnd() text-warning"></i>
                        @L("Employee of the Month", "موظف الشهر")
                    </h1>
                    <p class="text-muted mb-0">
                        @L("Administrative page for Employee of the Month awards based on comprehensive evaluation metrics", 
                           "صفحة إدارية لجوائز موظف الشهر بناءً على مقاييس التقييم الشاملة")
                    </p>
                </div>
                <div class="d-flex gap-2">

                    <button class="btn btn-primary" @onclick="CalculateSingleEmployeeOfTheMonth" disabled="@isCalculating">
                        @if (isCalculating)
                        {
                            <span class="spinner-border spinner-border-sm @GetMarginEnd()" role="status"></span>
                        }
                        else
                        {
                            <i class="fas fa-trophy @GetMarginEnd()"></i>
                        }
                        @L("Select Employee of the Month", "اختيار موظف الشهر")
                    </button>
                    <button class="btn btn-outline-secondary" @onclick="SeedEvaluationData" disabled="@isSeeding">
                        @if (isSeeding)
                        {
                            <span class="spinner-border spinner-border-sm @GetMarginEnd()" role="status"></span>
                        }
                        else
                        {
                            <i class="fas fa-database @GetMarginEnd()"></i>
                        }
                        @L("Seed Evaluation Data", "إنشاء بيانات التقييم")
                    </button>


                    <button class="btn btn-outline-info" @onclick="VerifyDataIntegrity" disabled="@isVerifying">
                        @if (isVerifying)
                        {
                            <span class="spinner-border spinner-border-sm @GetMarginEnd()" role="status"></span>
                        }
                        else
                        {
                            <i class="fas fa-check-circle @GetMarginEnd()"></i>
                        }
                        @L("Verify Data Integrity", "التحقق من سلامة البيانات")
                    </button>
                    @if (integrityReport != null && !integrityReport.OverallConsistency)
                    {
                        <button class="btn btn-outline-warning" @onclick="FixDataIntegrityIssues" disabled="@isFixing">
                            @if (isFixing)
                            {
                                <span class="spinner-border spinner-border-sm @GetMarginEnd()" role="status"></span>
                            }
                            else
                            {
                                <i class="fas fa-wrench @GetMarginEnd()"></i>
                            }
                            @L("Fix Data Issues", "إصلاح مشاكل البيانات")
                        </button>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Period Selection -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h6 class="card-title mb-3">
                        <i class="fas fa-calendar-alt @GetMarginEnd()"></i>
                        @L("Select Period", "اختيار الفترة")
                    </h6>
                    <div class="row g-3">
                        <div class="col-md-8">
                            <label class="form-label">@L("Month/Year", "الشهر/السنة")</label>
                            <input type="month" class="form-control" value="@selectedPeriod" @onchange="OnPeriodChangedAsync" />
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button class="btn btn-outline-primary w-100" @onclick="LoadData" disabled="@isLoading">
                                @if (isLoading)
                                {
                                    <span class="spinner-border spinner-border-sm @GetMarginEnd()" role="status"></span>
                                }
                                else
                                {
                                    <i class="fas fa-search @GetMarginEnd()"></i>
                                }
                                @L("Load", "تحميل")
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading State -->
    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">@L("Loading...", "جاري التحميل...")</span>
            </div>
            <p class="mt-3 text-muted">@L("Loading Employee of the Month data...", "جاري تحميل بيانات موظف الشهر...")</p>
        </div>
    }
    else if (employeeOfTheMonthRecords.Any())
    {
        <!-- Employee of the Month Table -->
        <div class="row">
            <div class="col">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-trophy @GetMarginEnd() text-warning"></i>
                            @L("Employee of the Month - Organization Wide", "موظف الشهر - على مستوى الادارة")
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (employeeOfTheMonthRecords?.Any() == true)
                        {
                            var winner = GetEmployeeOfTheMonthWinner();
                            rankedEmployees = GetRankedEmployees();

                            @if (winner != null)
                            {
                                <!-- Employee of the Month Winner Display -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <div class="card border-warning bg-gradient" style="background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);">
                                            <div class="card-body text-center py-4">
                                                <div class="mb-3">
                                                    <i class="fas fa-crown text-warning" style="font-size: 3rem;"></i>
                                                </div>
                                                <h3 class="card-title text-dark mb-2">
                                                    @L("🏆 Employee of the Month", "🏆 موظف الشهر")
                                                </h3>
                                                <h4 class="text-primary mb-2">@GetEmployeeName(winner)</h4>
                                                <p class="text-muted mb-3">
                                                    <i class="fas fa-building @GetMarginEnd()"></i>
                                                    @GetDepartmentName(winner)
                                                </p>
                                                <div class="row text-center">
                                                    <div class="col-md-3">
                                                        <div class="border-end">
                                                            <h5 class="text-success mb-1">@FormatAttendancePercentage(winner.AttendancePercentage)</h5>
                                                            <small class="text-muted">@L("Attendance", "الحضور")</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="border-end">
                                                            <h5 class="text-info mb-1">@FormatWorkloadValue(winner.EmployeeWorkVolume)</h5>
                                                            <small class="text-muted">@L("Work Volume", "حجم العمل")</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="border-end">
                                                            <h5 class="text-warning mb-1">@FormatScore(winner.SupervisorEvaluationScore)</h5>
                                                            <small class="text-muted">@L("Supervisor Score", "تقييم المشرف")</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <h5 class="text-primary mb-1">@FormatScore(winner.TotalScore)</h5>
                                                        <small class="text-muted">@L("Total Score", "النتيجة الإجمالية")</small>
                                                    </div>
                                                </div>
                                                @if (!string.IsNullOrEmpty(winner.JustificationEn) || !string.IsNullOrEmpty(winner.JustificationAr))
                                                {
                                                    <div class="mt-3">
                                                        <p class="text-muted mb-0">
                                                            <i class="fas fa-quote-left @GetMarginEnd()"></i>
                                                            @(IsArabic ? winner.JustificationAr : winner.JustificationEn)
                                                        </p>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }

                            <!-- All Employees Ranking -->
                            <div class="row">
                                <div class="col-12">
                                    <h5 class="mb-3">
                                        <i class="fas fa-list-ol @GetMarginEnd()"></i>
                                        @L("All Employees Ranking", "ترتيب جميع الموظفين")
                                    </h5>
                                    <div class="table-responsive">
                                        <table class="table table-hover mb-0 employee-table">
                                            <thead class="table-light">
                                                <tr>
                                                    <th scope="col" class="text-center">@L("Employee ID", "الرقم الوظيفي")</th>
                                                    <th scope="col">@L("Employee Name", "اسم الموظف")</th>
                                                    <th scope="col">@L("Department", "القسم")</th>
                                                    <th scope="col" class="text-center">@L("Department Workload", "حجم عمل القسم")</th>
                                                    <th scope="col" class="text-center">@L("Employee Workload", "حجم عمل الموظف")</th>
                                                    <th scope="col" class="text-center">@L("Percentage", "النسبة المئوية")</th>
                                                    <th scope="col" class="text-center">@L("Department Ranking", "ترتيب القسم")</th>
                                                    <th scope="col" class="text-center">@L("Workload Calculation", "حساب حجم العمل")</th>
                                                    <th scope="col" class="text-center">@L("Supervisor Evaluation", "تقييم المسئول")</th>
                                                    <th scope="col" class="text-center">@L("Exceptional Work", "العمل المميز")</th>
                                                    <th scope="col" class="text-center">@L("Excellence", "التميز")</th>
                                                    <th scope="col" class="text-center">@L("80% for Workload", "80% لحجم العمل")</th>
                                                    <th scope="col" class="text-center">@L("Behavior", "السلوك")</th>
                                                    <th scope="col" class="text-center">@L("Attendance & Departure", "الحضور و الانصراف")</th>
                                                    <th scope="col" class="text-center">@L("Total", "إجمالي")</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                    @for (int i = 0; i < rankedEmployees.Count; i++)
                                    {
                                        var record = rankedEmployees[i];
                                        var isWinner = winner != null && record.EmployeeId == winner.EmployeeId;
                                        var rowClass = isWinner ? "table-warning" : "";
                                        var recordIndex = i; // Capture index for button events

                                        <tr class="@rowClass">
                                            <!-- Employee ID -->
                                            <td class="text-center">
                                                <span class="fw-medium text-primary">@record.EmployeeId</span>
                                            </td>

                                            <!-- Employee Name -->
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @if (isWinner)
                                                    {
                                                        <div class="avatar-sm bg-warning text-white rounded-circle d-flex align-items-center justify-content-center @GetMarginEnd()">
                                                            <i class="fas fa-crown"></i>
                                                        </div>
                                                    }
                                                    else
                                                    {
                                                        <div class="avatar-sm bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center @GetMarginEnd()">
                                                            <i class="fas fa-user"></i>
                                                        </div>
                                                    }
                                                    <div>
                                                        <div class="fw-medium">@GetEmployeeName(record)</div>
                                                        @if (isWinner)
                                                        {
                                                            <small class="text-warning fw-bold">@L("🏆 Employee of the Month", "🏆 موظف الشهر")</small>
                                                        }
                                                        else
                                                        {
                                                            <small class="text-muted">@record.EmployeeId</small>
                                                        }
                                                    </div>
                                                </div>
                                            </td>

                                            <!-- Department -->
                                            <td>
                                                <span class="badge bg-info">@GetDepartmentName(record)</span>
                                            </td>

                                            <!-- Department Workload -->
                                            <td class="text-center">
                                                <span class="fw-medium text-secondary">@FormatWorkloadValue(record.DepartmentWorkVolume)</span>
                                            </td>

                                            <!-- Employee Workload -->
                                            <td class="text-center">
                                                <span class="fw-medium text-primary">@FormatWorkloadValue(record.EmployeeWorkVolume)</span>
                                            </td>

                                            <!-- Percentage -->
                                            <td class="text-center">
                                                <div class="d-flex align-items-center justify-content-center">
                                                    <div class="progress employee-progress flex-grow-1 @GetMarginEnd()" style="height: 8px; max-width: 60px;">
                                                        <div class="progress-bar bg-success" style="width: @(record.WorkVolumePercentage)%"></div>
                                                    </div>
                                                    <span class="text-success fw-medium">@FormatPercentageValue(record.WorkVolumePercentage)%</span>
                                                </div>
                                            </td>

                                            <!-- Department Ranking -->
                                            <td class="text-center">
                                                <span class="badge bg-info text-white fw-medium">@record.DepartmentRanking</span>
                                                <br><small class="text-muted">@L("Rank", "ترتيب")</small>
                                            </td>

                                            <!-- Workload Calculation -->
                                            <td class="text-center">
                                                <span class="text-primary fw-medium">@record.WorkloadCalculation.ToString("F2")</span>
                                                <br><small class="text-muted">@L("35×% + 35×Rank", "35×% + 35×ترتيب")</small>
                                            </td>

                                            <!-- Supervisor Evaluation -->
                                            <td class="text-center">
                                                <span class="text-warning fw-medium">@FormatSupervisorScore(record.SupervisorEvaluationScore)</span>
                                                <br><small class="text-muted">(@FormatPercentageValue(record.SupervisorEvaluationPercentage)%)</small>
                                            </td>

                                            <!-- Exceptional Work -->
                                            <td class="text-center">
                                                <div class="d-flex flex-column align-items-center gap-2">
                                                    <div class="d-flex align-items-center gap-1">
                                                        <i class="fas fa-star text-warning"></i>
                                                        <span class="text-info fw-medium">@FormatWholeNumberScore(record.ExceptionalWorkScore)</span>
                                                    </div>

                                                    <!-- File Attachment Controls -->
                                                    <div class="btn-group-vertical" role="group">
                                                        @if (record.Id > 0)
                                                        {
                                                            <!-- Only show upload/view buttons for actual Employee of the Month winners -->
                                                            <button type="button" class="btn btn-sm btn-outline-primary"
                                                                    @onclick="@(() => ShowUploadModal(record.Id))"
                                                                    title="@L("Upload File", "رفع ملف")">
                                                                <i class="fas fa-upload"></i>
                                                            </button>
                                                            <button type="button" class="btn btn-sm btn-outline-info"
                                                                    @onclick="@(() => ShowAttachments(record.Id))"
                                                                    title="@L("View Files", "عرض الملفات")">
                                                                <i class="fas fa-paperclip"></i>
                                                                @if (GetAttachmentCount(record.Id) > 0)
                                                                {
                                                                    <span class="badge bg-danger ms-1">@GetAttachmentCount(record.Id)</span>
                                                                }
                                                            </button>
                                                        }
                                                        else
                                                        {
                                                            <!-- Disabled buttons for employees not yet selected as Employee of the Month -->
                                                            <button class="btn btn-sm btn-outline-secondary" disabled
                                                                    title="@L("File upload is only available for Employee of the Month winners", "رفع الملفات متاح فقط لموظفي الشهر الفائزين")">
                                                                <i class="fas fa-upload"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-secondary" disabled
                                                                    title="@L("No files available", "لا توجد ملفات متاحة")">
                                                                <i class="fas fa-paperclip"></i>
                                                            </button>
                                                        }
                                                    </div>
                                                </div>
                                            </td>

                                            <!-- Excellence -->
                                            <td class="text-center">
                                                <div class="d-flex flex-column align-items-center">
                                                    <i class="fas fa-medal text-success mb-1"></i>
                                                    <span class="text-success fw-medium">@FormatWholeNumberScore(record.ExcellenceScore)</span>
                                                </div>
                                            </td>

                                            <!-- 80% for Workload -->
                                            <td class="text-center">
                                                <span class="text-primary fw-medium">@record.WorkloadEightyPercent.ToString("F2")</span>
                                                <br><small class="text-muted">80% @L("Calculation", "حساب")</small>
                                            </td>

                                            <!-- Behavior -->
                                            <td class="text-center">
                                                <div class="d-flex flex-column align-items-center">
                                                    <i class="fas fa-handshake text-success mb-1"></i>
                                                    <span class="text-success fw-medium">@FormatBehaviorScore(record.BehaviorScore)</span>
                                                </div>
                                            </td>

                                            <!-- Attendance & Departure -->
                                            <td class="text-center">
                                                <div class="d-flex align-items-center justify-content-center">
                                                    <div class="progress employee-progress flex-grow-1 @GetMarginEnd()" style="height: 8px; max-width: 60px;">
                                                        <div class="progress-bar bg-primary" style="width: @(record.AttendancePercentage * 100)%"></div>
                                                    </div>
                                                    <span class="text-primary fw-medium">@FormatAttendancePercentage(record.AttendancePercentage)</span>
                                                </div>
                                            </td>

                                            <!-- Total -->
                                            <td class="text-center">
                                                <div class="d-flex flex-column align-items-center">
                                                    @if (isWinner)
                                                    {
                                                        <span class="badge bg-warning text-dark fs-6 fw-bold mb-1">
                                                            <i class="fas fa-crown"></i> @FormatPercentageValue(record.TotalScore * 100)%
                                                        </span>
                                                        <i class="fas fa-trophy text-warning"></i>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-success fs-6 fw-bold mb-1">@FormatPercentageValue(record.TotalScore * 100)%</span>
                                                        <i class="fas fa-chart-line text-success"></i>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                    }


                                            </tbody>
                                        </table>
                                    </div>


                                </div>
                            </div>
                        }
                        else
                        {
                            <!-- No Data State -->
                            <div class="text-center py-5">
                                <div class="mb-4">
                                    <i class="fas fa-trophy fa-3x text-muted"></i>
                                </div>
                                <h5 class="text-muted mb-3">@L("No evaluation data available", "لا توجد بيانات تقييم متاحة")</h5>
                                <p class="text-muted mb-4">@L("Please seed evaluation data first to see Employee of the Month results", "يرجى إنشاء بيانات التقييم أولاً لرؤية نتائج موظف الشهر")</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
    else if (!isLoading)
    {
        <!-- No Data State -->
        <div class="text-center py-5">
            <div class="mb-4">
                <i class="fas fa-trophy fa-3x text-muted"></i>
            </div>
            <h5 class="text-muted">@L("No Employee of the Month records found", "لم يتم العثور على سجلات موظف الشهر")</h5>
            <p class="text-muted">
                @L("Select a different period or calculate Employee of the Month for the current period.", 
                   "اختر فترة مختلفة أو احسب موظف الشهر للفترة الحالية.")
            </p>
        </div>
    }
</div>

<!-- Success/Error Messages -->
@if (!string.IsNullOrEmpty(successMessage))
{
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div class="toast show" role="alert">
            <div class="toast-header bg-success text-white">
                <i class="fas fa-check-circle @GetMarginEnd()"></i>
                <strong class="me-auto">@L("Success", "نجح")</strong>
                <button type="button" class="btn-close btn-close-white" @onclick="() => successMessage = string.Empty"></button>
            </div>
            <div class="toast-body">
                @successMessage
            </div>
        </div>
    </div>
}

@code {
    // Authentication and user state
    private bool isAuthenticated = false;
    private ApplicationUser? currentUser = null;

    // Component state
    private bool isLoading = false;
    private bool isCalculating = false;
    private bool isSeeding = false;
    private bool isVerifying = false;
    private bool isFixing = false;
    private string selectedPeriod = DateTime.Now.ToString("yyyy-MM");
    private List<EmployeeOfTheMonthViewModel> employeeOfTheMonthRecords = new();
    private List<EmployeeOfTheMonthViewModel> rankedEmployees = new();
    private string successMessage = string.Empty;
    private string errorMessage = string.Empty;
    private DataIntegrityReport? integrityReport = null;

    // File attachment state
    private bool isUploading = false;
    private IBrowserFile? selectedFile = null;
    private InputFile? fileInput;
    private int currentEmployeeOfTheMonthId = 0;
    private List<EmployeeOfTheMonthAttachment>? currentAttachments = null;
    private Dictionary<int, int> attachmentCounts = new();
    private FileUploadModel uploadModel = new();
    private string fileInputKey = Guid.NewGuid().ToString(); // For forcing InputFile reset

    // File upload model
    public class FileUploadModel
    {
        public string DescriptionEn { get; set; } = string.Empty;
        public string DescriptionAr { get; set; } = string.Empty;
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadUserStateAsync();
        if (CanAccessEmployeeOfTheMonth())
        {
            await LoadData();
        }
    }

    private async Task LoadUserStateAsync()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        isAuthenticated = authState.User.Identity?.IsAuthenticated ?? false;

        if (isAuthenticated)
        {
            var userIdClaim = authState.User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier);
            if (userIdClaim != null)
            {
                currentUser = await DbContext.Users.FirstOrDefaultAsync(u => u.Id == userIdClaim.Value);
            }
        }
    }

    private bool CanAccessEmployeeOfTheMonth()
    {
        if (!isAuthenticated || currentUser == null) return false;
        return currentUser.Role == UserRole.SUPER_ADMIN || currentUser.Role == UserRole.EXCELLENCE_TEAM;
    }

    private async Task LoadData()
    {
        if (!CanAccessEmployeeOfTheMonth()) return;

        isLoading = true;
        errorMessage = string.Empty;

        try
        {
            // Use the new method that shows ALL evaluated employees, not just Employee of the Month winners
            employeeOfTheMonthRecords = await EmployeeOfTheMonthService.GetAllEvaluatedEmployeesForPeriodAsync(selectedPeriod);
            rankedEmployees = GetRankedEmployees(); // Update ranked employees
            await LoadAttachmentCounts();
        }
        catch (Exception ex)
        {
            errorMessage = L("Error loading Employee of the Month data: {0}", "خطأ في تحميل بيانات موظف الشهر: {0}").Replace("{0}", ex.Message);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task OnPeriodChangedAsync(ChangeEventArgs e)
    {
        selectedPeriod = e.Value?.ToString() ?? DateTime.Now.ToString("yyyy-MM");
        await LoadData();
    }

    private async Task CalculateSingleEmployeeOfTheMonth()
    {
        if (!CanAccessEmployeeOfTheMonth() || currentUser == null) return;

        isCalculating = true;
        errorMessage = string.Empty;
        successMessage = string.Empty;

        try
        {
            // Use current month if no period selected
            var period = string.IsNullOrEmpty(selectedPeriod) ? DateTime.Now.ToString("yyyy-MM") : selectedPeriod;

            var success = await EmployeeOfTheMonthService.CalculateAndSelectSingleEmployeeOfTheMonthAsync(period, currentUser.Id);

            if (success)
            {
                successMessage = L("Single Employee of the Month selected successfully for {0}",
                                  "تم اختيار موظف الشهر الواحد بنجاح للفترة {0}").Replace("{0}", period);
                await LoadData(); // Reload data to show new results
            }
            else
            {
                errorMessage = L("Failed to select Employee of the Month. Please check the logs for details.",
                                "فشل في اختيار موظف الشهر. يرجى مراجعة السجلات للحصول على التفاصيل.");
            }
        }
        catch (Exception ex)
        {
            errorMessage = L("Error selecting Employee of the Month: {0}", "خطأ في اختيار موظف الشهر: {0}").Replace("{0}", ex.Message);
        }
        finally
        {
            isCalculating = false;
            StateHasChanged();
        }
    }

    private string GetEmployeeName(EmployeeOfTheMonthViewModel record)
    {
        return IsArabic ? record.EmployeeNameAr : record.EmployeeName;
    }

    private string GetDepartmentName(EmployeeOfTheMonthViewModel record)
    {
        return IsArabic ? record.DepartmentNameAr : record.DepartmentName;
    }

    // Score formatting methods for user-friendly display
    private string FormatWorkloadValue(decimal value)
    {
        return Math.Round(value, 0).ToString("F0");
    }

    private string FormatSupervisorScore(decimal totalScore)
    {
        // SupervisorEvaluationScore is already the total score out of 50
        // Just round to whole number for display
        return Math.Round(totalScore, 0).ToString("F0");
    }

    private string FormatBehaviorScore(decimal behaviorScore)
    {
        // Behavior score is calculated as SupervisorEvaluationScore * 0.2
        // SupervisorEvaluationScore is total score out of 50, so behavior score is out of 10
        // Round to one decimal place for display
        return Math.Round(behaviorScore, 1).ToString("F1");
    }

    private string FormatWholeNumberScore(decimal score)
    {
        return Math.Round(score, 0).ToString("F0");
    }

    private string FormatPercentageValue(decimal percentage)
    {
        return percentage.ToString("F1", System.Globalization.CultureInfo.InvariantCulture);
    }

    private string FormatAttendancePercentage(decimal attendancePercentage)
    {
        // attendancePercentage is already a decimal (0.0-1.0), multiply by 100 for display
        var percentageValue = attendancePercentage * 100;
        return Math.Round(percentageValue, 0).ToString("F0", System.Globalization.CultureInfo.InvariantCulture) + "%";
    }

    // File attachment methods

    private async Task ShowUploadModal(int employeeOfTheMonthId)
    {
        try
        {
            // Validate that this is a valid Employee of the Month record
            if (employeeOfTheMonthId <= 0)
            {
                errorMessage = L("File upload is only available for Employee of the Month winners", "رفع الملفات متاح فقط لموظفي الشهر الفائزين");
                StateHasChanged();
                return;
            }

            currentEmployeeOfTheMonthId = employeeOfTheMonthId;
            uploadModel = new FileUploadModel();
            selectedFile = null;
            errorMessage = string.Empty; // Clear any previous errors

            // Reset file input before showing modal
            await ResetFileInput();

            // Show modal using proper Bootstrap 5 JavaScript interop
            await JSRuntime.InvokeVoidAsync("showUploadModal");

            StateHasChanged();
        }
        catch (Exception ex)
        {
            errorMessage = $"Error opening upload modal: {ex.Message}";
            StateHasChanged();
        }
    }





    private async Task ShowAttachments(int employeeOfTheMonthId)
    {
        try
        {
            // Validate that this is a valid Employee of the Month record
            if (employeeOfTheMonthId <= 0)
            {
                errorMessage = L("Attachments are only available for Employee of the Month winners", "المرفقات متاحة فقط لموظفي الشهر الفائزين");
                StateHasChanged();
                return;
            }

            currentEmployeeOfTheMonthId = employeeOfTheMonthId;
            currentAttachments = await AttachmentService.GetAttachmentsAsync(employeeOfTheMonthId);
            await JSRuntime.InvokeVoidAsync("eval", "new bootstrap.Modal(document.getElementById('attachmentsModal')).show()");
        }
        catch (Exception ex)
        {
            errorMessage = $"Error loading attachments: {ex.Message}";
            StateHasChanged();
        }
    }

    private async Task OnFileSelected(InputFileChangeEventArgs e)
    {
        try
        {
            // Clear any previous errors first
            errorMessage = string.Empty;

            // Very defensive approach - check if event exists first
            if (e == null)
            {
                // Event is null - this should not happen but handle gracefully
                selectedFile = null;
                StateHasChanged();
                return;
            }

            // Now safely check if file exists
            IBrowserFile? file = null;
            try
            {
                file = e.File;
            }
            catch (Exception fileAccessException)
            {
                // Handle cases where accessing e.File throws an exception
                // This might happen with "No file was supplied" errors
                if (fileAccessException.Message.Contains("No file was supplied") ||
                    fileAccessException.Message.Contains("no file") ||
                    fileAccessException.Message.Contains("supplied"))
                {
                    // This is normal - user cancelled or no file selected
                    selectedFile = null;
                    StateHasChanged();
                    return;
                }
                else
                {
                    // Some other file access error
                    errorMessage = L("Error accessing file. Please try again.", "خطأ في الوصول للملف. يرجى المحاولة مرة أخرى.");
                    selectedFile = null;
                    await ResetFileInput();
                    StateHasChanged();
                    return;
                }
            }

            // Check if file is null after safe access
            if (file == null)
            {
                // User cancelled file selection or no file was provided
                // This is normal behavior, not an error
                selectedFile = null;
                StateHasChanged();
                return;
            }

            // Additional safety check for file properties
            try
            {
                // Access file properties to ensure the file object is valid
                var fileName = file.Name;
                var fileSize = file.Size;

                // Check if file has zero size (empty file)
                if (fileSize == 0)
                {
                    errorMessage = L("Selected file is empty. Please select a valid file.", "الملف المحدد فارغ. يرجى اختيار ملف صالح.");
                    selectedFile = null;
                    await ResetFileInput();
                    StateHasChanged();
                    return;
                }

                // Check if file name is empty or invalid
                if (string.IsNullOrWhiteSpace(fileName))
                {
                    errorMessage = L("Invalid file name", "اسم الملف غير صالح");
                    selectedFile = null;
                    await ResetFileInput();
                    StateHasChanged();
                    return;
                }

                // If we get here, we have a valid file
                selectedFile = file;
            }
            catch (Exception)
            {
                // If we can't access file properties, the file object is invalid
                errorMessage = L("Invalid file selected. Please try selecting the file again.", "ملف غير صالح محدد. يرجى المحاولة مرة أخرى.");
                selectedFile = null;
                await ResetFileInput();
                StateHasChanged();
                return;
            }

            // Validate file immediately (selectedFile is already set above)
            if (selectedFile != null)
            {
                // Check file size
                if (selectedFile.Size > 10 * 1024 * 1024) // 10MB
                {
                    errorMessage = L("File size exceeds maximum limit of 10MB", "حجم الملف يتجاوز الحد الأقصى 10 ميجابايت");
                    selectedFile = null;
                    await ResetFileInput();
                    StateHasChanged();
                    return;
                }

                // Check file extension
                var allowedExtensions = new[] { ".pdf", ".doc", ".docx", ".jpg", ".jpeg", ".png", ".txt", ".xlsx", ".xls" };
                var fileExtension = Path.GetExtension(selectedFile.Name).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                {
                    errorMessage = L($"File type {fileExtension} is not allowed", $"نوع الملف {fileExtension} غير مسموح");
                    selectedFile = null;
                    await ResetFileInput();
                    StateHasChanged();
                    return;
                }
            }

            StateHasChanged();
        }
        catch (Exception ex)
        {
            // Only show error for unexpected exceptions, not for normal "no file" cases
            if (ex.Message.Contains("No file was supplied") ||
                ex.Message.Contains("no file") ||
                ex.Message.Contains("supplied"))
            {
                // This is normal behavior when user cancels file selection
                selectedFile = null;
                StateHasChanged();
            }
            else
            {
                // This is an actual error that should be shown
                errorMessage = $"Error selecting file: {ex.Message}";
                selectedFile = null;
                await ResetFileInput();
                StateHasChanged();
            }
        }
    }

    private async Task HandleFileUpload()
    {
        if (selectedFile == null)
        {
            errorMessage = L("Please select a file to upload", "يرجى اختيار ملف للرفع");
            StateHasChanged();
            return;
        }

        if (currentUser == null)
        {
            errorMessage = L("User not authenticated", "المستخدم غير مصادق عليه");
            StateHasChanged();
            return;
        }



        if (currentEmployeeOfTheMonthId <= 0)
        {
            errorMessage = L("Invalid Employee of the Month record", "سجل موظف الشهر غير صالح");
            StateHasChanged();
            return;
        }

        try
        {
            isUploading = true;
            errorMessage = string.Empty;
            StateHasChanged();

            await AttachmentService.UploadAttachmentAsync(
                currentEmployeeOfTheMonthId,
                selectedFile,
                uploadModel.DescriptionEn ?? string.Empty,
                uploadModel.DescriptionAr ?? string.Empty,
                currentUser.Id
            );

            // Refresh attachment counts
            await LoadAttachmentCounts();

            // Close modal
            await JSRuntime.InvokeVoidAsync("hideUploadModal");

            successMessage = L("File uploaded successfully", "تم رفع الملف بنجاح");

            // Reset form
            uploadModel = new FileUploadModel();
            selectedFile = null;

            // Reset file input
            await ResetFileInput();

            StateHasChanged();
        }
        catch (Exception ex)
        {
            errorMessage = L($"Upload failed: {ex.Message}", $"فشل الرفع: {ex.Message}");
            StateHasChanged();
        }
        finally
        {
            isUploading = false;
            StateHasChanged();
        }
    }

    private async Task DownloadAttachment(int attachmentId)
    {
        try
        {
            var (fileData, fileName, contentType) = await AttachmentService.DownloadAttachmentAsync(attachmentId);
            await JSRuntime.InvokeVoidAsync("downloadFile", fileName, Convert.ToBase64String(fileData), contentType);
        }
        catch (Exception ex)
        {
            errorMessage = ex.Message;
        }
    }

    private async Task DeleteAttachment(int attachmentId)
    {
        if (currentUser == null) return;

        try
        {
            var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", L("Are you sure you want to delete this file?", "هل أنت متأكد من حذف هذا الملف؟"));
            if (!confirmed) return;

            await AttachmentService.DeleteAttachmentAsync(attachmentId, currentUser.Id);

            // Refresh attachments list
            currentAttachments = await AttachmentService.GetAttachmentsAsync(currentEmployeeOfTheMonthId);

            // Refresh attachment counts
            await LoadAttachmentCounts();

            StateHasChanged();
        }
        catch (Exception ex)
        {
            errorMessage = ex.Message;
        }
    }

    private int GetAttachmentCount(int employeeOfTheMonthId)
    {
        // Only return count for valid Employee of the Month records
        if (employeeOfTheMonthId <= 0) return 0;
        return attachmentCounts.TryGetValue(employeeOfTheMonthId, out var count) ? count : 0;
    }

    private bool IsEmployeeOfTheMonthWinner(EmployeeOfTheMonthViewModel record)
    {
        // An employee is a winner if they have a valid Employee of the Month record ID
        return record.Id > 0;
    }

    private async Task DebugEmployeeOfTheMonthRecords()
    {
        try
        {
            var actualRecords = await EmployeeOfTheMonthService.GetEmployeeOfTheMonthByPeriodAsync(selectedPeriod);
            var allEvaluated = await EmployeeOfTheMonthService.GetAllEvaluatedEmployeesForPeriodAsync(selectedPeriod);

            var debugMessage = $"Debug Info for {selectedPeriod}:\n";
            debugMessage += $"- Actual Employee of the Month records: {actualRecords.Count}\n";
            debugMessage += $"- All evaluated employees: {allEvaluated.Count}\n";
            debugMessage += $"- Employees with valid IDs (winners): {allEvaluated.Count(e => e.Id > 0)}\n";
            debugMessage += $"- Employees with ID = 0 (non-winners): {allEvaluated.Count(e => e.Id == 0)}\n";

            if (actualRecords.Any())
            {
                debugMessage += "\nActual winners:\n";
                foreach (var winner in actualRecords)
                {
                    debugMessage += $"- {winner.EmployeeName} (ID: {winner.Id}, Employee ID: {winner.EmployeeId})\n";
                }
            }

            Console.WriteLine(debugMessage);
            successMessage = "Debug information logged to console. Check browser developer tools.";
            StateHasChanged();
        }
        catch (Exception ex)
        {
            errorMessage = $"Debug error: {ex.Message}";
            StateHasChanged();
        }
    }

    private async Task LoadAttachmentCounts()
    {
        attachmentCounts.Clear();
        foreach (var record in employeeOfTheMonthRecords)
        {
            var attachments = await AttachmentService.GetAttachmentsAsync(record.Id);
            attachmentCounts[record.Id] = attachments.Count;
        }
    }

    private string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }

    private async Task ResetFileInput()
    {
        try
        {
            // Method 1: Use JavaScript to reset the input
            await JSRuntime.InvokeVoidAsync("resetFileInput", "fileUploadInput");

            // Method 2: Force Blazor to recreate the InputFile component by changing its key
            fileInputKey = Guid.NewGuid().ToString();

            // Clear the selected file
            selectedFile = null;
        }
        catch (Exception)
        {
            // Ignore errors when resetting file input, but still clear the selected file
            selectedFile = null;
            fileInputKey = Guid.NewGuid().ToString();
        }
    }

    private async Task SeedEvaluationData()
    {
        if (!CanAccessEmployeeOfTheMonth() || currentUser == null) return;

        isSeeding = true;
        errorMessage = string.Empty;
        successMessage = string.Empty;

        try
        {
            var period = string.IsNullOrEmpty(selectedPeriod) ? DateTime.Now.ToString("yyyy-MM") : selectedPeriod;
            await EvaluationDataSeedingService.SeedComprehensiveEvaluationDataAsync(period);

            successMessage = L("Evaluation data seeded successfully for {0}",
                              "تم إنشاء بيانات التقييم بنجاح للفترة {0}").Replace("{0}", period);
            await LoadData(); // Reload data to show new results
        }
        catch (Exception ex)
        {
            errorMessage = L("Error seeding evaluation data: {0}", "خطأ في إنشاء بيانات التقييم: {0}").Replace("{0}", ex.Message);
        }
        finally
        {
            isSeeding = false;
            StateHasChanged();
        }
    }

    private async Task VerifyDataIntegrity()
    {
        if (!CanAccessEmployeeOfTheMonth() || currentUser == null) return;

        isVerifying = true;
        errorMessage = string.Empty;
        successMessage = string.Empty;

        try
        {
            var period = string.IsNullOrEmpty(selectedPeriod) ? DateTime.Now.ToString("yyyy-MM") : selectedPeriod;
            integrityReport = await EmployeeOfTheMonthService.VerifyDataIntegrityAsync(period);

            if (integrityReport.OverallConsistency)
            {
                successMessage = L("Data integrity verification passed! All department totals match employee work volumes.",
                                  "تم التحقق من سلامة البيانات بنجاح! جميع إجماليات الأقسام تطابق أحجام عمل الموظفين.");
            }
            else
            {
                var inconsistentDepts = integrityReport.DepartmentReports.Where(d => !d.IsConsistent).ToList();
                errorMessage = L("Data integrity issues found in {0} departments. Check console for details.",
                                "تم العثور على مشاكل في سلامة البيانات في {0} أقسام. تحقق من وحدة التحكم للتفاصيل.")
                                .Replace("{0}", inconsistentDepts.Count.ToString());

                // Log detailed information to console
                await JSRuntime.InvokeVoidAsync("console.log", "Data Integrity Report:", integrityReport);

                // Log specific department issues
                foreach (var dept in inconsistentDepts)
                {
                    await JSRuntime.InvokeVoidAsync("console.error",
                        $"Department {dept.DepartmentName} (ID: {dept.DepartmentId}): " +
                        $"Stored Total = {dept.StoredDepartmentTotal}, " +
                        $"Calculated Total = {dept.CalculatedDepartmentTotal}, " +
                        $"Employee Count = {dept.EmployeeCount}");

                    foreach (var emp in dept.EmployeeDetails)
                    {
                        await JSRuntime.InvokeVoidAsync("console.log",
                            $"  Employee {emp.EmployeeName}: Quality={emp.QualityWork}, Oracle={emp.OracleWork}, Documented={emp.DocumentedWork}, Total={emp.TotalWork}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            errorMessage = L("Error verifying data integrity: {0}", "خطأ في التحقق من سلامة البيانات: {0}").Replace("{0}", ex.Message);
        }
        finally
        {
            isVerifying = false;
            StateHasChanged();
        }
    }

    private async Task FixDataIntegrityIssues()
    {
        if (!CanAccessEmployeeOfTheMonth() || currentUser == null) return;

        isFixing = true;
        errorMessage = string.Empty;
        successMessage = string.Empty;

        try
        {
            var period = string.IsNullOrEmpty(selectedPeriod) ? DateTime.Now.ToString("yyyy-MM") : selectedPeriod;
            var success = await EmployeeOfTheMonthService.FixDataIntegrityIssuesAsync(period);

            if (success)
            {
                successMessage = L("Data integrity issues have been fixed successfully! Please verify data integrity again.",
                                  "تم إصلاح مشاكل سلامة البيانات بنجاح! يرجى التحقق من سلامة البيانات مرة أخرى.");

                // Clear the integrity report so the fix button disappears
                integrityReport = null;
            }
            else
            {
                errorMessage = L("Failed to fix data integrity issues. Please check the logs for details.",
                                "فشل في إصلاح مشاكل سلامة البيانات. يرجى مراجعة السجلات للتفاصيل.");
            }
        }
        catch (Exception ex)
        {
            errorMessage = L("Error fixing data integrity issues: {0}", "خطأ في إصلاح مشاكل سلامة البيانات: {0}").Replace("{0}", ex.Message);
        }
        finally
        {
            isFixing = false;
            StateHasChanged();
        }
    }

    // Helper methods for single winner display
    private EmployeeOfTheMonthViewModel? GetEmployeeOfTheMonthWinner()
    {
        if (employeeOfTheMonthRecords?.Any() != true) return null;

        // Find the employee who was actually selected as Employee of the Month (has SelectedAt date)
        var winner = employeeOfTheMonthRecords.FirstOrDefault(e => e.SelectedAt != DateTime.MinValue);

        // If no one was officially selected, return the highest scorer
        return winner ?? employeeOfTheMonthRecords.OrderByDescending(e => e.TotalScore).First();
    }

    private List<EmployeeOfTheMonthViewModel> GetRankedEmployees()
    {
        if (employeeOfTheMonthRecords?.Any() != true) return new List<EmployeeOfTheMonthViewModel>();

        return employeeOfTheMonthRecords
            .OrderByDescending(e => e.TotalScore)
            .ThenByDescending(e => e.AttendancePercentage)
            .ThenByDescending(e => e.EmployeeWorkVolume)
            .ToList();
    }

    private string FormatScore(decimal score)
    {
        return Math.Round(score, 2).ToString("F2");
    }
}

<style>
    .avatar-sm {
        width: 32px;
        height: 32px;
        font-size: 12px;
    }

    .table th {
        font-weight: 600;
        font-size: 0.875rem;
        white-space: nowrap;
        vertical-align: middle;
        border-bottom: 2px solid #dee2e6;
    }

    .table td {
        vertical-align: middle;
        font-size: 0.875rem;
    }

    .progress {
        background-color: #e9ecef;
    }

    .badge {
        font-size: 0.75rem;
    }

    .table-responsive {
        border-radius: 0.375rem;
    }

    .employee-of-month-table {
        min-width: 1400px;
    }

    .employee-of-month-table th,
    .employee-of-month-table td {
        padding: 0.75rem 0.5rem;
        min-width: 100px;
    }

    .employee-of-month-table th:first-child,
    .employee-of-month-table td:first-child {
        min-width: 80px;
    }

    .employee-of-month-table th:nth-child(2),
    .employee-of-month-table td:nth-child(2) {
        min-width: 200px;
    }

    .toast-container {
        z-index: 1055;
    }

    .card-header {
        border-bottom: 1px solid rgba(0,0,0,.125);
    }

    .text-corporate-blue {
        color: #1e40af !important;
    }

    .bg-corporate-blue {
        background-color: #1e40af !important;
    }

    .text-corporate-green {
        color: #059669 !important;
    }

    .bg-corporate-green {
        background-color: #059669 !important;
    }
</style>

<script>
    window.downloadFile = function (fileName, base64Data, contentType) {
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: contentType });

        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    };

    // Reset file input safely
    window.resetFileInput = function (inputId) {
        try {
            const input = document.getElementById(inputId);
            if (input) {
                input.value = '';
                // Trigger change event to update Blazor component state
                input.dispatchEvent(new Event('change', { bubbles: true }));
            }
        } catch (error) {
            console.warn('Could not reset file input:', error);
        }
    };

    // Show upload modal
    window.showUploadModal = function () {
        try {
            const modalElement = document.getElementById('uploadModal');
            if (modalElement) {
                // Ensure Bootstrap is loaded
                if (typeof bootstrap !== 'undefined') {
                    const modal = new bootstrap.Modal(modalElement);
                    modal.show();
                } else {
                    console.error('Bootstrap is not loaded');
                }
            } else {
                console.error('Upload modal element not found');
            }
        } catch (error) {
            console.error('Error showing upload modal:', error);
        }
    };

    // Hide upload modal
    window.hideUploadModal = function () {
        try {
            const modalElement = document.getElementById('uploadModal');
            if (modalElement && typeof bootstrap !== 'undefined') {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                }
            }
        } catch (error) {
            console.error('Error hiding upload modal:', error);
        }
    };

    // Handle modal events
    document.addEventListener('DOMContentLoaded', function () {
        const uploadModal = document.getElementById('uploadModal');
        if (uploadModal) {
            uploadModal.addEventListener('shown.bs.modal', function () {
                // Reset file input when modal is shown
                window.resetFileInput('fileUploadInput');

                // Focus on file input for better UX
                const fileInput = document.getElementById('fileUploadInput');
                if (fileInput) {
                    setTimeout(() => fileInput.focus(), 100);
                }
            });

            uploadModal.addEventListener('hidden.bs.modal', function () {
                // Reset file input when modal is hidden
                window.resetFileInput('fileUploadInput');
            });
        }
    });
</script>

<!-- File Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-upload @GetMarginEnd()"></i>
                    @L("Upload Exceptional Work Documentation", "رفع توثيق العمل المميز")
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                @if (!string.IsNullOrEmpty(errorMessage))
                {
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle @GetMarginEnd()"></i>
                        @errorMessage
                        <button type="button" class="btn-close" @onclick="() => errorMessage = string.Empty"></button>
                    </div>
                }

                <div>
                    <div class="mb-3">
                        <label class="form-label" for="fileUploadInput">@L("File", "الملف") <span class="text-danger">*</span></label>
                        <InputFile class="form-control"
                                   @ref="fileInput"
                                   OnChange="OnFileSelected"
                                   accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt,.xlsx,.xls"
                                   id="fileUploadInput"
                                   multiple="false"
                                   @key="@fileInputKey" />
                        <div class="form-text">@L("Max file size: 10MB. Allowed: PDF, DOC, DOCX, JPG, PNG, TXT, XLSX", "الحد الأقصى: 10 ميجابايت. المسموح: PDF, DOC, DOCX, JPG, PNG, TXT, XLSX")</div>
                        @if (selectedFile != null)
                        {
                            <div class="mt-2">
                                <small class="text-success">
                                    <i class="fas fa-check @GetMarginEnd()"></i>
                                    @L("Selected", "محدد"): @selectedFile.Name (@FormatFileSize(selectedFile.Size))
                                </small>
                            </div>
                        }

                        @* Debug information - only show in development *@
                        @if (!string.IsNullOrEmpty(Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")) &&
                             Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development")
                        {
                            <div class="mt-2 p-2 bg-light border rounded">
                                <small class="text-muted">
                                    🐛 Debug: File Input Key: @fileInputKey.Substring(0, 8)...,
                                    Selected File: @(selectedFile?.Name ?? "None"),
                                    Employee ID: @currentEmployeeOfTheMonthId,
                                    Is Uploading: @isUploading
                                </small>
                            </div>
                        }
                    </div>

                    <div class="mb-3">
                        <label class="form-label">@L("Description (English)", "الوصف (إنجليزي)")</label>
                        <InputTextArea @bind-Value="uploadModel.DescriptionEn" class="form-control" rows="3" placeholder="@L("Describe the exceptional work (optional)", "وصف العمل المميز (اختياري)")" />
                    </div>

                    <div class="mb-3">
                        <label class="form-label">@L("Description (Arabic)", "الوصف (عربي)")</label>
                        <InputTextArea @bind-Value="uploadModel.DescriptionAr" class="form-control" rows="3" placeholder="@L("وصف العمل المميز (اختياري)", "Describe the exceptional work (optional)")" />
                    </div>

                    <div class="d-flex justify-content-end gap-2">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" disabled="@isUploading">
                            @L("Cancel", "إلغاء")
                        </button>
                        <button type="button" class="btn btn-primary" disabled="@(selectedFile == null || isUploading)" @onclick="HandleFileUpload">
                            @if (isUploading)
                            {
                                <span class="spinner-border spinner-border-sm @GetMarginEnd()"></span>
                                @L("Uploading...", "جاري الرفع...")
                            }
                            else
                            {
                                <i class="fas fa-upload @GetMarginEnd()"></i>
                                @L("Upload", "رفع")
                            }
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Attachments View Modal -->
<div class="modal fade" id="attachmentsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-paperclip @GetMarginEnd()"></i>
                    @L("Exceptional Work Documentation", "توثيق العمل المميز")
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                @if (currentAttachments?.Any() == true)
                {
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>@L("File Name", "اسم الملف")</th>
                                    <th>@L("Description", "الوصف")</th>
                                    <th>@L("Size", "الحجم")</th>
                                    <th>@L("Uploaded", "تاريخ الرفع")</th>
                                    <th>@L("Actions", "الإجراءات")</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var attachment in currentAttachments)
                                {
                                    <tr>
                                        <td>
                                            <i class="fas fa-file @GetMarginEnd()"></i>
                                            @attachment.FileName
                                        </td>
                                        <td>
                                            @if (IsArabic)
                                            {
                                                @(attachment.DescriptionAr ?? attachment.DescriptionEn)
                                            }
                                            else
                                            {
                                                @(attachment.DescriptionEn ?? attachment.DescriptionAr)
                                            }
                                        </td>
                                        <td>@FormatFileSize(attachment.FileSize)</td>
                                        <td>@attachment.CreatedAt.ToString("yyyy-MM-dd HH:mm")</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary" @onclick="() => DownloadAttachment(attachment.Id)">
                                                    <i class="fas fa-download"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" @onclick="() => DeleteAttachment(attachment.Id)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-4">
                        <i class="fas fa-file-upload fa-3x text-muted mb-3"></i>
                        <p class="text-muted">@L("No files uploaded yet", "لم يتم رفع أي ملفات بعد")</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@if (!string.IsNullOrEmpty(errorMessage))
{
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div class="toast show" role="alert">
            <div class="toast-header bg-danger text-white">
                <i class="fas fa-exclamation-triangle @GetMarginEnd()"></i>
                <strong class="me-auto">@L("Error", "خطأ")</strong>
                <button type="button" class="btn-close btn-close-white" @onclick="() => errorMessage = string.Empty"></button>
            </div>
            <div class="toast-body">
                @errorMessage
            </div>
        </div>
    </div>
}
