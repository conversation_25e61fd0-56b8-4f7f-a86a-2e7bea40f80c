using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EmployeeRatingSystem.Blazor.Models
{
    /// <summary>
    /// Represents file attachments for exceptional work documentation in evaluations
    /// </summary>
    public class EvaluationAttachment : TimeStampedModel
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// Reference to the evaluation this attachment belongs to (optional)
        /// </summary>
        public int? EvaluationId { get; set; }

        /// <summary>
        /// Navigation property to the evaluation
        /// </summary>
        [ForeignKey(nameof(EvaluationId))]
        public virtual Evaluation? Evaluation { get; set; }

        /// <summary>
        /// Reference to the supervisor evaluation this attachment belongs to (optional)
        /// </summary>
        public int? SupervisorEvaluationId { get; set; }

        /// <summary>
        /// Navigation property to the supervisor evaluation
        /// </summary>
        [ForeignKey(nameof(SupervisorEvaluationId))]
        public virtual SupervisorEvaluation? SupervisorEvaluation { get; set; }

        /// <summary>
        /// Type of attachment (exceptional_work, general_documentation, etc.)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string AttachmentType { get; set; } = "exceptional_work";

        /// <summary>
        /// Original filename as uploaded by user
        /// </summary>
        [Required]
        [StringLength(255)]
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// File path on server (relative to wwwroot)
        /// </summary>
        [Required]
        [StringLength(500)]
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// File size in bytes
        /// </summary>
        [Required]
        public long FileSize { get; set; }

        /// <summary>
        /// MIME content type
        /// </summary>
        [Required]
        [StringLength(100)]
        public string ContentType { get; set; } = string.Empty;

        /// <summary>
        /// Description of the attachment in English
        /// </summary>
        [StringLength(1000)]
        public string DescriptionEn { get; set; } = string.Empty;

        /// <summary>
        /// Description of the attachment in Arabic
        /// </summary>
        [StringLength(1000)]
        public string DescriptionAr { get; set; } = string.Empty;

        /// <summary>
        /// User ID who uploaded this attachment
        /// </summary>
        [Required]
        [StringLength(450)]
        public string UploadedBy { get; set; } = string.Empty;

        /// <summary>
        /// Navigation property to the user who uploaded this attachment
        /// </summary>
        [ForeignKey(nameof(UploadedBy))]
        public virtual ApplicationUser Uploader { get; set; } = null!;

        /// <summary>
        /// Whether this attachment is active (for soft delete)
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// When this attachment was uploaded
        /// </summary>
        public DateTime UploadedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Constants for attachment types
    /// </summary>
    public static class EvaluationAttachmentTypes
    {
        public const string ExceptionalWork = "exceptional_work";
        public const string GeneralDocumentation = "general_documentation";
        public const string SupportingEvidence = "supporting_evidence";
        public const string TrainingCertificate = "training_certificate";
        public const string ProjectDocumentation = "project_documentation";
    }
}
