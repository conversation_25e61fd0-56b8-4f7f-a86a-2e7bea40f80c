@page "/debug/user-creation-test"
@using Microsoft.AspNetCore.Identity
@using Microsoft.EntityFrameworkCore
@using System.ComponentModel.DataAnnotations
@using EmployeeRatingSystem.Blazor.Data
@using EmployeeRatingSystem.Blazor.Models
@inject ApplicationDbContext DbContext
@inject UserManager<ApplicationUser> UserManager
@inject IJSRuntime JSRuntime

<PageTitle>User Creation Test</PageTitle>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-bug me-2"></i>User Creation Test</h3>
                    <p class="mb-0">Test user creation functionality with detailed debugging</p>
                </div>
                <div class="card-body">
                    @if (isLoading)
                    {
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Loading...</p>
                        </div>
                    }
                    else
                    {
                        <div class="row">
                            <div class="col-md-6">
                                <h5>Create Test User</h5>
                                <EditForm Model="testUser" OnValidSubmit="CreateTestUser" OnInvalidSubmit="OnInvalidSubmit">
                                    <DataAnnotationsValidator />
                                    <ValidationSummary class="alert alert-danger" />

                                    <div class="mb-3">
                                        <label class="form-label">Employee ID <span class="text-danger">*</span></label>
                                        <InputText @bind-Value="testUser.EmployeeId" class="form-control" />
                                        <ValidationMessage For="() => testUser.EmployeeId" />
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Email <span class="text-danger">*</span></label>
                                        <InputText @bind-Value="testUser.Email" class="form-control" type="email" />
                                        <ValidationMessage For="() => testUser.Email" />
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">English Name <span class="text-danger">*</span></label>
                                        <InputText @bind-Value="testUser.EnglishName" class="form-control" />
                                        <ValidationMessage For="() => testUser.EnglishName" />
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Arabic Name <span class="text-danger">*</span></label>
                                        <InputText @bind-Value="testUser.ArabicName" class="form-control" />
                                        <ValidationMessage For="() => testUser.ArabicName" />
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Role <span class="text-danger">*</span></label>
                                        <InputSelect @bind-Value="testUser.Role" class="form-select">
                                            @foreach (var role in Enum.GetValues<UserRole>())
                                            {
                                                <option value="@role">@role.ToString()</option>
                                            }
                                        </InputSelect>
                                        <ValidationMessage For="() => testUser.Role" />
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Password (Optional)</label>
                                        <InputText @bind-Value="testUser.Password" class="form-control" type="password" placeholder="Password123!" />
                                        <ValidationMessage For="() => testUser.Password" />
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check">
                                            <InputCheckbox @bind-Value="testUser.IsActive" class="form-check-input" id="isActive" />
                                            <label class="form-check-label" for="isActive">Active User</label>
                                        </div>
                                    </div>

                                    <button type="submit" class="btn btn-primary" disabled="@isSaving">
                                        @if (isSaving)
                                        {
                                            <span class="spinner-border spinner-border-sm me-2"></span>
                                        }
                                        Create Test User
                                    </button>
                                </EditForm>
                            </div>

                            <div class="col-md-6">
                                <h5>Debug Log</h5>
                                <div class="bg-dark text-light p-3 rounded" style="height: 400px; overflow-y: auto;">
                                    @foreach (var log in debugLogs)
                                    {
                                        <div class="mb-1">
                                            <small class="text-muted">[@log.Timestamp.ToString("HH:mm:ss")]</small>
                                            <span class="<EMAIL>">@log.Message</span>
                                        </div>
                                    }
                                </div>
                                <button class="btn btn-sm btn-secondary mt-2" @onclick="ClearLogs">Clear Logs</button>
                            </div>
                        </div>

                        @if (createdUsers.Any())
                        {
                            <div class="mt-4">
                                <h5>Created Users</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Employee ID</th>
                                                <th>Email</th>
                                                <th>Name</th>
                                                <th>Role</th>
                                                <th>Created</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var user in createdUsers)
                                            {
                                                <tr>
                                                    <td>@user.EmployeeId</td>
                                                    <td>@user.Email</td>
                                                    <td>@user.EnglishName</td>
                                                    <td>@user.Role</td>
                                                    <td>@user.CreatedAt.ToString("yyyy-MM-dd HH:mm")</td>
                                                    <td>
                                                        <button class="btn btn-sm btn-danger" @onclick="() => DeleteUser(user)">Delete</button>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        }
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private bool isLoading = true;
    private bool isSaving = false;
    private TestUserModel testUser = new();
    private List<ApplicationUser> createdUsers = new();
    private List<DebugLog> debugLogs = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadCreatedUsers();
        AddLog("Page initialized", "info");
        isLoading = false;
    }

    private async Task LoadCreatedUsers()
    {
        try
        {
            createdUsers = await DbContext.Users
                .Where(u => u.EmployeeId != null && u.EmployeeId.StartsWith("TEST"))
                .OrderByDescending(u => u.CreatedAt)
                .ToListAsync();
            AddLog($"Loaded {createdUsers.Count} test users", "info");
        }
        catch (Exception ex)
        {
            AddLog($"Error loading users: {ex.Message}", "danger");
        }
    }

    private async Task CreateTestUser()
    {
        AddLog("Starting user creation process", "info");
        isSaving = true;
        StateHasChanged();

        try
        {
            AddLog($"Creating user with Employee ID: {testUser.EmployeeId}", "info");

            // Check for duplicates first
            var existingUserByEmail = await UserManager.FindByEmailAsync(testUser.Email);
            if (existingUserByEmail != null)
            {
                AddLog($"Duplicate email found: {testUser.Email}", "warning");
                await JSRuntime.InvokeVoidAsync("alert", $"Error: A user with email '{testUser.Email}' already exists.");
                return;
            }

            var existingUserByEmployeeId = await DbContext.Users
                .FirstOrDefaultAsync(u => u.EmployeeId == testUser.EmployeeId && !u.IsDeleted);
            if (existingUserByEmployeeId != null)
            {
                AddLog($"Duplicate Employee ID found: {testUser.EmployeeId}", "warning");
                await JSRuntime.InvokeVoidAsync("alert", $"Error: A user with Employee ID '{testUser.EmployeeId}' already exists.");
                return;
            }

            AddLog("No duplicates found, proceeding with user creation", "info");

            var newUser = new ApplicationUser
            {
                EmployeeId = testUser.EmployeeId,
                Email = testUser.Email,
                UserName = testUser.Email,
                EnglishName = testUser.EnglishName,
                ArabicName = testUser.ArabicName,
                Role = testUser.Role,
                IsActive = testUser.IsActive,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            AddLog("User object created, calling UserManager.CreateAsync", "info");

            var password = !string.IsNullOrEmpty(testUser.Password) ? testUser.Password : "Password123!";
            var result = await UserManager.CreateAsync(newUser, password);

            if (result.Succeeded)
            {
                AddLog($"User created successfully with password: {password}", "success");
                await LoadCreatedUsers();
                testUser = new TestUserModel(); // Reset form
                await JSRuntime.InvokeVoidAsync("alert", $"User created successfully!\nPassword: {password}");
            }
            else
            {
                var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                AddLog($"User creation failed: {errors}", "danger");
                await JSRuntime.InvokeVoidAsync("alert", $"Error creating user: {errors}");
            }
        }
        catch (Exception ex)
        {
            AddLog($"Exception during user creation: {ex.Message}", "danger");
            await JSRuntime.InvokeVoidAsync("alert", $"Exception: {ex.Message}");
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    private async Task OnInvalidSubmit()
    {
        AddLog("Form validation failed", "warning");
        await JSRuntime.InvokeVoidAsync("alert", "Please fill in all required fields correctly.");
    }

    private async Task DeleteUser(ApplicationUser user)
    {
        try
        {
            DbContext.Users.Remove(user);
            await DbContext.SaveChangesAsync();
            await LoadCreatedUsers();
            AddLog($"Deleted user: {user.EmployeeId}", "info");
        }
        catch (Exception ex)
        {
            AddLog($"Error deleting user: {ex.Message}", "danger");
        }
    }

    private void AddLog(string message, string level)
    {
        debugLogs.Add(new DebugLog
        {
            Timestamp = DateTime.Now,
            Message = message,
            Level = level
        });
        StateHasChanged();
    }

    private void ClearLogs()
    {
        debugLogs.Clear();
        StateHasChanged();
    }

    public class TestUserModel
    {
        [Required(ErrorMessage = "Employee ID is required")]
        public string EmployeeId { get; set; } = "TEST" + DateTime.Now.ToString("yyyyMMddHHmmss");

        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Invalid email format")]
        public string Email { get; set; } = "";

        [Required(ErrorMessage = "English name is required")]
        public string EnglishName { get; set; } = "";

        [Required(ErrorMessage = "Arabic name is required")]
        public string ArabicName { get; set; } = "";

        [Required(ErrorMessage = "Role is required")]
        public UserRole Role { get; set; } = UserRole.EMPLOYEE;

        public bool IsActive { get; set; } = true;

        public string Password { get; set; } = "";
    }

    public class DebugLog
    {
        public DateTime Timestamp { get; set; }
        public string Message { get; set; } = "";
        public string Level { get; set; } = "info";
    }
}
