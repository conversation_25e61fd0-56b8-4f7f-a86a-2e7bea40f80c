@page "/debug/auth-fix"
@using Microsoft.AspNetCore.Identity
@using Microsoft.EntityFrameworkCore
@using EmployeeRatingSystem.Blazor.Models
@using EmployeeRatingSystem.Blazor.Data
@using EmployeeRatingSystem.Blazor.Components.Shared

@inherits LocalizedComponentBase
@inject ApplicationDbContext DbContext
@inject UserManager<ApplicationUser> UserManager
@inject RoleManager<IdentityRole> RoleManager
@inject ILogger<AuthenticationFix> Logger

<PageTitle>Authentication Fix Tool</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-tools me-2"></i>
                    Authentication Fix Tool
                </h1>
                <div class="btn-group">
                    <button class="btn btn-primary" @onclick="LoadUsers" disabled="@isLoading">
                        <i class="fas fa-sync-alt me-2"></i>
                        Refresh Data
                    </button>
                    <button class="btn btn-success" @onclick="FixAllUsers" disabled="@isLoading">
                        <i class="fas fa-wrench me-2"></i>
                        Fix All Authentication Issues
                    </button>
                    <button class="btn btn-warning" @onclick="TestAllLogins" disabled="@isLoading">
                        <i class="fas fa-vial me-2"></i>
                        Test All Logins
                    </button>
                </div>
            </div>

            @if (!string.IsNullOrEmpty(message))
            {
                <div class="alert alert-@(isError ? "danger" : "success") alert-dismissible fade show">
                    <i class="fas fa-@(isError ? "exclamation-triangle" : "check-circle") me-2"></i>
                    @message
                    <button type="button" class="btn-close" @onclick="ClearMessage"></button>
                </div>
            }

            <!-- User Status Table -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>
                        User Authentication Status
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Employee ID</th>
                                    <th>Name</th>
                                    <th>Role</th>
                                    <th>Has Password</th>
                                    <th>Is Active</th>
                                    <th>Expected Password</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var user in users)
                                {
                                    var expectedPassword = GetExpectedPassword(user.EmployeeId);
                                    var hasPassword = !string.IsNullOrEmpty(user.PasswordHash);
                                    
                                    <tr class="@(hasPassword && user.IsActive ? "table-success" : "table-danger")">
                                        <td><strong>@user.EmployeeId</strong></td>
                                        <td>@user.EnglishName</td>
                                        <td>
                                            <span class="badge bg-primary">@user.Role.GetDisplayName()</span>
                                        </td>
                                        <td>
                                            @if (hasPassword)
                                            {
                                                <span class="badge bg-success">YES</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-danger">NO</span>
                                            }
                                        </td>
                                        <td>
                                            @if (user.IsActive)
                                            {
                                                <span class="badge bg-success">ACTIVE</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-warning">INACTIVE</span>
                                            }
                                        </td>
                                        <td>
                                            <code>@expectedPassword</code>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary" 
                                                    @onclick="() => FixSingleUser(user.EmployeeId)" 
                                                    disabled="@isLoading">
                                                <i class="fas fa-wrench me-1"></i>
                                                Fix
                                            </button>
                                            <button class="btn btn-sm btn-outline-info" 
                                                    @onclick="() => TestSingleLogin(user.EmployeeId)" 
                                                    disabled="@isLoading">
                                                <i class="fas fa-vial me-1"></i>
                                                Test
                                            </button>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Test Results -->
            @if (testResults.Any())
            {
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-clipboard-check me-2"></i>
                            Authentication Test Results
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Employee ID</th>
                                        <th>Password Used</th>
                                        <th>Result</th>
                                        <th>Details</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var result in testResults.OrderBy(r => r.EmployeeId))
                                    {
                                        <tr class="@(result.Success ? "table-success" : "table-danger")">
                                            <td><strong>@result.EmployeeId</strong></td>
                                            <td><code>@result.PasswordUsed</code></td>
                                            <td>
                                                @if (result.Success)
                                                {
                                                    <span class="badge bg-success">SUCCESS</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">FAILED</span>
                                                }
                                            </td>
                                            <td>@result.Details</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }

            <!-- Debug Logs -->
            @if (debugLogs.Any())
            {
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-terminal me-2"></i>
                            Debug Logs
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="bg-dark text-light p-3 rounded" style="max-height: 400px; overflow-y: auto;">
                            @foreach (var log in debugLogs.TakeLast(100))
                            {
                                <div class="mb-1">
                                    <small class="text-muted">[@log.Timestamp.ToString("HH:mm:ss")]</small>
                                    <span>@log.Message</span>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@code {
    private List<ApplicationUser> users = new();
    private List<TestResult> testResults = new();
    private List<DebugLog> debugLogs = new();
    private string message = string.Empty;
    private bool isError = false;
    private bool isLoading = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadUsers();
    }

    private async Task LoadUsers()
    {
        isLoading = true;
        try
        {
            AddLog("Loading all users from database...");
            users = await UserManager.Users.OrderBy(u => u.EmployeeId).ToListAsync();
            AddLog($"Loaded {users.Count} users");
            
            foreach (var user in users)
            {
                AddLog($"  {user.EmployeeId}: {user.EnglishName}, HasPassword: {!string.IsNullOrEmpty(user.PasswordHash)}, IsActive: {user.IsActive}");
            }
        }
        catch (Exception ex)
        {
            AddLog($"Error loading users: {ex.Message}");
            ShowMessage($"Error loading users: {ex.Message}", true);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private string GetExpectedPassword(string employeeId)
    {
        return employeeId switch
        {
            "EMP007" => "Fa35108981",
            _ => "Password123!"
        };
    }

    private async Task FixSingleUser(string employeeId)
    {
        isLoading = true;
        try
        {
            var user = users.FirstOrDefault(u => u.EmployeeId == employeeId);
            if (user == null)
            {
                AddLog($"User {employeeId} not found");
                return;
            }

            AddLog($"Fixing user {employeeId} - {user.EnglishName}...");
            
            var expectedPassword = GetExpectedPassword(employeeId);
            
            // Ensure user is active
            if (!user.IsActive)
            {
                user.IsActive = true;
                user.IsDeleted = false;
                await UserManager.UpdateAsync(user);
                AddLog($"  ✓ Activated user {employeeId}");
            }

            // Reset password using UserManager
            var token = await UserManager.GeneratePasswordResetTokenAsync(user);
            var result = await UserManager.ResetPasswordAsync(user, token, expectedPassword);
            
            if (result.Succeeded)
            {
                AddLog($"  ✓ Successfully set password for {employeeId}");
                
                // Ensure user has proper role
                var userRoles = await UserManager.GetRolesAsync(user);
                if (!userRoles.Contains(user.Role.ToString()))
                {
                    await UserManager.AddToRoleAsync(user, user.Role.ToString());
                    AddLog($"  ✓ Added role {user.Role} to {employeeId}");
                }
                
                ShowMessage($"Successfully fixed user {employeeId}", false);
            }
            else
            {
                var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                AddLog($"  ✗ Failed to set password for {employeeId}: {errors}");
                ShowMessage($"Failed to fix user {employeeId}: {errors}", true);
            }
            
            await LoadUsers(); // Refresh data
        }
        catch (Exception ex)
        {
            AddLog($"Error fixing user {employeeId}: {ex.Message}");
            ShowMessage($"Error fixing user {employeeId}: {ex.Message}", true);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task FixAllUsers()
    {
        isLoading = true;
        try
        {
            AddLog("Starting fix process for all users...");
            
            int fixedCount = 0;
            
            foreach (var user in users)
            {
                if (string.IsNullOrEmpty(user.PasswordHash) || !user.IsActive)
                {
                    await FixSingleUser(user.EmployeeId);
                    fixedCount++;
                }
            }
            
            AddLog($"Fix process completed. Fixed {fixedCount} users.");
            ShowMessage($"Successfully fixed {fixedCount} users", false);
        }
        catch (Exception ex)
        {
            AddLog($"Error during fix process: {ex.Message}");
            ShowMessage($"Error during fix process: {ex.Message}", true);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task TestSingleLogin(string employeeId)
    {
        try
        {
            var user = users.FirstOrDefault(u => u.EmployeeId == employeeId);
            if (user == null) return;

            var expectedPassword = GetExpectedPassword(employeeId);
            
            AddLog($"Testing login for {employeeId} with password '{expectedPassword}'...");
            
            var result = await UserManager.CheckPasswordAsync(user, expectedPassword);
            
            var testResult = new TestResult
            {
                EmployeeId = employeeId,
                PasswordUsed = expectedPassword,
                Success = result,
                Details = result ? "Password verification successful" : "Password verification failed"
            };
            
            testResults.RemoveAll(r => r.EmployeeId == employeeId);
            testResults.Add(testResult);
            
            AddLog($"  {(result ? "✓" : "✗")} Login test for {employeeId}: {(result ? "SUCCESS" : "FAILED")}");
            
            StateHasChanged();
        }
        catch (Exception ex)
        {
            AddLog($"Error testing login for {employeeId}: {ex.Message}");
        }
    }

    private async Task TestAllLogins()
    {
        isLoading = true;
        try
        {
            AddLog("Testing login for all users...");
            testResults.Clear();
            
            foreach (var user in users)
            {
                await TestSingleLogin(user.EmployeeId);
            }
            
            var successCount = testResults.Count(r => r.Success);
            var totalCount = testResults.Count;
            
            AddLog($"Login testing completed: {successCount}/{totalCount} users can authenticate");
            ShowMessage($"Login testing completed: {successCount}/{totalCount} users can authenticate", successCount == totalCount);
        }
        catch (Exception ex)
        {
            AddLog($"Error during login testing: {ex.Message}");
            ShowMessage($"Error during login testing: {ex.Message}", true);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void AddLog(string logMessage)
    {
        debugLogs.Add(new DebugLog { Message = logMessage, Timestamp = DateTime.Now });
        StateHasChanged();
    }

    private void ShowMessage(string msg, bool error)
    {
        message = msg;
        isError = error;
        StateHasChanged();
    }

    private void ClearMessage()
    {
        message = string.Empty;
        StateHasChanged();
    }

    private class TestResult
    {
        public string EmployeeId { get; set; } = string.Empty;
        public string PasswordUsed { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string Details { get; set; } = string.Empty;
    }

    private class DebugLog
    {
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }
}
