@page "/debug/employee-id-analysis"
@using Microsoft.AspNetCore.Identity
@using EmployeeRatingSystem.Blazor.Models
@using EmployeeRatingSystem.Blazor.Data
@using Microsoft.EntityFrameworkCore
@inject ApplicationDbContext DbContext
@inject UserManager<ApplicationUser> UserManager
@inject IJSRuntime JSRuntime

<h3>Employee ID Analysis & Duplicate Detection</h3>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-3">
                <div class="card-header">
                    <h5>All Employee IDs in Database</h5>
                    <button class="btn btn-sm btn-primary" @onclick="RefreshData">Refresh Data</button>
                </div>
                <div class="card-body">
                    @if (allUsers != null)
                    {
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Active Users (@activeUsers.Count)</h6>
                                <div style="max-height: 300px; overflow-y: auto;">
                                    <table class="table table-sm table-striped">
                                        <thead>
                                            <tr>
                                                <th>Employee ID</th>
                                                <th>Email</th>
                                                <th>Name</th>
                                                <th>Role</th>
                                                <th>Active</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var user in activeUsers)
                                            {
                                                <tr>
                                                    <td><strong>@user.EmployeeId</strong></td>
                                                    <td>@user.Email</td>
                                                    <td>@user.EnglishName</td>
                                                    <td>@user.Role</td>
                                                    <td>@(user.IsActive ? "✓" : "✗")</td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>Deleted/Inactive Users (@deletedUsers.Count)</h6>
                                <div style="max-height: 300px; overflow-y: auto;">
                                    <table class="table table-sm table-striped">
                                        <thead>
                                            <tr>
                                                <th>Employee ID</th>
                                                <th>Email</th>
                                                <th>Name</th>
                                                <th>Deleted</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var user in deletedUsers)
                                            {
                                                <tr class="text-muted">
                                                    <td><strong>@user.EmployeeId</strong></td>
                                                    <td>@user.Email</td>
                                                    <td>@user.EnglishName</td>
                                                    <td>@(user.IsDeleted ? "✓" : "✗")</td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    }
                    else
                    {
                        <p>Loading...</p>
                    }
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Test Employee ID Duplicate Check</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Employee ID to Test</label>
                        <input type="text" class="form-control" @bind="testEmployeeId" placeholder="Enter Employee ID to check" />
                    </div>
                    <div class="mb-3">
                        <button class="btn btn-primary" @onclick="CheckEmployeeIdDuplicate" disabled="@isChecking">
                            @if (isChecking)
                            {
                                <span class="spinner-border spinner-border-sm me-1"></span>
                            }
                            Check for Duplicate
                        </button>
                        <button class="btn btn-secondary ms-2" @onclick="GenerateUniqueEmployeeId">
                            Generate Unique ID
                        </button>
                    </div>
                    
                    @if (!string.IsNullOrEmpty(duplicateCheckResult))
                    {
                        <div class="alert @(duplicateCheckResult.Contains("✓") ? "alert-success" : "alert-warning")">
                            @duplicateCheckResult
                        </div>
                    }
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Employee ID Statistics</h5>
                </div>
                <div class="card-body">
                    @if (allUsers != null)
                    {
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between">
                                <span>Total Users:</span>
                                <strong>@allUsers.Count</strong>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>Active Users:</span>
                                <strong>@activeUsers.Count</strong>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>Deleted Users:</span>
                                <strong>@deletedUsers.Count</strong>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>Unique Employee IDs:</span>
                                <strong>@uniqueEmployeeIds.Count</strong>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>Duplicate Employee IDs:</span>
                                <strong class="text-danger">@duplicateEmployeeIds.Count</strong>
                            </li>
                        </ul>
                        
                        @if (duplicateEmployeeIds.Any())
                        {
                            <div class="mt-3">
                                <h6 class="text-danger">Duplicate Employee IDs Found:</h6>
                                <ul class="list-unstyled">
                                    @foreach (var duplicate in duplicateEmployeeIds)
                                    {
                                        <li class="text-danger">• @duplicate</li>
                                    }
                                </ul>
                            </div>
                        }
                    }
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Analysis Log</h5>
                </div>
                <div class="card-body">
                    <div style="height: 200px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; font-family: monospace; font-size: 12px;">
                        @foreach (var log in analysisLogs)
                        {
                            <div>@log</div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private List<ApplicationUser>? allUsers;
    private List<ApplicationUser> activeUsers = new();
    private List<ApplicationUser> deletedUsers = new();
    private List<string> uniqueEmployeeIds = new();
    private List<string> duplicateEmployeeIds = new();
    private string testEmployeeId = "";
    private bool isChecking = false;
    private string duplicateCheckResult = "";
    private List<string> analysisLogs = new();

    protected override async Task OnInitializedAsync()
    {
        await RefreshData();
    }

    private async Task RefreshData()
    {
        AddLog("Starting data refresh...");
        
        // Load all users including deleted ones
        allUsers = await DbContext.Users.ToListAsync();
        AddLog($"Loaded {allUsers.Count} total users from database");
        
        // Separate active and deleted users
        activeUsers = allUsers.Where(u => !u.IsDeleted).OrderBy(u => u.EmployeeId).ToList();
        deletedUsers = allUsers.Where(u => u.IsDeleted).OrderBy(u => u.EmployeeId).ToList();
        
        AddLog($"Active users: {activeUsers.Count}, Deleted users: {deletedUsers.Count}");
        
        // Analyze Employee ID duplicates
        var employeeIdGroups = allUsers
            .GroupBy(u => u.EmployeeId)
            .ToList();
            
        uniqueEmployeeIds = employeeIdGroups
            .Where(g => g.Count() == 1)
            .Select(g => g.Key)
            .ToList();
            
        duplicateEmployeeIds = employeeIdGroups
            .Where(g => g.Count() > 1)
            .Select(g => g.Key)
            .ToList();
            
        AddLog($"Unique Employee IDs: {uniqueEmployeeIds.Count}");
        AddLog($"Duplicate Employee IDs: {duplicateEmployeeIds.Count}");
        
        if (duplicateEmployeeIds.Any())
        {
            AddLog($"DUPLICATES FOUND: {string.Join(", ", duplicateEmployeeIds)}");
        }
        
        StateHasChanged();
    }

    private async Task CheckEmployeeIdDuplicate()
    {
        if (string.IsNullOrWhiteSpace(testEmployeeId))
        {
            duplicateCheckResult = "Please enter an Employee ID to check";
            return;
        }
        
        isChecking = true;
        duplicateCheckResult = "";
        StateHasChanged();
        
        try
        {
            AddLog($"Checking Employee ID: '{testEmployeeId}'");
            
            // Method 1: Direct database query (same as SaveUser method)
            var existingByEmployeeId = await DbContext.Users
                .FirstOrDefaultAsync(u => u.EmployeeId == testEmployeeId && !u.IsDeleted);
                
            AddLog($"Database query result: {(existingByEmployeeId != null ? $"FOUND - {existingByEmployeeId.Email}" : "NOT FOUND")}");
            
            // Method 2: Check in loaded data
            var existingInActiveUsers = activeUsers.FirstOrDefault(u => u.EmployeeId == testEmployeeId);
            var existingInDeletedUsers = deletedUsers.FirstOrDefault(u => u.EmployeeId == testEmployeeId);
            
            AddLog($"Active users check: {(existingInActiveUsers != null ? $"FOUND - {existingInActiveUsers.Email}" : "NOT FOUND")}");
            AddLog($"Deleted users check: {(existingInDeletedUsers != null ? $"FOUND - {existingInDeletedUsers.Email}" : "NOT FOUND")}");
            
            if (existingByEmployeeId != null)
            {
                duplicateCheckResult = $"❌ DUPLICATE FOUND: Employee ID '{testEmployeeId}' already exists\n" +
                                     $"Email: {existingByEmployeeId.Email}\n" +
                                     $"Name: {existingByEmployeeId.EnglishName}\n" +
                                     $"Active: {(existingByEmployeeId.IsActive ? "Yes" : "No")}\n" +
                                     $"Deleted: {(existingByEmployeeId.IsDeleted ? "Yes" : "No")}";
            }
            else
            {
                duplicateCheckResult = $"✓ AVAILABLE: Employee ID '{testEmployeeId}' is available for use";
            }
        }
        catch (Exception ex)
        {
            duplicateCheckResult = $"ERROR: {ex.Message}";
            AddLog($"Error checking duplicate: {ex.Message}");
        }
        finally
        {
            isChecking = false;
            StateHasChanged();
        }
    }

    private async Task GenerateUniqueEmployeeId()
    {
        var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
        var candidateId = $"EMP{timestamp}";
        
        // Ensure it's truly unique
        while (allUsers?.Any(u => u.EmployeeId == candidateId) == true)
        {
            await Task.Delay(1); // Wait 1ms
            timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
            candidateId = $"EMP{timestamp}";
        }
        
        testEmployeeId = candidateId;
        AddLog($"Generated unique Employee ID: {candidateId}");
        StateHasChanged();
    }

    private void AddLog(string message)
    {
        var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
        analysisLogs.Add($"[{timestamp}] {message}");
        
        // Keep only last 50 log entries
        if (analysisLogs.Count > 50)
        {
            analysisLogs.RemoveAt(0);
        }
    }
}
