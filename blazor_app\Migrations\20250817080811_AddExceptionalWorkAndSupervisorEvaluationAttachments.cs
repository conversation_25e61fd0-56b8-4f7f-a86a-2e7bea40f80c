﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EmployeeRatingSystem.Blazor.Migrations
{
    /// <inheritdoc />
    public partial class AddExceptionalWorkAndSupervisorEvaluationAttachments : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_EvaluationAttachments_Evaluations_EvaluationId",
                table: "EvaluationAttachments");

            migrationBuilder.AddColumn<string>(
                name: "ExceptionalWork",
                table: "SupervisorEvaluations",
                type: "TEXT",
                maxLength: 2000,
                nullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "EvaluationId",
                table: "EvaluationAttachments",
                type: "INTEGER",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "INTEGER");

            migrationBuilder.AddColumn<int>(
                name: "SupervisorEvaluationId",
                table: "EvaluationAttachments",
                type: "INTEGER",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_EvaluationAttachments_SupervisorEvaluationId",
                table: "EvaluationAttachments",
                column: "SupervisorEvaluationId");

            migrationBuilder.AddForeignKey(
                name: "FK_EvaluationAttachments_Evaluations_EvaluationId",
                table: "EvaluationAttachments",
                column: "EvaluationId",
                principalTable: "Evaluations",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_EvaluationAttachments_SupervisorEvaluations_SupervisorEvaluationId",
                table: "EvaluationAttachments",
                column: "SupervisorEvaluationId",
                principalTable: "SupervisorEvaluations",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_EvaluationAttachments_Evaluations_EvaluationId",
                table: "EvaluationAttachments");

            migrationBuilder.DropForeignKey(
                name: "FK_EvaluationAttachments_SupervisorEvaluations_SupervisorEvaluationId",
                table: "EvaluationAttachments");

            migrationBuilder.DropIndex(
                name: "IX_EvaluationAttachments_SupervisorEvaluationId",
                table: "EvaluationAttachments");

            migrationBuilder.DropColumn(
                name: "ExceptionalWork",
                table: "SupervisorEvaluations");

            migrationBuilder.DropColumn(
                name: "SupervisorEvaluationId",
                table: "EvaluationAttachments");

            migrationBuilder.AlterColumn<int>(
                name: "EvaluationId",
                table: "EvaluationAttachments",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "INTEGER",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_EvaluationAttachments_Evaluations_EvaluationId",
                table: "EvaluationAttachments",
                column: "EvaluationId",
                principalTable: "Evaluations",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
