﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EmployeeRatingSystem.Blazor.Migrations
{
    /// <inheritdoc />
    public partial class AddComprehensiveEvaluationSystem : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AttendanceData",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    EmployeeId = table.Column<string>(type: "TEXT", nullable: false),
                    DepartmentId = table.Column<int>(type: "INTEGER", nullable: false),
                    EvaluationPeriod = table.Column<string>(type: "TEXT", maxLength: 7, nullable: false),
                    AttendanceDays = table.Column<int>(type: "INTEGER", nullable: false),
                    TotalWorkingDays = table.Column<int>(type: "INTEGER", nullable: false),
                    AbsenceDays = table.Column<int>(type: "INTEGER", nullable: false),
                    LateArrivals = table.Column<int>(type: "INTEGER", nullable: false),
                    EarlyDepartures = table.Column<int>(type: "INTEGER", nullable: false),
                    Comments = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    RecordedBy = table.Column<string>(type: "TEXT", nullable: false),
                    RecordedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AttendanceData", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AttendanceData_AspNetUsers_EmployeeId",
                        column: x => x.EmployeeId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AttendanceData_Departments_DepartmentId",
                        column: x => x.DepartmentId,
                        principalTable: "Departments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ComprehensiveEvaluations",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    EmployeeId = table.Column<string>(type: "TEXT", nullable: false),
                    DepartmentId = table.Column<int>(type: "INTEGER", nullable: false),
                    EvaluationPeriod = table.Column<string>(type: "TEXT", maxLength: 7, nullable: false),
                    WorkVolumePercentage = table.Column<decimal>(type: "decimal(5,4)", nullable: false),
                    WorkVolumeScore = table.Column<decimal>(type: "decimal(5,4)", nullable: false),
                    AttendancePercentage = table.Column<decimal>(type: "decimal(5,4)", nullable: false),
                    AttendanceScore = table.Column<decimal>(type: "decimal(5,4)", nullable: false),
                    SupervisorScore = table.Column<decimal>(type: "decimal(5,4)", nullable: false),
                    TotalScore = table.Column<decimal>(type: "decimal(5,4)", nullable: false),
                    DepartmentRank = table.Column<int>(type: "INTEGER", nullable: true),
                    OverallRank = table.Column<int>(type: "INTEGER", nullable: true),
                    Status = table.Column<int>(type: "INTEGER", nullable: false),
                    CalculatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    CalculatedBy = table.Column<string>(type: "TEXT", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ComprehensiveEvaluations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ComprehensiveEvaluations_AspNetUsers_EmployeeId",
                        column: x => x.EmployeeId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ComprehensiveEvaluations_Departments_DepartmentId",
                        column: x => x.DepartmentId,
                        principalTable: "Departments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "DepartmentWorkVolumeTotals",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    DepartmentId = table.Column<int>(type: "INTEGER", nullable: false),
                    EvaluationPeriod = table.Column<string>(type: "TEXT", maxLength: 7, nullable: false),
                    TotalQualityProgramWork = table.Column<decimal>(type: "decimal(10,2)", nullable: false),
                    TotalOracleWork = table.Column<decimal>(type: "decimal(10,2)", nullable: false),
                    TotalDocumentedWork = table.Column<decimal>(type: "decimal(10,2)", nullable: false),
                    RecordedBy = table.Column<string>(type: "TEXT", nullable: false),
                    RecordedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DepartmentWorkVolumeTotals", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DepartmentWorkVolumeTotals_Departments_DepartmentId",
                        column: x => x.DepartmentId,
                        principalTable: "Departments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MonthlyWorkingDays",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    EvaluationPeriod = table.Column<string>(type: "TEXT", maxLength: 7, nullable: false),
                    TotalWorkingDays = table.Column<int>(type: "INTEGER", nullable: false),
                    Holidays = table.Column<int>(type: "INTEGER", nullable: false),
                    Weekends = table.Column<int>(type: "INTEGER", nullable: false),
                    Comments = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    ConfiguredBy = table.Column<string>(type: "TEXT", nullable: false),
                    ConfiguredAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MonthlyWorkingDays", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "SupervisorEvaluations",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    EmployeeId = table.Column<string>(type: "TEXT", nullable: false),
                    SupervisorId = table.Column<string>(type: "TEXT", nullable: false),
                    DepartmentId = table.Column<int>(type: "INTEGER", nullable: false),
                    EvaluationPeriod = table.Column<string>(type: "TEXT", maxLength: 7, nullable: false),
                    WorkQualityScore = table.Column<decimal>(type: "TEXT", nullable: false),
                    ProductivityScore = table.Column<decimal>(type: "TEXT", nullable: false),
                    InitiativeScore = table.Column<decimal>(type: "TEXT", nullable: false),
                    TeamworkScore = table.Column<decimal>(type: "TEXT", nullable: false),
                    CommunicationScore = table.Column<decimal>(type: "TEXT", nullable: false),
                    Comments = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    AreasForImprovement = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    Strengths = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    NextPeriodGoals = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    Status = table.Column<int>(type: "INTEGER", nullable: false),
                    SubmittedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    ApprovedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    ApprovedBy = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SupervisorEvaluations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SupervisorEvaluations_AspNetUsers_EmployeeId",
                        column: x => x.EmployeeId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_SupervisorEvaluations_AspNetUsers_SupervisorId",
                        column: x => x.SupervisorId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_SupervisorEvaluations_Departments_DepartmentId",
                        column: x => x.DepartmentId,
                        principalTable: "Departments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "WorkVolumeData",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    EmployeeId = table.Column<string>(type: "TEXT", nullable: false),
                    DepartmentId = table.Column<int>(type: "INTEGER", nullable: false),
                    EvaluationPeriod = table.Column<string>(type: "TEXT", maxLength: 7, nullable: false),
                    QualityProgramWork = table.Column<decimal>(type: "decimal(10,2)", nullable: false),
                    OracleWork = table.Column<decimal>(type: "decimal(10,2)", nullable: false),
                    DocumentedWork = table.Column<decimal>(type: "decimal(10,2)", nullable: false),
                    Comments = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    RecordedBy = table.Column<string>(type: "TEXT", nullable: false),
                    RecordedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WorkVolumeData", x => x.Id);
                    table.ForeignKey(
                        name: "FK_WorkVolumeData_AspNetUsers_EmployeeId",
                        column: x => x.EmployeeId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_WorkVolumeData_Departments_DepartmentId",
                        column: x => x.DepartmentId,
                        principalTable: "Departments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AttendanceData_DepartmentId",
                table: "AttendanceData",
                column: "DepartmentId");

            migrationBuilder.CreateIndex(
                name: "IX_AttendanceData_EmployeeId",
                table: "AttendanceData",
                column: "EmployeeId");

            migrationBuilder.CreateIndex(
                name: "IX_ComprehensiveEvaluations_DepartmentId",
                table: "ComprehensiveEvaluations",
                column: "DepartmentId");

            migrationBuilder.CreateIndex(
                name: "IX_ComprehensiveEvaluations_EmployeeId",
                table: "ComprehensiveEvaluations",
                column: "EmployeeId");

            migrationBuilder.CreateIndex(
                name: "IX_DepartmentWorkVolumeTotals_DepartmentId",
                table: "DepartmentWorkVolumeTotals",
                column: "DepartmentId");

            migrationBuilder.CreateIndex(
                name: "IX_SupervisorEvaluations_DepartmentId",
                table: "SupervisorEvaluations",
                column: "DepartmentId");

            migrationBuilder.CreateIndex(
                name: "IX_SupervisorEvaluations_EmployeeId",
                table: "SupervisorEvaluations",
                column: "EmployeeId");

            migrationBuilder.CreateIndex(
                name: "IX_SupervisorEvaluations_SupervisorId",
                table: "SupervisorEvaluations",
                column: "SupervisorId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkVolumeData_DepartmentId",
                table: "WorkVolumeData",
                column: "DepartmentId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkVolumeData_EmployeeId",
                table: "WorkVolumeData",
                column: "EmployeeId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AttendanceData");

            migrationBuilder.DropTable(
                name: "ComprehensiveEvaluations");

            migrationBuilder.DropTable(
                name: "DepartmentWorkVolumeTotals");

            migrationBuilder.DropTable(
                name: "MonthlyWorkingDays");

            migrationBuilder.DropTable(
                name: "SupervisorEvaluations");

            migrationBuilder.DropTable(
                name: "WorkVolumeData");
        }
    }
}
