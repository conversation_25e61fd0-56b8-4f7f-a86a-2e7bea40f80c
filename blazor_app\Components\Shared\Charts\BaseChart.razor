@using EmployeeRatingSystem.Blazor.Components.Shared
@inherits LocalizedComponentBase
@inject IJSRuntime JSRuntime

<div class="chart-container @CssClass" style="@Style">
    <canvas id="@CanvasId" width="@Width" height="@Height"></canvas>
</div>

@code {
    /// <summary>
    /// Unique identifier for the canvas element
    /// </summary>
    [Parameter, EditorRequired] public string CanvasId { get; set; } = string.Empty;

    /// <summary>
    /// Chart title
    /// </summary>
    [Parameter] public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Chart width
    /// </summary>
    [Parameter] public int Width { get; set; } = 400;

    /// <summary>
    /// Chart height
    /// </summary>
    [Parameter] public int Height { get; set; } = 300;

    /// <summary>
    /// Additional CSS classes
    /// </summary>
    [Parameter] public string CssClass { get; set; } = string.Empty;

    /// <summary>
    /// Additional inline styles
    /// </summary>
    [Parameter] public string Style { get; set; } = string.Empty;

    /// <summary>
    /// Whether the chart is loading
    /// </summary>
    [Parameter] public bool IsLoading { get; set; } = false;

    /// <summary>
    /// Loading message
    /// </summary>
    [Parameter] public string LoadingMessage { get; set; } = "";

    /// <summary>
    /// Chart data has changed callback
    /// </summary>
    [Parameter] public EventCallback OnDataChanged { get; set; }

    protected override void OnInitialized()
    {
        if (string.IsNullOrEmpty(LoadingMessage))
        {
            LoadingMessage = L("Loading chart...", "جاري تحميل الرسم البياني...");
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await InitializeChart();
        }
    }

    /// <summary>
    /// Initialize the chart - to be overridden by derived components
    /// </summary>
    protected virtual async Task InitializeChart()
    {
        // Base implementation - override in derived components
        await Task.CompletedTask;
    }

    /// <summary>
    /// Update the chart data - to be overridden by derived components
    /// </summary>
    public virtual async Task UpdateChart()
    {
        // Base implementation - override in derived components
        await Task.CompletedTask;
    }

    /// <summary>
    /// Destroy the chart
    /// </summary>
    public async Task DestroyChart()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("destroyChart", CanvasId);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error destroying chart {CanvasId}: {ex.Message}");
        }
    }

    /// <summary>
    /// Resize the chart
    /// </summary>
    public async Task ResizeChart()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("resizeChart", CanvasId);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error resizing chart {CanvasId}: {ex.Message}");
        }
    }

    public async ValueTask DisposeAsync()
    {
        await DestroyChart();
    }
}
