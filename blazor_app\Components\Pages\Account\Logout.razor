@page "/logout"
@rendermode InteractiveServer
@using Microsoft.AspNetCore.Components.Authorization
@using EmployeeRatingSystem.Blazor.Services
@using EmployeeRatingSystem.Blazor.Components.Shared

@inherits LocalizedComponentBase
@inject IEmployeeAuthenticationService EmployeeAuthService
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider

<PageTitle>@GetPageTitle("Logout", "تسجيل الخروج")</PageTitle>

<div class="logout-page">
    <div class="container-fluid h-100">
        <div class="row h-100 justify-content-center align-items-center">
            <div class="col-md-6 col-lg-4">
                <div class="text-center">
                    @if (isLoggingOut)
                    {
                        <div class="card shadow-sm">
                            <div class="card-body p-5">
                                <div class="mb-4">
                                    <div class="spinner-border text-primary mb-3" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <h4 class="fw-bold">@L("Signing Out", "جاري تسجيل الخروج")</h4>
                                    <p class="text-muted">@L("Please wait while we sign you out...", "يرجى الانتظار بينما نقوم بتسجيل خروجك...")</p>
                                </div>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="card shadow-sm">
                            <div class="card-body p-5">
                                <div class="mb-4">
                                    <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                                    <h4 class="fw-bold">@L("Signed Out Successfully", "تم تسجيل الخروج بنجاح")</h4>
                                    <p class="text-muted">@L("You have been successfully signed out.", "تم تسجيل خروجك بنجاح.")</p>
                                </div>

                                <div class="d-grid gap-2">
                                    <a href="/login" class="btn btn-primary">
                                        <i class="fas fa-sign-in-alt @GetMarginEnd(1)"></i>
                                        @L("Sign In Again", "تسجيل الدخول مرة أخرى")
                                    </a>
                                    <a href="/" class="btn btn-outline-secondary">
                                        <i class="fas fa-home @GetMarginEnd(1)"></i>
                                        @L("Go to Home", "الذهاب للصفحة الرئيسية")
                                    </a>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private bool isLoggingOut = true;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            // Check if user is authenticated
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();

            if (authState.User.Identity?.IsAuthenticated == true)
            {
                // Sign out the user
                await EmployeeAuthService.SignOutAsync();

                // Wait a moment for the UI
                await Task.Delay(1500);
            }

            isLoggingOut = false;
            StateHasChanged();

            // Redirect to login after showing success message
            await Task.Delay(2000);
            Navigation.NavigateTo("/login");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Logout error: {ex.Message}");
            isLoggingOut = false;
            StateHasChanged();

            // Redirect to login even if there's an error
            await Task.Delay(1000);
            Navigation.NavigateTo("/login");
        }
    }
}
