using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EmployeeRatingSystem.Blazor.Models
{
    /// <summary>
    /// Represents supervisor evaluation criteria and scoring
    /// </summary>
    public class SupervisorEvaluation : TimeStampedModel
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// Employee being evaluated
        /// </summary>
        [Required]
        public string EmployeeId { get; set; } = string.Empty;

        /// <summary>
        /// Reference to the employee
        /// </summary>
        [ForeignKey(nameof(EmployeeId))]
        public virtual ApplicationUser Employee { get; set; } = null!;

        /// <summary>
        /// Supervisor conducting the evaluation
        /// </summary>
        [Required]
        public string SupervisorId { get; set; } = string.Empty;

        /// <summary>
        /// Reference to the supervisor
        /// </summary>
        [ForeignKey(nameof(SupervisorId))]
        public virtual ApplicationUser Supervisor { get; set; } = null!;

        /// <summary>
        /// Department ID
        /// </summary>
        [Required]
        public int DepartmentId { get; set; }

        /// <summary>
        /// Reference to the department
        /// </summary>
        [ForeignKey(nameof(DepartmentId))]
        public virtual Department Department { get; set; } = null!;

        /// <summary>
        /// Evaluation period (Year-Month format: 2025-01)
        /// </summary>
        [Required]
        [StringLength(7)]
        public string EvaluationPeriod { get; set; } = string.Empty;

        /// <summary>
        /// Efficiency and quality in work and ability to perform duties (0-5 points)
        /// الكفاءة والجودة فى العمل والقدرة على أداء الواجب
        /// </summary>
        [Range(0, 5)]
        public decimal EfficiencyAndQualityScore { get; set; }

        /// <summary>
        /// Leadership ability in decision-making, issuing instructions, and persuasion (0-5 points)
        /// القدرة القيادية فى إتخاذ القرارات وإصدار التعليمات وقوة الإقناع
        /// </summary>
        [Range(0, 5)]
        public decimal LeadershipAbilityScore { get; set; }

        /// <summary>
        /// Ability to plan, develop, and innovate (0-5 points)
        /// القدرة على التخطيط والتطوير والابداع
        /// </summary>
        [Range(0, 5)]
        public decimal PlanningAndInnovationScore { get; set; }

        /// <summary>
        /// Participation in teamwork and encouraging colleagues (0-5 points)
        /// المشاركة فى العمل بروح الجماعة وحث زملاءه على العمل
        /// </summary>
        [Range(0, 5)]
        public decimal TeamworkParticipationScore { get; set; }

        /// <summary>
        /// Ability to bear responsibility and work pressure (0-5 points)
        /// القدرة على تحمل المسؤولية وضغوط العمل
        /// </summary>
        [Range(0, 5)]
        public decimal ResponsibilityAndPressureScore { get; set; }

        /// <summary>
        /// Ability to deal with emergency situations (0-5 points)
        /// القدرة على التعامل مع المواقف الطارئة
        /// </summary>
        [Range(0, 5)]
        public decimal EmergencyHandlingScore { get; set; }

        /// <summary>
        /// General behavior and social relationships with others (0-5 points)
        /// السلوك العام وعلاقاته الإجتماعية بالغير
        /// </summary>
        [Range(0, 5)]
        public decimal GeneralBehaviorScore { get; set; }

        /// <summary>
        /// Relationship with superiors (0-5 points)
        /// علاقته مع رؤسائه
        /// </summary>
        [Range(0, 5)]
        public decimal RelationshipWithSuperiorsScore { get; set; }

        /// <summary>
        /// Discipline and commitment to official working hours (0-5 points)
        /// الضبط والربط والإلتزام بالدوام الرسمى
        /// </summary>
        [Range(0, 5)]
        public decimal DisciplineAndCommitmentScore { get; set; }

        /// <summary>
        /// Ability to develop work level (0-5 points)
        /// القدرة على تطوير مستواه في العمل
        /// </summary>
        [Range(0, 5)]
        public decimal WorkDevelopmentScore { get; set; }

        /// <summary>
        /// Total supervisor evaluation score (out of 50)
        /// المجموع الكلى لتقييم رئيس القسم من 50
        /// </summary>
        [NotMapped]
        public decimal TotalScore => EfficiencyAndQualityScore + LeadershipAbilityScore + PlanningAndInnovationScore +
                                   TeamworkParticipationScore + ResponsibilityAndPressureScore + EmergencyHandlingScore +
                                   GeneralBehaviorScore + RelationshipWithSuperiorsScore + DisciplineAndCommitmentScore +
                                   WorkDevelopmentScore;

        /// <summary>
        /// Supervisor evaluation percentage (Total Score / 50)
        /// </summary>
        [NotMapped]
        public decimal EvaluationPercentage => TotalScore / 50;

        /// <summary>
        /// Weighted supervisor score for final calculation (20% weight)
        /// تقيم المسئول 20%
        /// </summary>
        [NotMapped]
        public decimal WeightedScore => (TotalScore / 50) * 0.2m;

        /// <summary>
        /// Supervisor comments and feedback
        /// </summary>
        [StringLength(1000)]
        public string? Comments { get; set; }

        /// <summary>
        /// Areas for improvement
        /// </summary>
        [StringLength(1000)]
        public string? AreasForImprovement { get; set; }

        /// <summary>
        /// Strengths identified
        /// </summary>
        [StringLength(1000)]
        public string? Strengths { get; set; }

        /// <summary>
        /// Goals for next period
        /// </summary>
        [StringLength(1000)]
        public string? NextPeriodGoals { get; set; }

        /// <summary>
        /// Exceptional work performed by the employee (العمل المميز)
        /// </summary>
        [StringLength(2000)]
        public string? ExceptionalWork { get; set; }

        /// <summary>
        /// Evaluation status
        /// </summary>
        public EvaluationStatus Status { get; set; } = EvaluationStatus.DRAFT;

        /// <summary>
        /// When the evaluation was submitted
        /// </summary>
        public DateTime? SubmittedAt { get; set; }

        /// <summary>
        /// When the evaluation was approved
        /// </summary>
        public DateTime? ApprovedAt { get; set; }

        /// <summary>
        /// Who approved the evaluation
        /// </summary>
        public string? ApprovedBy { get; set; }

        /// <summary>
        /// File attachments for exceptional work documentation
        /// </summary>
        public virtual ICollection<EvaluationAttachment> Attachments { get; set; } = new List<EvaluationAttachment>();
    }

    /// <summary>
    /// Comprehensive evaluation result combining all components
    /// </summary>
    public class ComprehensiveEvaluation : TimeStampedModel
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// Employee being evaluated
        /// </summary>
        [Required]
        public string EmployeeId { get; set; } = string.Empty;

        /// <summary>
        /// Reference to the employee
        /// </summary>
        [ForeignKey(nameof(EmployeeId))]
        public virtual ApplicationUser Employee { get; set; } = null!;

        /// <summary>
        /// Department ID
        /// </summary>
        [Required]
        public int DepartmentId { get; set; }

        /// <summary>
        /// Reference to the department
        /// </summary>
        [ForeignKey(nameof(DepartmentId))]
        public virtual Department Department { get; set; } = null!;

        /// <summary>
        /// Evaluation period (Year-Month format: 2025-01)
        /// </summary>
        [Required]
        [StringLength(7)]
        public string EvaluationPeriod { get; set; } = string.Empty;

        /// <summary>
        /// Work volume percentage within department
        /// النسبة المئوية لحجم عمل الموظف داخل القسم
        /// </summary>
        [Column(TypeName = "decimal(5,4)")]
        public decimal WorkVolumePercentage { get; set; }

        /// <summary>
        /// Work volume score (60% weight)
        /// الدرجة من 60%
        /// </summary>
        [Column(TypeName = "decimal(5,4)")]
        public decimal WorkVolumeScore { get; set; }

        /// <summary>
        /// Attendance percentage
        /// نسبة حضور الموظف
        /// </summary>
        [Column(TypeName = "decimal(5,4)")]
        public decimal AttendancePercentage { get; set; }

        /// <summary>
        /// Attendance score (20% weight)
        /// نسبة حضور الموظف في الشهر 20%
        /// </summary>
        [Column(TypeName = "decimal(5,4)")]
        public decimal AttendanceScore { get; set; }

        /// <summary>
        /// Supervisor evaluation score (20% weight)
        /// تقيم المسئول 20%
        /// </summary>
        [Column(TypeName = "decimal(5,4)")]
        public decimal SupervisorScore { get; set; }

        /// <summary>
        /// Final total evaluation score
        /// مجموع التقيييم
        /// </summary>
        [Column(TypeName = "decimal(5,4)")]
        public decimal TotalScore { get; set; }

        /// <summary>
        /// Rank within department for this period
        /// </summary>
        public int? DepartmentRank { get; set; }

        /// <summary>
        /// Overall rank across all departments
        /// </summary>
        public int? OverallRank { get; set; }

        /// <summary>
        /// Evaluation status
        /// </summary>
        public EvaluationStatus Status { get; set; } = EvaluationStatus.DRAFT;

        /// <summary>
        /// When the evaluation was calculated
        /// </summary>
        public DateTime CalculatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Who calculated this evaluation
        /// </summary>
        [Required]
        public string CalculatedBy { get; set; } = string.Empty;
    }


}
