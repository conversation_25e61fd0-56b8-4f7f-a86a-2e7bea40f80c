using EmployeeRatingSystem.Blazor.Data;
using EmployeeRatingSystem.Blazor.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Components.Forms;
using System.Linq;

namespace EmployeeRatingSystem.Blazor.Services
{
    /// <summary>
    /// Service interface for Employee of the Month attachment functionality
    /// </summary>
    public interface IEmployeeOfTheMonthAttachmentService
    {
        Task<EmployeeOfTheMonthAttachment> UploadAttachmentAsync(int employeeOfTheMonthId, IBrowserFile file, string description, string descriptionAr, string uploadedBy);
        Task<List<EmployeeOfTheMonthAttachment>> GetAttachmentsAsync(int employeeOfTheMonthId);
        Task<EmployeeOfTheMonthAttachment?> GetAttachmentAsync(int attachmentId);
        Task<bool> DeleteAttachmentAsync(int attachmentId, string deletedBy);
        Task<(byte[] fileData, string fileName, string contentType)> DownloadAttachmentAsync(int attachmentId);
    }

    /// <summary>
    /// Service for managing Employee of the Month file attachments
    /// </summary>
    public class EmployeeOfTheMonthAttachmentService : IEmployeeOfTheMonthAttachmentService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<EmployeeOfTheMonthAttachmentService> _logger;
        private readonly IWebHostEnvironment _environment;
        private const long MaxFileSize = 10 * 1024 * 1024; // 10MB
        private readonly string[] AllowedExtensions = { ".pdf", ".doc", ".docx", ".jpg", ".jpeg", ".png", ".txt", ".xlsx", ".xls" };

        public EmployeeOfTheMonthAttachmentService(
            ApplicationDbContext context,
            ILogger<EmployeeOfTheMonthAttachmentService> logger,
            IWebHostEnvironment environment)
        {
            _context = context;
            _logger = logger;
            _environment = environment;
        }

        /// <summary>
        /// Upload a new attachment for Employee of the Month exceptional work documentation
        /// </summary>
        public async Task<EmployeeOfTheMonthAttachment> UploadAttachmentAsync(
            int employeeOfTheMonthId,
            IBrowserFile file,
            string description,
            string descriptionAr,
            string uploadedBy)
        {
            try
            {
                _logger.LogInformation($"Starting file upload for Employee of the Month ID {employeeOfTheMonthId}, file: {file?.Name}");

                if (file == null || file.Size == 0)
                    throw new ArgumentException("File is required");

                if (file.Size > MaxFileSize)
                    throw new ArgumentException($"File size exceeds maximum limit of {MaxFileSize / (1024 * 1024)}MB");

                var fileExtension = Path.GetExtension(file.Name).ToLowerInvariant();
                if (!AllowedExtensions.Contains(fileExtension))
                    throw new ArgumentException($"File type {fileExtension} is not allowed");

                _logger.LogInformation($"File validation passed: {file.Name}, size: {file.Size}, extension: {fileExtension}");

                // Verify Employee of the Month record exists
                var employeeOfTheMonth = await _context.EmployeeOfTheMonth
                    .Include(e => e.Employee)
                    .FirstOrDefaultAsync(e => e.Id == employeeOfTheMonthId && e.IsActive && !e.IsDeleted);
                if (employeeOfTheMonth == null)
                {
                    _logger.LogWarning($"Employee of the Month record not found for ID {employeeOfTheMonthId}. This usually means the employee is not an Employee of the Month winner.");
                    throw new ArgumentException("File uploads are only available for Employee of the Month winners. Please ensure the employee has been selected as Employee of the Month for the current period.");
                }

                _logger.LogInformation($"Employee of the Month record found: {employeeOfTheMonth.EmployeeId}");

                // Create uploads directory if it doesn't exist
                var uploadsPath = Path.Combine(_environment.WebRootPath, "uploads", "employee-of-month");
                _logger.LogInformation($"Creating uploads directory: {uploadsPath}");
                Directory.CreateDirectory(uploadsPath);

                // Generate unique filename
                var uniqueFileName = $"{Guid.NewGuid()}{fileExtension}";
                var filePath = Path.Combine(uploadsPath, uniqueFileName);
                _logger.LogInformation($"Generated file path: {filePath}");

                // Save file to disk
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.OpenReadStream(MaxFileSize).CopyToAsync(stream);
                }
                _logger.LogInformation($"File saved to disk: {filePath}");

                // Create attachment record
                var attachment = new EmployeeOfTheMonthAttachment
                {
                    EmployeeOfTheMonthId = employeeOfTheMonthId,
                    FileName = file.Name,
                    FilePath = Path.Combine("uploads", "employee-of-month", uniqueFileName),
                    FileSize = file.Size,
                    ContentType = file.ContentType,
                    DescriptionEn = description ?? string.Empty,
                    DescriptionAr = descriptionAr ?? string.Empty,
                    UploadedBy = uploadedBy,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.EmployeeOfTheMonthAttachments.Add(attachment);
                await _context.SaveChangesAsync();

                _logger.LogInformation($"Successfully uploaded attachment {file.Name} for Employee of the Month ID {employeeOfTheMonthId}");

                return attachment;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to upload attachment for Employee of the Month ID {employeeOfTheMonthId}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Get all attachments for a specific Employee of the Month record
        /// </summary>
        public async Task<List<EmployeeOfTheMonthAttachment>> GetAttachmentsAsync(int employeeOfTheMonthId)
        {
            return await _context.EmployeeOfTheMonthAttachments
                .Where(a => a.EmployeeOfTheMonthId == employeeOfTheMonthId)
                .OrderByDescending(a => a.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// Get a specific attachment by ID
        /// </summary>
        public async Task<EmployeeOfTheMonthAttachment?> GetAttachmentAsync(int attachmentId)
        {
            return await _context.EmployeeOfTheMonthAttachments
                .Include(a => a.EmployeeOfTheMonth)
                .ThenInclude(e => e.Employee)
                .FirstOrDefaultAsync(a => a.Id == attachmentId);
        }

        /// <summary>
        /// Delete an attachment
        /// </summary>
        public async Task<bool> DeleteAttachmentAsync(int attachmentId, string deletedBy)
        {
            try
            {
                var attachment = await _context.EmployeeOfTheMonthAttachments
                    .FirstOrDefaultAsync(a => a.Id == attachmentId);

                if (attachment == null)
                    return false;

                // Delete physical file
                var fullPath = Path.Combine(_environment.WebRootPath, attachment.FilePath);
                if (File.Exists(fullPath))
                {
                    File.Delete(fullPath);
                }

                // Remove from database
                _context.EmployeeOfTheMonthAttachments.Remove(attachment);
                await _context.SaveChangesAsync();

                _logger.LogInformation($"Deleted attachment {attachment.FileName} (ID: {attachmentId}) by {deletedBy}");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting attachment {attachmentId}");
                return false;
            }
        }

        /// <summary>
        /// Download an attachment file
        /// </summary>
        public async Task<(byte[] fileData, string fileName, string contentType)> DownloadAttachmentAsync(int attachmentId)
        {
            var attachment = await GetAttachmentAsync(attachmentId);
            if (attachment == null)
                throw new FileNotFoundException("Attachment not found");

            var fullPath = Path.Combine(_environment.WebRootPath, attachment.FilePath);
            if (!File.Exists(fullPath))
                throw new FileNotFoundException("Physical file not found");

            var fileData = await File.ReadAllBytesAsync(fullPath);
            return (fileData, attachment.FileName, attachment.ContentType ?? "application/octet-stream");
        }
    }
}
