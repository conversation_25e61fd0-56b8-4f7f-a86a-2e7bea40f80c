﻿// <auto-generated />
using System;
using EmployeeRatingSystem.Blazor.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace EmployeeRatingSystem.Blazor.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250817080332_AddEvaluationAttachments")]
    partial class AddEvaluationAttachments
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "8.0.0");

            modelBuilder.Entity("ApplicationUserDepartment", b =>
                {
                    b.Property<int>("ManagedDepartmentsId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ManagersId")
                        .HasColumnType("TEXT");

                    b<PERSON>("ManagedDepartmentsId", "ManagersId");

                    b.<PERSON>("ManagersId");

                    b.ToTable("UserManagedDepartments", (string)null);
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ArabicName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("INTEGER");

                    b.Property<string>("EmployeeId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("EnglishName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("INTEGER");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("TEXT");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("TEXT");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("TEXT");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("INTEGER");

                    b.Property<string>("PreferredLanguage")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(10)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("en");

                    b.Property<int?>("PrimaryDepartmentId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Role")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("TEXT");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("EmployeeId")
                        .IsUnique();

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.HasIndex("PrimaryDepartmentId");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.AttendanceData", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("AbsenceDays")
                        .HasColumnType("INTEGER");

                    b.Property<int>("AttendanceDays")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Comments")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("DepartmentId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("EarlyDepartures")
                        .HasColumnType("INTEGER");

                    b.Property<string>("EmployeeId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("EvaluationPeriod")
                        .IsRequired()
                        .HasMaxLength(7)
                        .HasColumnType("TEXT");

                    b.Property<int>("LateArrivals")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("RecordedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("RecordedBy")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("TotalWorkingDays")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("EmployeeId");

                    b.ToTable("AttendanceData");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.ComprehensiveEvaluation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("AttendancePercentage")
                        .HasColumnType("decimal(5,4)");

                    b.Property<decimal>("AttendanceScore")
                        .HasColumnType("decimal(5,4)");

                    b.Property<DateTime>("CalculatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("CalculatedBy")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("DepartmentId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("DepartmentRank")
                        .HasColumnType("INTEGER");

                    b.Property<string>("EmployeeId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("EvaluationPeriod")
                        .IsRequired()
                        .HasMaxLength(7)
                        .HasColumnType("TEXT");

                    b.Property<int?>("OverallRank")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Status")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("SupervisorScore")
                        .HasColumnType("decimal(5,4)");

                    b.Property<decimal>("TotalScore")
                        .HasColumnType("decimal(5,4)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("WorkVolumePercentage")
                        .HasColumnType("decimal(5,4)");

                    b.Property<decimal>("WorkVolumeScore")
                        .HasColumnType("decimal(5,4)");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("EmployeeId");

                    b.ToTable("ComprehensiveEvaluations");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.Department", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("DescriptionAr")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("DescriptionEn")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Level")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Lft")
                        .HasColumnType("INTEGER");

                    b.Property<string>("NameAr")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<string>("NameEn")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<int>("Order")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("ParentId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Rght")
                        .HasColumnType("INTEGER");

                    b.Property<int>("TreeId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("ParentId");

                    b.HasIndex("TreeId", "Lft");

                    b.HasIndex("TreeId", "Rght");

                    b.ToTable("Departments");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.DepartmentWorkVolumeTotal", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("DepartmentId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("EvaluationPeriod")
                        .IsRequired()
                        .HasMaxLength(7)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("RecordedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("RecordedBy")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal>("TotalDocumentedWork")
                        .HasColumnType("decimal(10,2)");

                    b.Property<decimal>("TotalOracleWork")
                        .HasColumnType("decimal(10,2)");

                    b.Property<decimal>("TotalQualityProgramWork")
                        .HasColumnType("decimal(10,2)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.ToTable("DepartmentWorkVolumeTotals");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.EmployeeOfTheMonth", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("AttendancePercentage")
                        .HasColumnType("decimal(5,4)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("DepartmentId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("EmployeeId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<string>("JustificationAr")
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT");

                    b.Property<string>("JustificationEn")
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT");

                    b.Property<string>("MonthYear")
                        .IsRequired()
                        .HasMaxLength(7)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("SelectedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("SelectedBy")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("SupervisorScore")
                        .HasColumnType("decimal(5,4)");

                    b.Property<decimal>("TotalScore")
                        .HasColumnType("decimal(5,4)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("WorkVolume")
                        .HasColumnType("decimal(10,2)");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("SelectedBy");

                    b.HasIndex("EmployeeId", "DepartmentId", "MonthYear")
                        .IsUnique();

                    b.ToTable("EmployeeOfTheMonth");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.EmployeeOfTheMonthAttachment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ContentType")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("DescriptionAr")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("DescriptionEn")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("EmployeeOfTheMonthId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<long>("FileSize")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("UploadedBy")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("EmployeeOfTheMonthId");

                    b.HasIndex("UploadedBy");

                    b.ToTable("EmployeeOfTheMonthAttachments");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.EmployeePreConfiguration", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("AssignedRole")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("EffectiveDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("EmployeeId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDepartmentManager")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDepartmentSupervisor")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<int?>("PrimaryDepartmentId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("UpdatedByUserId")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("EmployeeId")
                        .IsUnique();

                    b.HasIndex("PrimaryDepartmentId");

                    b.HasIndex("UpdatedByUserId");

                    b.ToTable("EmployeePreConfigurations");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.EmployeePreConfigurationDepartment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("DepartmentId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("EmployeePreConfigurationId")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsManagementRole")
                        .HasColumnType("INTEGER");

                    b.Property<string>("RoleInDepartment")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("EmployeePreConfigurationId", "DepartmentId")
                        .IsUnique();

                    b.ToTable("EmployeePreConfigurationDepartments");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.Evaluation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ApplicationUserId")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("ApprovedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("ApprovedById")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("EmployeeId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("EvaluationPeriodEnd")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("EvaluationPeriodStart")
                        .HasColumnType("TEXT");

                    b.Property<string>("EvaluatorId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<string>("OverallCommentsAr")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("OverallCommentsEn")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("PercentageScore")
                        .HasColumnType("decimal(5,2)");

                    b.Property<DateTime>("PeriodEnd")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("PeriodStart")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("RejectedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("RejectedById")
                        .HasColumnType("TEXT");

                    b.Property<string>("RejectionReason")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("SubmittedAt")
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("TotalScore")
                        .HasColumnType("decimal(5,2)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ApplicationUserId");

                    b.HasIndex("ApprovedById");

                    b.HasIndex("EvaluatorId");

                    b.HasIndex("RejectedById");

                    b.HasIndex("Status");

                    b.HasIndex("EmployeeId", "EvaluationPeriodStart", "EvaluationPeriodEnd");

                    b.ToTable("Evaluations");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.EvaluationAttachment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("AttachmentType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("DescriptionAr")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("DescriptionEn")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<int>("EvaluationId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<long>("FileSize")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("UploadedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("UploadedBy")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("EvaluationId");

                    b.HasIndex("UploadedBy");

                    b.ToTable("EvaluationAttachments");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.EvaluationCategory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ColorCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(7)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("#3b82f6");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("DescriptionAr")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("DescriptionEn")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("IconClass")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("fas fa-star");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsMandatory")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("MaxScore")
                        .HasColumnType("decimal(4,1)");

                    b.Property<decimal>("MinScore")
                        .HasColumnType("decimal(4,1)");

                    b.Property<string>("NameAr")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<string>("NameEn")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<int>("Order")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("WeightPercentage")
                        .HasColumnType("decimal(5,2)");

                    b.HasKey("Id");

                    b.HasIndex("Order");

                    b.ToTable("EvaluationCategories");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.EvaluationQuestion", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("CategoryId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("HelpTextAr")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("HelpTextEn")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsMandatory")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("MaxScore")
                        .HasColumnType("decimal(4,1)");

                    b.Property<decimal>("MinScore")
                        .HasColumnType("decimal(4,1)");

                    b.Property<int>("Order")
                        .HasColumnType("INTEGER");

                    b.Property<string>("TextAr")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("TextEn")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("WeightPercentage")
                        .HasColumnType("decimal(5,2)");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId", "Order");

                    b.ToTable("EvaluationQuestions");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.EvaluationResponse", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("CommentsAr")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("CommentsEn")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("EvaluationId")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<int>("QuestionId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Score")
                        .HasColumnType("decimal(4,1)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("QuestionId");

                    b.HasIndex("EvaluationId", "QuestionId")
                        .IsUnique();

                    b.ToTable("EvaluationResponses");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.MonthlyWorkingDays", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Comments")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("ConfiguredAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("ConfiguredBy")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("EvaluationPeriod")
                        .IsRequired()
                        .HasMaxLength(7)
                        .HasColumnType("TEXT");

                    b.Property<int>("Holidays")
                        .HasColumnType("INTEGER");

                    b.Property<int>("TotalWorkingDays")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("Weekends")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.ToTable("MonthlyWorkingDays");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.SupervisorEvaluation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ApprovedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("ApprovedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("AreasForImprovement")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("Comments")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("DepartmentId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("DisciplineAndCommitmentScore")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("EfficiencyAndQualityScore")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("EmergencyHandlingScore")
                        .HasColumnType("TEXT");

                    b.Property<string>("EmployeeId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("EvaluationPeriod")
                        .IsRequired()
                        .HasMaxLength(7)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("GeneralBehaviorScore")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("LeadershipAbilityScore")
                        .HasColumnType("TEXT");

                    b.Property<string>("NextPeriodGoals")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("PlanningAndInnovationScore")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("RelationshipWithSuperiorsScore")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("ResponsibilityAndPressureScore")
                        .HasColumnType("TEXT");

                    b.Property<int>("Status")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Strengths")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("SubmittedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("SupervisorId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal>("TeamworkParticipationScore")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("WorkDevelopmentScore")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("SupervisorId");

                    b.ToTable("SupervisorEvaluations");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.UserDepartment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("AssignedDate")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeactivatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("DeactivationReason")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("DepartmentId")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsPrimary")
                        .HasColumnType("INTEGER");

                    b.Property<string>("RoleInDepartment")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("UserId", "DepartmentId")
                        .IsUnique();

                    b.ToTable("UserDepartments");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.WorkVolumeData", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Comments")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("DepartmentId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("DocumentedWork")
                        .HasColumnType("decimal(10,2)");

                    b.Property<string>("EmployeeId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("EvaluationPeriod")
                        .IsRequired()
                        .HasMaxLength(7)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("OracleWork")
                        .HasColumnType("decimal(10,2)");

                    b.Property<decimal>("QualityProgramWork")
                        .HasColumnType("decimal(10,2)");

                    b.Property<DateTime>("RecordedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("RecordedBy")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("EmployeeId");

                    b.ToTable("WorkVolumeData");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ClaimType")
                        .HasColumnType("TEXT");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("TEXT");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ClaimType")
                        .HasColumnType("TEXT");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("TEXT");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("TEXT");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("TEXT");

                    b.Property<string>("RoleId")
                        .HasColumnType("TEXT");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("TEXT");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .HasColumnType("TEXT");

                    b.Property<string>("Value")
                        .HasColumnType("TEXT");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("ApplicationUserDepartment", b =>
                {
                    b.HasOne("EmployeeRatingSystem.Blazor.Models.Department", null)
                        .WithMany()
                        .HasForeignKey("ManagedDepartmentsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeRatingSystem.Blazor.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("ManagersId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.ApplicationUser", b =>
                {
                    b.HasOne("EmployeeRatingSystem.Blazor.Models.Department", "PrimaryDepartment")
                        .WithMany("PrimaryUsers")
                        .HasForeignKey("PrimaryDepartmentId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("PrimaryDepartment");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.AttendanceData", b =>
                {
                    b.HasOne("EmployeeRatingSystem.Blazor.Models.Department", "Department")
                        .WithMany()
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeRatingSystem.Blazor.Models.ApplicationUser", "Employee")
                        .WithMany()
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");

                    b.Navigation("Employee");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.ComprehensiveEvaluation", b =>
                {
                    b.HasOne("EmployeeRatingSystem.Blazor.Models.Department", "Department")
                        .WithMany()
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeRatingSystem.Blazor.Models.ApplicationUser", "Employee")
                        .WithMany()
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");

                    b.Navigation("Employee");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.Department", b =>
                {
                    b.HasOne("EmployeeRatingSystem.Blazor.Models.Department", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.DepartmentWorkVolumeTotal", b =>
                {
                    b.HasOne("EmployeeRatingSystem.Blazor.Models.Department", "Department")
                        .WithMany()
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.EmployeeOfTheMonth", b =>
                {
                    b.HasOne("EmployeeRatingSystem.Blazor.Models.Department", "Department")
                        .WithMany()
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeRatingSystem.Blazor.Models.ApplicationUser", "Employee")
                        .WithMany()
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeRatingSystem.Blazor.Models.ApplicationUser", "Selector")
                        .WithMany()
                        .HasForeignKey("SelectedBy")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");

                    b.Navigation("Employee");

                    b.Navigation("Selector");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.EmployeeOfTheMonthAttachment", b =>
                {
                    b.HasOne("EmployeeRatingSystem.Blazor.Models.EmployeeOfTheMonth", "EmployeeOfTheMonth")
                        .WithMany("Attachments")
                        .HasForeignKey("EmployeeOfTheMonthId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeRatingSystem.Blazor.Models.ApplicationUser", "Uploader")
                        .WithMany()
                        .HasForeignKey("UploadedBy")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EmployeeOfTheMonth");

                    b.Navigation("Uploader");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.EmployeePreConfiguration", b =>
                {
                    b.HasOne("EmployeeRatingSystem.Blazor.Models.ApplicationUser", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("EmployeeRatingSystem.Blazor.Models.Department", "PrimaryDepartment")
                        .WithMany()
                        .HasForeignKey("PrimaryDepartmentId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("EmployeeRatingSystem.Blazor.Models.ApplicationUser", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedByUserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("CreatedByUser");

                    b.Navigation("PrimaryDepartment");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.EmployeePreConfigurationDepartment", b =>
                {
                    b.HasOne("EmployeeRatingSystem.Blazor.Models.Department", "Department")
                        .WithMany()
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeRatingSystem.Blazor.Models.EmployeePreConfiguration", "EmployeePreConfiguration")
                        .WithMany("AdditionalDepartments")
                        .HasForeignKey("EmployeePreConfigurationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");

                    b.Navigation("EmployeePreConfiguration");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.Evaluation", b =>
                {
                    b.HasOne("EmployeeRatingSystem.Blazor.Models.ApplicationUser", null)
                        .WithMany("EmployeeEvaluations")
                        .HasForeignKey("ApplicationUserId");

                    b.HasOne("EmployeeRatingSystem.Blazor.Models.ApplicationUser", "ApprovedBy")
                        .WithMany("ApprovedEvaluations")
                        .HasForeignKey("ApprovedById")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("EmployeeRatingSystem.Blazor.Models.ApplicationUser", "Employee")
                        .WithMany("Evaluations")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeRatingSystem.Blazor.Models.ApplicationUser", "Evaluator")
                        .WithMany("ConductedEvaluations")
                        .HasForeignKey("EvaluatorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("EmployeeRatingSystem.Blazor.Models.ApplicationUser", "RejectedBy")
                        .WithMany()
                        .HasForeignKey("RejectedById")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("ApprovedBy");

                    b.Navigation("Employee");

                    b.Navigation("Evaluator");

                    b.Navigation("RejectedBy");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.EvaluationAttachment", b =>
                {
                    b.HasOne("EmployeeRatingSystem.Blazor.Models.Evaluation", "Evaluation")
                        .WithMany("Attachments")
                        .HasForeignKey("EvaluationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeRatingSystem.Blazor.Models.ApplicationUser", "Uploader")
                        .WithMany()
                        .HasForeignKey("UploadedBy")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Evaluation");

                    b.Navigation("Uploader");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.EvaluationQuestion", b =>
                {
                    b.HasOne("EmployeeRatingSystem.Blazor.Models.EvaluationCategory", "Category")
                        .WithMany("Questions")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Category");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.EvaluationResponse", b =>
                {
                    b.HasOne("EmployeeRatingSystem.Blazor.Models.Evaluation", "Evaluation")
                        .WithMany("Responses")
                        .HasForeignKey("EvaluationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeRatingSystem.Blazor.Models.EvaluationQuestion", "Question")
                        .WithMany("Responses")
                        .HasForeignKey("QuestionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Evaluation");

                    b.Navigation("Question");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.SupervisorEvaluation", b =>
                {
                    b.HasOne("EmployeeRatingSystem.Blazor.Models.Department", "Department")
                        .WithMany()
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeRatingSystem.Blazor.Models.ApplicationUser", "Employee")
                        .WithMany()
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeRatingSystem.Blazor.Models.ApplicationUser", "Supervisor")
                        .WithMany()
                        .HasForeignKey("SupervisorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");

                    b.Navigation("Employee");

                    b.Navigation("Supervisor");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.UserDepartment", b =>
                {
                    b.HasOne("EmployeeRatingSystem.Blazor.Models.Department", "Department")
                        .WithMany("UserDepartments")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeRatingSystem.Blazor.Models.ApplicationUser", "User")
                        .WithMany("UserDepartments")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");

                    b.Navigation("User");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.WorkVolumeData", b =>
                {
                    b.HasOne("EmployeeRatingSystem.Blazor.Models.Department", "Department")
                        .WithMany()
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeRatingSystem.Blazor.Models.ApplicationUser", "Employee")
                        .WithMany()
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");

                    b.Navigation("Employee");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("EmployeeRatingSystem.Blazor.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("EmployeeRatingSystem.Blazor.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeRatingSystem.Blazor.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("EmployeeRatingSystem.Blazor.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.ApplicationUser", b =>
                {
                    b.Navigation("ApprovedEvaluations");

                    b.Navigation("ConductedEvaluations");

                    b.Navigation("EmployeeEvaluations");

                    b.Navigation("Evaluations");

                    b.Navigation("UserDepartments");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.Department", b =>
                {
                    b.Navigation("Children");

                    b.Navigation("PrimaryUsers");

                    b.Navigation("UserDepartments");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.EmployeeOfTheMonth", b =>
                {
                    b.Navigation("Attachments");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.EmployeePreConfiguration", b =>
                {
                    b.Navigation("AdditionalDepartments");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.Evaluation", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("Responses");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.EvaluationCategory", b =>
                {
                    b.Navigation("Questions");
                });

            modelBuilder.Entity("EmployeeRatingSystem.Blazor.Models.EvaluationQuestion", b =>
                {
                    b.Navigation("Responses");
                });
#pragma warning restore 612, 618
        }
    }
}
