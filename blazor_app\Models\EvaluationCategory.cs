using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EmployeeRatingSystem.Blazor.Models
{
    /// <summary>
    /// Evaluation category model for configurable evaluation criteria.
    /// Equivalent to Django's EvaluationCategory model.
    /// </summary>
    public class EvaluationCategory : BaseModel
    {
        /// <summary>
        /// Category name in English
        /// </summary>
        [Required]
        [StringLength(255)]
        [Display(Name = "English Name")]
        public string NameEn { get; set; } = string.Empty;

        /// <summary>
        /// Category name in Arabic
        /// </summary>
        [Required]
        [StringLength(255)]
        [Display(Name = "Arabic Name (الاسم بالعربية)")]
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// Category description in English
        /// </summary>
        [Display(Name = "English Description")]
        public string DescriptionEn { get; set; } = string.Empty;

        /// <summary>
        /// Category description in Arabic
        /// </summary>
        [Display(Name = "Arabic Description (الوصف بالعربية)")]
        public string DescriptionAr { get; set; } = string.Empty;

        /// <summary>
        /// Weight percentage for this category in overall evaluation
        /// </summary>
        [Required]
        [Range(0.01, 100.00)]
        [Column(TypeName = "decimal(5,2)")]
        [Display(Name = "Weight Percentage")]
        public decimal WeightPercentage { get; set; }

        /// <summary>
        /// Whether this category is mandatory for all evaluations
        /// </summary>
        [Display(Name = "Is Mandatory")]
        public bool IsMandatory { get; set; } = false;

        /// <summary>
        /// Whether this category is currently active
        /// </summary>
        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Display order for this category
        /// </summary>
        [Required]
        [Display(Name = "Order")]
        public int Order { get; set; }

        /// <summary>
        /// Maximum score possible for this category
        /// </summary>
        [Required]
        [Range(1, 100)]
        [Column(TypeName = "decimal(4,1)")]
        [Display(Name = "Max Score")]
        public decimal MaxScore { get; set; } = 10.0m;

        /// <summary>
        /// Minimum score required for this category
        /// </summary>
        [Required]
        [Range(0, 100)]
        [Column(TypeName = "decimal(4,1)")]
        [Display(Name = "Min Score")]
        public decimal MinScore { get; set; } = 0.0m;

        /// <summary>
        /// Questions belonging to this category
        /// </summary>
        public virtual ICollection<EvaluationQuestion> Questions { get; set; } = new List<EvaluationQuestion>();

        /// <summary>
        /// Color code for UI display (hex color)
        /// </summary>
        [StringLength(7)]
        [Display(Name = "Color Code")]
        public string ColorCode { get; set; } = "#3b82f6";

        /// <summary>
        /// Icon class for UI display
        /// </summary>
        [StringLength(50)]
        [Display(Name = "Icon Class")]
        public string IconClass { get; set; } = "fas fa-star";

        /// <summary>
        /// Get display name based on language preference
        /// </summary>
        public string GetDisplayName(string language = "en")
        {
            return language == "ar" ? NameAr : NameEn;
        }

        /// <summary>
        /// Get description based on language preference
        /// </summary>
        public string GetDescription(string language = "en")
        {
            return language == "ar" ? DescriptionAr : DescriptionEn;
        }

        /// <summary>
        /// Calculate weighted score for a given raw score
        /// </summary>
        public decimal CalculateWeightedScore(decimal rawScore)
        {
            if (rawScore < MinScore || rawScore > MaxScore)
            {
                throw new ArgumentOutOfRangeException(nameof(rawScore), 
                    $"Score must be between {MinScore} and {MaxScore}");
            }

            // Normalize score to percentage and apply weight
            var percentage = (rawScore / MaxScore) * 100;
            return percentage * (WeightPercentage / 100);
        }

        /// <summary>
        /// Validate that the category configuration is valid
        /// </summary>
        public bool IsValid()
        {
            return WeightPercentage > 0 && 
                   WeightPercentage <= 100 && 
                   MaxScore > MinScore && 
                   MinScore >= 0 &&
                   !string.IsNullOrWhiteSpace(NameEn) &&
                   !string.IsNullOrWhiteSpace(NameAr);
        }

        /// <summary>
        /// Get the number of questions in this category
        /// </summary>
        public int GetQuestionCount()
        {
            return Questions?.Count(q => q.IsActive && !q.IsDeleted) ?? 0;
        }

        public override string ToString()
        {
            return $"{NameEn} ({WeightPercentage}%)";
        }
    }
}
